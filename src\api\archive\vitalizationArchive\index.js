import http from "@/utils/request"

export default {
   // 查询档案信息列表数据
   recordBorrowApply: function (data) {
      return http.post(
         '/archive/borrow/info/recordBorrowApply',
         data
      )
   },
   // 查询档案借阅详情列表数据
   list: function (params) {
      return http.get(
         '/archive/borrow/details/list',
         params
      )
   },
   // 根据Id获取档案借阅详情数据
   queryById: function (params) {
      return http.get(
         '/archive/borrow/details/queryById',
         params
      )
   },
   // 查询档案借阅申请信息列表数据
   borrowList: function (params) {
      return http.get(
         '/archive/borrow/info/list',
         params
      )
   },
    // 档案借阅审核信息
    auditList: function (params) {
      return http.get(
         '/archive/borrow/audit/list',
         params
      )
   },
   // 保存或更新档案借阅申请主表信息
   save: function (data) {
      return http.post(
         '/archive/borrow/info/save',
         data
      )
   },
   // 档案借阅延续申请
   borrowAuditContent: function (data) {
      return http.post(
         '/archive/borrow/info/recordContinueBorrowApply',
         data
      )
   },
   // 档案借阅催还
   borrowOfReturn: function (params) {
      return http.get(
         '/archive/borrow/info/borrowOfReturn',
		  params
      )
   },
   // 撤销借阅申请
   revokeBorrow: function (params) {
      return http.get(
         '/archive/borrow/info/revokeBorrow',
         params
      )
   },

}
