<template>
	<div>
		<el-card shadow="never" header="个人信息">
			<el-form ref="formRef" :model="form" label-width="120px" style="margin-top:20px;">
				<el-form-item label="账号" >
                    <el-input v-model="form.name" disabled/>
					<div class="el-form-item-msg">账号信息用于登录，系统不允许修改</div>
                </el-form-item>

				<el-form-item label="姓名" >
					<el-input v-model="form.loginName"></el-input>
				</el-form-item>
				<el-form-item label="性别">
					<el-select v-model="form.sex" placeholder="请选择">
						<el-option label="保密" value="0"></el-option>
						<el-option label="男" value="1"></el-option>
						<el-option label="女" value="2"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="个性签名" >
					<el-input v-model="form.about" :autosize="{minRows: 6}" maxlength="500" show-word-limit
							  type="textarea"></el-input>
				</el-form-item>
				<!-- <el-form-item>
					<el-button type="primary">保存</el-button>
				</el-form-item> -->
			</el-form>
		</el-card>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import erpUserCenter from '@/api/erp/user/erpUserCenter';

const formRef=ref()
const form = reactive({
	loginName: '',
	name: '',
	sex: '1',
	about: ''
})
function getUserInfo() {
	erpUserCenter.getUserInfo().then(res => {
		console.log(res.data.user);
		if (res.code == 200) {
            form.name = res.data.user.name
			form.loginName = res.data.user.loginName
		}
	})
}
getUserInfo()
// const store = useStore();,
/**,
* 路由对象,
*/
const route = useRoute();
/**,
* 路由实例,
*/
const router = useRouter();
//console.log('1-开始创建组件-setup'),
/**,
* 数据部分,
*/
const data = reactive({})
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount'),
})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted'),
}),
	watchEffect(() => {
	})
</script>
<style scoped lang='scss'></style>
