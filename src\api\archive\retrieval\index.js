/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2024-12-23 13:40:43
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2024-12-31 17:23:45
 * @FilePath: \archive-manage-front\src\api\archive\retrieval\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
	// 查询档案信息列表数据
	listByIndex: function (params) {
		return http.get(
			'/archive/info/main/listByIndex',
			params
		)
	},
	// 档案图谱检索
	graphListLikeName: function (params) {
		return http.get(
			'/archive/info/main/graphListLikeName',
			params
		)
	},
	// 档案图谱检索新
	graphListLikeNameNew: function (params) {
		return http.get(
			'/archive/original/info/flowGraphListLikeName',
			params
		)
	},
	// 查询档案信息列表数据
	getRelationalData: function (params) {
		return http.get(
			'/archive/file/relevance/getRelevanceFileList',
			params
		)
	},
	// 保存或更新档案借阅车
	save: function (data) {
		return http.post(
			'/archive/borrow/car/save',
			data
		)
	},
	// 查询档案借阅车列表数据
	carList: function (params) {
		return http.get(
			'/archive/borrow/car/list',
			params
		)
	},
	// 根据ID查询档案借阅车列表数据
	queryById: function (params) {
		return http.get(
			'/archive/borrow/car/queryById',
			params
		)
	},
	// 删除借阅车
	carDelete: function (params) {
		return http.delete(
			'/archive/borrow/car/delete',
			params
		)
	},
	// 档案检索
	graphListByMap: function (data) {
		return http.post(
			'/archive/original/info/graphListByMap',
			data
		)
	},
	// 档案图谱检索
	graphFileListByMap: function (data) {
		return http.post(
			'/archive/info/main/graphFileListByMap',
			data
		)
	},
	fileBatchExport: function (params) {
		return http.get(
			'/archive/info/main/fileBatchExport',
			params, { responseType: 'blob' }, true
		)
	},

}
