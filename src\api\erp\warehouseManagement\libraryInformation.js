import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/storage/erpStorehouse/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/storage/erpStorehouse/delete',ids)
  },
  save: function (params) {
    return http.post('/erp/storage/erpStorehouse/save',params)
  },
  detail: function (id) {
    return http.get('/erp/storage/erpStorehouse/queryById',id)
  },
  logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
}