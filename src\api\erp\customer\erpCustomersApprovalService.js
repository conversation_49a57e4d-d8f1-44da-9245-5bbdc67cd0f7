import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/customer/erpCustomersApproval/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/customer/erpCustomersApproval/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/customer/erpCustomersApproval/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/customer/erpCustomersApproval/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/customer/erpCustomersApproval/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/customer/erpCustomersApproval/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/customer/erpCustomersApproval/import',
      data
    )
  }
}
