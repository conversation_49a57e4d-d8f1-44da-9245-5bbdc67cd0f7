<template>
	<div class="app-container">
		<el-card class="box-card BottomStyle" style="margin:10px">
			<el-form ref="queryRef" :inline="true" :model="queryParams" class="form_130">
				<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
					<el-form-item label="标题" prop="document">
						<el-input v-model="queryParams.document" class="form_225" clearable placeholder="请输入标题"/>
					</el-form-item>
					<el-form-item label="审核节点" prop="nodeName">
						<el-input v-model="queryParams.nodeName" class="form_225" clearable
								  placeholder="请输入审核节点"/>
					</el-form-item>
					<el-form-item label="任务类型" prop="n5">
						<el-select v-model="queryParams.documentType" class="m-2" placeholder="请输入任务类型"
								   style="width: 100%;min-width:100px">
							<el-option v-for="(item, index) in auditForm" :key="index" :label="item.name"
									   :value="item.value"/>
						</el-select>
					</el-form-item>
					<el-form-item label="申请时间" prop="createDate">
						<el-date-picker v-model="queryParams.createDate" class="form_225" end-placeholder="结束时间"
										range-separator="至" start-placeholder="开始时间" type="daterange"/>
					</el-form-item>
				</TopTitle>
			</el-form>
		</el-card>
		<!-- 表格数据 -->
		<el-card class="box-card" style="margin:10px">
			<el-row :gutter="10">
				<el-col :span="1.5" style="margin-left: 10px">
					<el-select v-model="batch" placeholder="请选择审核操作" style="width: 150px">
						<el-option label="批量同意" value="1"/>
						<el-option label="批量驳回" value="2"/>
					</el-select>
					<el-button :disabled="!chooseList.length || !batch" style="margin-left: 10px" type="primary"
							   @click="() => handleAllSub()">确定
					</el-button>
				</el-col>
			</el-row>
			<el-table v-loading="loading" :data="list" border style="margin-top: 15px"
					  @selection-change="handleSelectionChange_file">
				<el-table-column align="center" type="selection" width="55"/>
				<el-table-column align="center" label="序号" prop="sort" width="80">
					<template #default="scope">
						<span>{{
								(queryParams.current - 1) * queryParams.size +
								scope.$index +
								1
							}}</span>
					</template>
				</el-table-column>

				<el-table-column align="left" label="标题" prop="document" :show-overflow-tooltip="true"
								 min-width="300"/>
				<el-table-column align="left" label="配置主流程名称" prop="auditProcess.name" min-width="160">
					<!-- <template #default="scope">
                        <span>{{ formDict(typeList, scope.row.documentType) }}</span>
                    </template> -->
				</el-table-column>
				<el-table-column align="left" label="上一节点审核人" prop="createBy.name" min-width="150"/>
				<el-table-column align="left" label="当前审核节点" prop="auditNode.nodeName" min-width="150"/>
				<el-table-column align="center" label="申请时间" min-width="130">
					<template #default="scope">
						<span>{{
								moment(scope.row.createDate).format("YYYY-MM-DD")
							}}</span>
					</template>
				</el-table-column>

				<el-table-column align="center" label="状态" min-width="100">
					<template #default="scope">
						<span>审批中</span>
					</template>
				</el-table-column>

				<el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="300px">
					<template #default="scope">
						<el-button link type="primary" @click="handleAdd(scope.row)"><img
							src="@/assets/icons/detail.png"
							style="margin-right: 5px"/>详细信息
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="float: right">
				<pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
							:total="total" @pagination="getList"/>
			</div>
		</el-card>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-model="open" :title="title" append-to-body width="600px">
			<p style="color:#333;line-height:30px;font-size:16px">
				将对一下{{
					chooseList.length
				}}个审核任务进行批量同意操作，请确定
			</p>
			<div v-for="(item, index) in chooseList" :key="index" style="display: flex;line-height:30px">
				<p style="width:40%">
					<span style="color:#333;font-size:14px;">ID：</span>
					<span style="color:#666">{{ item.id }}</span>
				</p>
				<p>
					<span style="color:#333;font-size:14px;">任务标题：</span>
					<span style="color:#666">{{ item.document }}</span>
				</p>
			</div>
			<div v-show="batch == '2' ? true : false">
				<span>驳回原因</span>
				<el-input v-model="idea" clearable placeholder="请输入驳回原因" style="width: 240px"/>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定
					</el-button>
					<el-button @click="() => (open = false)">取 消</el-button>
				</div>
			</template>
		</el-dialog>

		<compile ref="compileRef" @refresh="refresh"/>
		<sellDetails ref="childRef" @refresh="refresh"/>
	</div>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from "vue";
import sellDetails from "@/components/detailsForm/sellDetails.vue";
import sysNoticeService from "@/api/model/approve/sysNoticeService";
import moment from "moment";

const {proxy} = getCurrentInstance();
const list = ref([]);
const open = ref(false);
const compileRef = ref(null);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const batch = ref("");
const idea = ref("");
const statusList = ref([]);
const auditForm = ref([]);
const chooseList = ref([]);
const newFilArr = ref([]);
const visible = ref(false);
const detailValue = ref({});
const childRef = ref(null);
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 10,
	},
});

const {queryParams, form, rules} = toRefs(data);
const beforeClose = () => {
	visible.value = false;
	detailValue.value = {};
};
const handleSelectionChange_file = (key) => {
	chooseList.value = key;
};
const handleAllSub = () => {
	if (batch.value == "2") {
		title.value = "批量驳回";
	} else {
		title.value = "批量同意";
	}
	open.value = true;
};

/** 查询角色列表 */
function getList() {
	loading.value = true;
	list.value = []
	sysNoticeService.list(queryParams.value).then((res) => {
		if (res.code == 200) {
			list.value = res.data.records;
			total.value = res.data.total;
			loading.value = false;
		}
	});
}

const formDict = (data, val) => {
	return proxy.selectDictLabel(data, val);
};

/** 搜索按钮操作 */
function handleQuery() {
	getList();
}

const refresh = () => {
	getList();
};
const handleAdd = (row) => {
	if (row.documentType >= 4 && row.documentType <= 7) {
		compileRef.value.details(row.documentType, row.documentId, row.id);
	} else if (row.documentType == 9) {
		childRef.value.details(row.documentId, row.id);
	} else {
		visible.value = true;
		detailValue.value = row;
	}
};

/** 重置按钮操作 */
function resetQuery() {
	proxy.resetForm("queryRef");
	handleQuery();
}

function submitForm() {
	let idss = "";
	let status = "";
	if (batch.value == "2") {
		status = "11";
	} else {
		status = "30";
	}
	newFilArr.value = [];
	chooseList.value.filter((item) => {
		newFilArr.value.push(item.id);
	});
	console.log(newFilArr.value);
	idss = newFilArr.value.join(",");
	sysNoticeService
		.handbatch({ids: idss, status: status, idea: idea.value})
		.then((res) => {
			console.log(res);
			if (res.code == 200) {
				proxy.msgSuccess(`批量审批成功`);
				open.value = false;
				getList();
			} else {
				open.value = false;
				proxy.msgSuccess(res.msg);
			}
		});
}

async function dict() {
	statusList.value = await proxy.getDictList("audit_status");
	auditForm.value = await proxy.getDictList('audit_form')
}

dict();
getList();
</script>

<style lang="scss" scoped>
::v-deep .BottomStyle {
	.el-card__body {
		padding-bottom: 0
	}
}
</style>
