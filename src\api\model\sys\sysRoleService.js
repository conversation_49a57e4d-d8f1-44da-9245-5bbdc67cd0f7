import http from "@/utils/request"

/*
 *@description: 系统角色
 *@author: 路正宁
 *@date: 2023-03-17 11:59:59
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/role/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/role/delete',
       {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
       '/sys/role/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/role/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/sys/role/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/role/export',
       params,
       'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/sys/role/import',
       data
    )
  }
}
