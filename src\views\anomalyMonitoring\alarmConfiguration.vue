<template>
    <div>
        <!-- 查询表头 -->
        <el-card class="box-card Botm">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-position="right" label-width="auto">
                <el-form-item label="任务名称" prop="alarmName">
                    <el-input v-model="queryParams.alarmName" class="form_225" clearable placeholder="请输入任务名称" />
                </el-form-item>
                <el-form-item label="状态" prop="status" >
                    <el-select v-model="queryParams.status" class="form_225" clearable placeholder="请选择状态">
                        <el-option v-for=" item in statusList" :key="item.id" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuery">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表单区域 -->
        <el-card style="margin:10px;">
            <el-button class="creatSpan" type="primary" @click="handleAdd(creatForm, rulerformRef)">新增</el-button>
            <el-table v-loading="loading" :data="configeList" border class="el-table">
                <!-- <el-table-column label="ID" align="left" prop="id"/> -->
                <el-table-column align="left" label="任务名称" prop="alarmName">
                </el-table-column>
                <el-table-column align="left" label="状态" prop="status">
                    <template #default="scope">
                        {{ scope.row.status == '1' ? '生效中' : '已禁用' }}
                    </template>
                </el-table-column>
                <el-table-column :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')" align="left" label="创建日期"
                    prop="createDate">
                </el-table-column>
                <el-table-column align="left" label="创建人">
                    <template #default>
                        {{ createName }}
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="450">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row, editForm)"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button v-if="scope.row.status == '1'" link style="color:#909399"
                            @click="handleDisabled(scope.row)"><img src="@/assets/icons/disabled.png"
                                style="margin-right:5px" />禁用</el-button>
                        <el-button v-if="scope.row.status == '2'" link style="color:#909399"
                            @click="handleDisabled(scope.row)"><img src="@/assets/icons/disabled.png"
                                style="margin-right:5px" />解禁</el-button>

                        <el-button link type="primary" @click="handleCopy(scope.row)"><img src="@/assets/icons/detail.png"
                                style="margin-right:5px" />复制</el-button>
                        <el-button link style="color:#67c23a" type="danger" @click="handlerLog(scope.row)"><img
                                src="@/assets/icons/review.png" style="margin-right:5px;" />操作日志</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增弹框 -->
        <el-dialog v-model="dialogFormVisible" title="创建任务">
            <el-form ref="creatForm" :model="dialogform" :rules="rules" label-width="80px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务名称" prop="alarmName">
                            <el-input v-model="dialogform.alarmName" autocomplete="off" placeholder="请输入任务名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="有效期" prop="createDate">
                            <el-date-picker v-model="dialogform.createDate" end-placeholder="结束时间" range-separator="至"
                                start-placeholder="开始时间" type="daterange" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务类型" prop="taskType">
                            <el-select v-model="dialogform.taskType" placeholder="请选择任务类型" style="width:100%;">
                                <el-option v-for=" item in tasktypeList" :key="item.id" :label="item.name" :value="item.value"  />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="执行时间" prop="executeTime">
                            <el-time-picker v-model="dialogform.executeTime" placeholder="请选择执行时间" />
                            <!-- <el-time-picker v-model="value1" placeholder="Arbitrary time" /> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="周期" prop="weekTerm">
                            <el-select v-model="dialogform.weekTerm" placeholder="请选择周期" style="width:100%;">
                                <el-option v-for=" item  in  cycleList" :key="item.id" :label="item.name"
                                    :value="item.value" @click="handlerCycle(item)" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="weekFlag" label="星期" prop="week">
                            <el-select v-model="dialogform.week" placeholder="请选择周" style="width:100%;">
                                <el-option v-for=" item  in  cycleOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="mounthFlag" label="日期" prop="mounth">
                            <el-select v-model="dialogform.mounth" placeholder="请选择日" style="width:100%;">
                                <el-option v-for=" item  in  mounthOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-form-item class="controlValue" label="监控字段" prop="itemcount">
                    <div v-for='(item, index) in itemcount' :key="index">
                        <el-cascader v-model="controlValue[index]" :options="options" />
                        <el-button style="margin:7px 0px 0px 20px;" type="danger"
                            @click="deleteControl(index)">删除</el-button>
                        <el-button style="margin:7px 0px 0px 20px;" type="primary" @click="addControl">增加</el-button>
                        <span v-if="errors[index]" class="error">{{ errors[index] }}</span>
                    </div>
                    <!-- <el-button v-if="itemcount.length === 0" type="primary" style="margin-top: 20px;"
                        @click="addControl">增加</el-button> -->
                </el-form-item>
                <!-- 监控规则 -->
                <el-form-item class="ruler-form" label="监控规则" prop="isStop">
                    <el-form v-for='(item, index) in itemcount2' :key="index" ref="rulerformRef" v-model="rulerValue[index]"
                        class="rulerForm">
                        <div class="rulerForm">
                            <div class="rulerDiv">
                                <el-form-item>
                                    <p>条件</p>
                                </el-form-item>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="监控字段" prop="monitorName">
                                            <el-select v-model="rulerValue[index].monitorName" placeholder="请选择字段"
                                                style="margin-top: 13px;">
                                                <el-option v-for=" options  in  supervisoryList" :key="options.id"
                                                    :label="options.name" :value="options.value" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item label-width="80px" prop="currentTime">
                                            <el-radio-group v-model="rulerValue[index].currentTime" class="ml-4">
                                                <el-radio label="1" size="large">当前日期+
                                                    <el-input-number v-model="rulerValue[index].currentTimeNow" :min="1"
                                                        style="width:50%;" @change="handleChange" />
                                                    天
                                                </el-radio>
                                                <el-radio label="2" size="large">指定日期
                                                    <el-date-picker v-model="rulerValue[index].sureTime" placeholder="请选择指定日期"
                                                        size="default" style="margin-left:10px;width:53%;"
                                                        type="date" value-format="YYYY-MM-DD" />
                                                </el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="操作" prop="operation">
                                            <div style="display: flex; flex-direction: column; margin-top:18px;">
                                                <el-checkbox v-model="rulerValue[index].noticeCenter" :false-label="1"
                                                    :true-label="2" label="消息中心提醒" size="large" @change="handlerUser" />
                                                <el-checkbox v-model="rulerValue[index].alarmTask" :false-label="1"
                                                    :true-label="2" label="生成异常任务" size="large" />
                                                <el-checkbox v-model="rulerValue[index].statusAble" :false-label="1"
                                                    :true-label="2" label="状态禁用" size="large" />
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item v-if="receiverFlag" label="接收人" prop="receiverBy">
                                            <el-select v-model="rulerValue[index].receiverBy" multiple
                                                placeholder="请选择接收人" style="margin-top: 13px;">
                                                <el-option v-for=" options  in  receiverList" :key="options.id"
                                                    :label="options.name" :value="options.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>
                            <el-button class="bottomadd" type="primary" @click="addDiv">增加</el-button>
                            <el-button class="bottomadd" type="danger" @click="deleteRuler(item, index)">删除</el-button>
                        </div>
                    </el-form>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveAlarm(creatForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 编辑弹框 -->
        <el-dialog v-model="editdialogFormVisible" title="修改任务">
            <el-form ref="editForm" :model="editdialogform" :rules="editRules" label-width="80px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务名称" prop="alarmName">
                            <el-input v-model="editdialogform.alarmName" autocomplete="off" placeholder="请输入任务名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="有效期" prop="createDate">
                            <el-date-picker v-model="editdialogform.createDate" end-placeholder="结束时间" range-separator="至"
                                start-placeholder="开始时间" type="daterange" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务类型" prop="taskType">
                            <el-select v-model="editdialogform.taskType" placeholder="请选择任务类型">
                                <el-option v-for=" item in tasktypeList" :key="item.id" :label="item.name" :value="item.value"  />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="执行时间" prop="executeTime">
                            <el-time-picker v-model="editdialogform.executeTime" format=" hh:mm:ss" placeholder="请选择执行时间"
                                value-format="HH:mm:ss" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="周期" prop="weekTerm">
                            <el-select v-model="editdialogform.weekTerm" placeholder="请选择周期">
                                <el-option v-for=" item  in  cycleList" :key="item.id" :label="item.name"
                                    :value="item.value" @click="handlerCycle(item)" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="weekFlag" label="星期" prop="week">
                            <el-select v-model="editdialogform.week" placeholder="请选择周">
                                <el-option v-for=" item  in  cycleOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="mounthFlag" label="日期" prop="mounth">
                            <el-select v-model="editdialogform.mounth" placeholder="请选择日">
                                <el-option v-for=" item  in  mounthOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item class="controlValue" label="监控字段" prop="monitorValue">
                    <div v-for='(item, index) in edititemcount' :key="index">
                        <el-cascader v-model="controlValue2[index]" :options="options" :props="regionProps" />
                        <el-button style="margin:7px 0px 0px 20px;" type="danger"
                            @click="editdeleteControl(index)">删除</el-button>
                        <el-button style="margin:7px 0px 0px 20px;" type="primary" @click="addControl2">增加</el-button>
                    </div>
                </el-form-item>
                <!-- 编辑监控规则 -->
                <el-form-item class="ruler-form" label="监控规则" prop="isStop">
                    <el-form v-for='(item, index) in edititemcount2' :key="index" v-model="editRulerValue"
                        class="rulerForm">
                        <div class="rulerForm">
                            <div class="rulerDiv">
                                <el-form-item>
                                    <p>条件</p>
                                </el-form-item>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="监控字段" prop="monitorName">
                                            <el-select v-model="editRulerValue[index].monitorName" placeholder="请选择字段"
                                                style="margin-top: 13px;">
                                                <el-option v-for=" options  in  supervisoryList" :key="options.id"
                                                    :label="options.name" :value="options.value" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item label-width="80px" prop="dateOption">
                                            <el-radio-group v-model="item.dateOption" class="ml-4">
                                                <el-radio label="1" size="large">当前日期+
                                                    <el-input-number v-model="editRulerValue[index].currentTimeNow" :min="1"
                                                        style="width:50%;" @change="handleChange" />
                                                    天
                                                </el-radio>
                                                <el-radio label="2" size="large">指定日期
                                                    <el-date-picker v-model="editRulerValue[index].sureTime" placeholder="请选择指定日期"
                                                        size="default" style="margin-left:10px;width:53%;"
                                                        type="date" value-format="YYYY-MM-DD" />
                                                </el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="操作" prop="operation">
                                    <div style="display: flex; flex-direction: column; margin-top:18px;">
                                        <el-checkbox v-model="editRulerValue[index].noticeCenter" :false-label="1"
                                            :true-label="2" label="消息中心提醒" size="large" />
                                        <el-checkbox v-model="editRulerValue[index].alarmTask" :false-label="1" :true-label="2"
                                            label="生成异常任务" size="large" />
                                        <el-checkbox v-model="editRulerValue[index].statusAble" :false-label="1" :true-label="2"
                                            label="状态禁用" size="large" />
                                    </div>
                                </el-form-item>
                            </div>
                            <el-button class="bottomadd" type="primary" @click="addDiv2">增加</el-button>
                            <el-button class="bottomadd" type="danger" @click="deleteRuler2(item, index)">删除</el-button>
                        </div>
                    </el-form>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editdialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="editAlarm(editForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <logList v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" :reviewVisible="reviewVisible" />
    </div>
</template>

<script setup>
import {getCurrentInstance, reactive, ref} from 'vue';
import alarm from "@/api/erp/alarm/alarm";
import {ElMessage} from "element-plus";
import moment from 'moment';
import logList from './logList.vue'

const { proxy } = getCurrentInstance();
const statusList = ref([])
const reviewRow = ref({})
const errors = ref([])
const rulerformRef = ref()
const supervisoryList = ref([])
const reviewVisible = ref(false)
const receiverFlag = ref(false)
const createName = ref('')
const tasktypeList = ref([])
const cycleList = ref([])
const queryForm = ref()
const creatForm = ref()
const editForm = ref()
const total = ref(0)
const regionProps = ref({
    value: 'value',
    label: 'label',
})
const queryParams = ref({
    current: 1,
    size: 10,
})
const configeList = ref([])
const dialogform = reactive([])
const editdialogform = reactive([])
const weekFlag = ref(false)
const editdialogFormVisible = ref(false)
const mounthFlag = ref(false)
const itemcount = ref(1)
const edititemcount = ref(1)
// const edititemcount2 = ref(1)
const rules = reactive({
    alarmName: [{ required: true, message: '请输入任务名称', trigger: 'blur' },],
    createDate: [{ required: true, message: '请选择有效期', trigger: 'blur' },],
    taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' },],
    executeTime: [{ required: true, message: '请选择执行时间', trigger: 'blur' },],
    weekTerm: [{ required: true, message: '请选择周期', trigger: 'blur' },],
    week: [{ required: true, message: '请选择星期', trigger: 'blur' },],
    mounth: [{ required: true, message: '请选择日', trigger: 'blur' },],
    // itemcount: [{ itemcount: 'array', required: true, message: '请选择监控字段', trigger: 'blur' },],
    // isStop: [{required: true, message: '请填写监控规则', trigger: 'blur' },],
})
const editRules = reactive({
    alarmName: [{ required: true, message: '请输入任务名称', trigger: 'blur' },],
    createDate: [{ required: true, message: '请选择有效期', trigger: 'blur' },],
    taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' },],
    executeTime: [{ required: true, message: '请选择执行时间', trigger: 'blur' },],
    weekTerm: [{ required: true, message: '请选择周期', trigger: 'blur' },],
    week: [{ required: true, message: '请选择星期', trigger: 'blur' },],
    mounth: [{ required: true, message: '请选择日', trigger: 'blur' },],
    // monitorValue: [{ required: true, message: '请选择监控字段', trigger: 'blur' },],
})
// const itemcount2 = ref(1)
const itemcount2 = ref([{
    monitorName: '',
    dateOption: '1',
    num: '',
    sureTime: '',
    noticeCenter: '',
    alarmTask: '',
    statusAble: ''
}])
const edititemcount2 = ref([{
    monitorName: '',
    dateOption: '1',
    num: '',
    sureTime: '',
    noticeCenter: '',
    alarmTask: '',
    statusAble: ''
}])
const controlValue = ref([])
const receiverList = ref([])
const controlValue2 = ref([])
const rulerValue = ref([{}])
const editRulerValue = ref([{}])
const loading = ref(false);
const ids = ref('')
const dialogFormVisible = ref(false)
const options = [
    {   id:'5',
        value: '1',
        label: '生产厂家',
        children: [
            {   id:'9',
                value: '1',
                label: '基本信息',
                children: [
                    {   id:'9',
                        value: '1',
                        label: '营业执照有效期',
                    },
                ],
            },
            {   id:'10',
                value: '2',
                label: '生产许可证',
                children: [
                    {   id:'10',
                        value: '2',
                        label: '有限期',
                    },
                ],
            },
        ]
    },
    {
        value: '2',
        label: '商品-药品',
        children: [
            {
                value: '3',
                label: '质量信息',
                children: [
                    {
                        value: '3',
                        label: '批准文号有限期',
                    },
                ],
            },
            {
                value: '4',
                label: '商品附件',
                children: [
                    {
                        value: '4',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '3',
        label: '商品-器械',
        children: [
            {
                value: '5',
                label: '质量信息',
                children: [
                    {
                        value: '5',
                        label: '注册证有限期',
                    },
                ],
            },
            {
                value: '6',
                label: '商品附件',
                children: [
                    {
                        value: '6',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '4',
        label: '商品-消杀',
        children: [
            {
                value: '7',
                label: '质量信息',
                children: [
                    {
                        value: '7',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '8',
                label: '商品附件',
                children: [
                    {
                        value: '8',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '5',
        label: '商品-食品',
        children: [
            {
                value: '9',
                label: '质量信息',
                children: [
                    {
                        value: '9',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '10',
                label: '商品附件',
                children: [
                    {
                        value: '10',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '6',
        label: '供应商',
        children: [
            {
                value: '11',
                label: '基本信息',
                children: [
                    {
                        value: '11',
                        label: '营业期限',
                    },
                ],
            },
            {
                value: '12',
                label: '质量信息',
                children: [
                    {
                        value: '12',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '13',
                label: '委托书',
                children: [
                    {
                        value: '13',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '14',
                label: '质保协议',
                children: [
                    {
                        value: '14',
                        label: '有限期',
                    },
                ],
            },
            {
                value: '15',
                label: '供应商附件',
                children: [
                    {
                        value: '15',
                        label: '有限期',
                    },
                ],
            },
        ]
    }, {
        value: '7',
        label: '客户',
        children: [
            {
                value: '16',
                label: '基本信息',
                children: [
                    {
                        value: '16',
                        label: '营业期限',
                    },
                ],
            },
            {
                value: '17',
                label: '许可证',
                children: [
                    {
                        value: '17',
                        label: '有限期',
                    },
                ],
            },
            {
                value: '18',
                label: '执业许可证',
                children: [
                    {
                        value: '18',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '19',
                label: '委托书',
                children: [
                    {
                        value: '19',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '20',
                label: '质保协议',
                children: [
                    {
                        value: '20',
                        label: '有限期',
                    },
                ],
            },
            {
                value: '21',
                label: '客户附件',
                children: [
                    {
                        value: '21',
                        label: '有限期',
                    },
                ],
            },
        ]
    },

]
const cycleOptions = [
    {
        value: 'MON',
        label: '周一',
    },
    {
        value: 'TUE',
        label: '周二',
    },
    {
        value: 'WED',
        label: '周三',
    },
    {
        value: 'THU',
        label: '周四',
    },
    {
        value: 'FRI',
        label: '周五',
    },
    {
        value: 'SAT',
        label: '周六',
    },
    {
        value: 'SUN',
        label: '周日',
    },
]
const mounthOptions = [
    {
        value: '1',
        label: '1日',
    },
    {
        value: '2',
        label: '2日',
    },
    {
        value: '3',
        label: '3日',
    },
    {
        value: '4',
        label: '4日',
    },
    {
        value: '5',
        label: '5日',
    },
    {
        value: '6',
        label: '6日',
    },
    {
        value: '7',
        label: '7日',
    },
    {
        value: '8',
        label: '8日',
    },
    {
        value: '9',
        label: '9日',
    },

    {
        value: '10',
        label: '10日',
    },
    {
        value: '11',
        label: '11日',
    },
    {
        value: '12',
        label: '12日',
    },
    {
        value: '13',
        label: '13日',
    },
    {
        value: '14',
        label: '14日',
    },
    {
        value: '15',
        label: '15日',
    },
    {
        value: '16',
        label: '16日',
    },
    {
        value: '17',
        label: '17日',
    },
    {
        value: '18',
        label: '18日',
    },
    {
        value: '19',
        label: '19日',
    },
    {
        value: '20',
        label: '20日',
    },
    {
        value: '21',
        label: '21日',
    },
    {
        value: '22',
        label: '22日',
    },
    {
        value: '23',
        label: '23日',
    },
    {
        value: '24',
        label: '24日',
    },
    {
        value: '25',
        label: '25日',
    },
    {
        value: '26',
        label: '26日',
    },
    {
        value: '27',
        label: '27日',
    },
    {
        value: '28',
        label: '28日',
    },
    {
        value: '29',
        label: '29日',
    },
    {
        value: '30',
        label: '30日',
    },
    {
        value: '31',
        label: '31日',
    },


]
//计算日期差值
function DateDiff(Date_end, Date_start) {
    let aDate, oDate1, oDate2, iDays;
    Date_end = Date_end.split(" ");
    aDate = Date_end[0].split("-");
    oDate1 = new Date(aDate[0], aDate[1], aDate[2]);
    Date_start = Date_start.split(" ");
    aDate = Date_start[0].split("-");
    oDate2 = new Date(aDate[0], aDate[1], aDate[2]);
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24);
    return iDays;
}
const handlerUser = () => {
    receiverFlag.value = !receiverFlag.value
    var Organization = JSON.parse(localStorage.getItem("Organization"))
    var orgKey = JSON.parse(localStorage.getItem("orgKey"))
    // console.log(Organization,orgKey);
    var num = orgKey.content
    var userList = Organization.content
    var userId = userList[num].id
    alarm.getUser({ 'sysOrg.id': userId }).then(res => {
        if (res.code == 200) {
            receiverList.value = res.data.records
        }
    })


}
// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
// 编辑按钮
const handleEdit = (row) => {
    editdialogFormVisible.value = true
    weekFlag.value = false
    mounthFlag.value = false
    ids.value = row.id
    alarm.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.alarmName = res.data.alarmName
            editdialogform.taskType = res.data.taskType
            editdialogform.weekTerm = res.data.weekTerm
            if (res.data.weekTerm == '2') {
                weekFlag.value = true

            }
            let tmpArr = []
            tmpArr.push(new Date(res.data.validBeginTime))
            tmpArr.push(new Date(res.data.validEndTime))
            editdialogform.createDate = tmpArr
            editdialogform.executeTime = res.data.executeTime
            // edititemcount.value = '15'
            // controlValue2.value = '15'
            // edititemcount.value = [['5','9','9'],['5','10','10']]
            // controlValue2.value = [['5','9','9'],['5','10','10']]
            // '5','9','9'
            // ["5,9 ,9","3,3,5"]
            // res.data.monitorValue.split(',').forEach((item) => {
            //     console.log(item);
            //     // edititemcount.value = item
            //     // controlValue2.value = item
            // })
            res.data.monitorRuleDTOS.map(item => {
                function diff() {
                    var res1 = moment(item.currentTimeNow).format('YYYY-MM-DD');
                    var res2 = moment(new Date()).format('YYYY-MM-DD');
                    item.currentTimeNow = DateDiff(res1, res2)
                    item.noticeCenter = item.noticeCenter == '' ? 1 : 2
                    item.alarmTask = item.alarmTask == '' ? 1 : 2
                    item.statusAble = item.statusAble == '' ? 1 : 2
                    return item.currentTimeNow, item.noticeCenter, item.alarmTask, item.disable
                }
                return diff()
            })
            editRulerValue.value = []
            edititemcount2.value = []
            res.data.monitorRuleDTOS.forEach((item) => {
                editRulerValue.value.push(item)
                edititemcount2.value.push(item)
            })
        }
    })
}
//编辑请求
const editAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var validBeginTime = moment(editdialogform.createDate[0]).format('YYYY-MM-DD')
            var validEndTime = moment(editdialogform.createDate[1]).format('YYYY-MM-DD')
			editRulerValue.value._rawValue.map(obj => {
                function calculateNewDate() {
                    var now = new Date(); // 获取当前时间
                    now.setDate(now.getDate() + obj.currentTimeNow); // 将用户输入的天数加到当前日期上
                    var year = now.getFullYear(); // 年份
                    var month = now.getMonth() + 1; // 月份（0-11）
                    var date = now.getDate(); // 天数（1到31）
                    obj.currentTimeNow = year + "-" + month + "-" + date
                    obj.currentTimeNow = moment(obj.currentTimeNow).format('YYYY-MM-DD')
                    return obj.currentTimeNow; // 返回新日期字符串
                }
                obj.sureTime = moment(obj.sureTime).format('YYYY-MM-DD')
                obj.noticeCenter = obj.noticeCenter.toString()
                obj.alarmTask = obj.alarmTask.toString()
                obj.statusAble = obj.statusAble.toString()
                return calculateNewDate(), obj.sureTime, obj.noticeCenter, obj.alarmTask, obj.statusAble
            });
            var params = {
                id: ids.value,
                alarmName: editdialogform.alarmName,
                validBeginTime: validBeginTime,
                validEndTime: validEndTime,
                taskType: editdialogform.taskType,
                executeTime: editdialogform.executeTime,
                weekTerm: editdialogform.weekTerm,
                weekTermValue: editdialogform.week || editdialogform.mounth,
				monitorRuleDTOS: editRulerValue.value._rawValue
            }
            params.monitorValue = controlValue2.value
            alarm.save(params).then(res => {
                if (res.code == 200) {
                    if (res.code == 200) {
                        ElMessage({
                            message: "修改成功",
                            type: "success",
                        });
                        editdialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "修改失败，请稍后重试",
                        });
                    }
                }
            })
        }
    });
}
//新增报警配置
const saveAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var validBeginTime = moment(dialogform.createDate[0]).format('YYYY-MM-DD')
            var validEndTime = moment(dialogform.createDate[1]).format('YYYY-MM-DD')
            function formatTime(dateString) {
                var date = new Date(dateString);
                var hour = ('0' + date.getHours()).slice(-2);
                var minute = ('0' + date.getMinutes()).slice(-2);
                var second = ('0' + date.getSeconds()).slice(-2);
                return hour + ':' + minute + ':' + second;
            }
            var executeTime = formatTime(dialogform.executeTime)
            rulerValue.value.map(obj => {
                // console.log(obj.receiverBy.toString());
                function user() {
                    obj.receiverBy = obj.receiverBy.toString()
                    return obj.receiverBy
                }
                function calculateNewDate() {
                    var now = new Date(); // 获取当前时间
                    now.setDate(now.getDate() + obj.currentTimeNow); // 将用户输入的天数加到当前日期上
                    var year = now.getFullYear(); // 年份
                    var month = now.getMonth() + 1; // 月份（0-11）
                    var date = now.getDate(); // 天数（1到31）
                    // console.log(year + "-" + month + "-" + date);
                    obj.currentTimeNow = year + "-" + month + "-" + date
                    obj.currentTimeNow = moment(obj.currentTimeNow).format('YYYY-MM-DD')
                    return obj.currentTimeNow; // 返回新日期字符串
                }
                return calculateNewDate(), user()
            });
            var params = {
                alarmName: dialogform.alarmName,
                validBeginTime: validBeginTime,
                validEndTime: validEndTime,
                taskType: dialogform.taskType,
                executeTime: executeTime,
                weekTerm: dialogform.weekTerm,
                // receiverBy:
                weekTermValue: dialogform.week || dialogform.mounth,
                monitorRuleDTOS: rulerValue.value
            }
            const arr = []
            controlValue.value.forEach(v => {
                arr.push( v.toString())
            })
            // result = JSON.stringify()
            console.log( arr);
            // params.monitorValue = arr
            // console.log(params);
            // alarm.save(params).then(res => {
            //     if (res.code == 200) {
            //         if (res.code == 200) {
            //             ElMessage({
            //                 message: "保存成功",
            //                 type: "success",
            //             });
            //             dialogFormVisible.value = false
            //             getList()
            //         } else {
            //             ElMessage({
            //                 type: "error",
            //                 message: "添加失败，请稍后重试",
            //             });
            //         }
            //     }
            // })
        }
    });
}

//复制按钮
const handleCopy = (row) => {
    editdialogFormVisible.value = true
    // alarm.detail({ id: row.id }).then(res => {
    //     if (res.code == 200) {
    //         editdialogform.alarmName = res.data.alarmName
    //         editdialogform.taskType = res.data.taskType
    //         editdialogform.weekTerm = res.data.weekTerm
    //         controlValue2.value = res.data.monitorValue
    //     }
    // })
}
//禁用和解禁
const handleDisabled = (row) => {
    var params = {
        id: row.id,
        status: row.status == '1' ? '2' : '1'
    }
    alarm.updateStatus(params).then(res => {
        if (res.code == 200) {
            proxy.msgSuccess(row.status == '2' ? '解禁成功' : '禁用成功')
            getList()
        }
    })
}
// 重置
function resetQuery(formEl) {
    formEl.resetFields()
    getList()
}
//新增报警配置的按钮
const handleAdd = (formEl, formEl2) => {
    dialogFormVisible.value = true
    weekFlag.value = false
    mounthFlag.value = false
    controlValue.value = []
    itemcount.value = 1
    formEl.resetFields()
    formEl2.resetFields()
}
// 增加监控字段
const addControl = () => {
    itemcount.value += 1;
    controlValue.value.push('');
}
//编辑增加监控字段
const addControl2 = () => {
    edititemcount.value += 1;
    controlValue2.value.push('');
}
const controlArr = ref([])
const controlArr2 = ref([])
// 删除监控字段
const deleteControl = index => {
    const arr = [...controlArr.value]
    arr.splice(index, 1)
    controlArr.value = [...arr]
    if (itemcount.value > 1) {
        controlValue.value.splice(index, 1);
        itemcount.value -= 1;
    }
};
//编辑删除监控字段
const editdeleteControl = index => {
    const arr = [...controlArr2.value]
    arr.splice(index, 1)
    controlArr2.value = [...arr]
    if (edititemcount.value > 1) {
        controlValue2.value.splice(index, 1);
        edititemcount.value -= 1;
    }
};

// 保存监控字段
// 增加规则
const addDiv = () => {
    rulerValue.value.push({
        // supervisory: '',
        // dateOption: '1',
        // num: '',
        // specifiedDate: '',
        // reminder: '',
        // exceptionTask: '',
        // disable: ''
    })
    itemcount2.value.push(
        {
            supervisory: '',
            dateOption: '1',
            num: '',
            specifiedDate: '',
            reminder: '',
            exceptionTask: '',
            disable: ''
        }
    )
    // console.log(itemcount2.value);
}
//编辑增加规则
const addDiv2 = () => {
    editRulerValue.value.push({})
    edititemcount2.value.push(
        {
            monitorName: '',
            num: '',
            sureTime: '',
            noticeCenter: '',
            alarmTask: '',
            statusAble: ''
        }
    )
}
//删除规则
const deleteRuler = (item, index) => {
	if (itemcount2.value._rawValue.length > 1) {
        itemcount2.value.splice(index, 1);
    }
};
// 编辑删除规则
const deleteRuler2 = (item, index) => {
	if (edititemcount2.value._rawValue.length > 1) {
        edititemcount2.value.splice(index, 1);
    }
};
//搜索
const searchQuery = () => {
    getList()
}
//报警配置列表
const getList = () => {
    loading.value = true
    const params = {
        ...queryParams.value,
    }
    alarm.list(params).then(res => {
        if (res.code == 200) {
            configeList.value = res.data.records
            res.data.records.forEach(item => {
                createName.value = item.createBy.name
            });
            total.value = res.data.total
        }
    }).finally(()=>{
        loading.value = false
    })
}
//周期
const handlerCycle = (item) => {
    if (item.value == 1) {
        weekFlag.value = false
        mounthFlag.value = false
    } else if (item.value == 2) {
        weekFlag.value = true
        mounthFlag.value = false
    } else if (item.value == 3) {
        weekFlag.value = false
        mounthFlag.value = true
    }
}
//字典请求
async function dict() {
    statusList.value = await proxy.getDictList('alarm_state')
    tasktypeList.value = await proxy.getDictList('task_type')
    cycleList.value = await proxy.getDictList('cycle')
    supervisoryList.value = await proxy.getDictList('supervisory')
}
dict()
getList()
</script>

<style lang='scss'  scoped>
::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.rulerDiv {
    border: 1px solid transparent;
    background: linear-gradient(rgb(247, 247, 247), rgb(250, 250, 250)) padding-box,
        repeating-linear-gradient(-45deg, #ccc 0, #ccc 0.5em, white 0, white 0.75em);
    padding: 10px;
    width: 80%;
    margin-top: 20px;
    // border: 1px dashed #000;
    // border-width: 5px;
    // background-color: #4e2222;
}

::v-deep .ruler-datepicker .el-date-editor.el-input {
    margin-left: 10px;
    width: 150px;
}

::v-deep .rulerInput .el-input__wrapper {
    margin-top: 20px;
}

.rulerForm {
    display: flex;
}

.bottomadd {
    // margin:20px 0px 0px 10px
    position: relative;
    top: 20px;
    left: 10px
}

::v-deep .el-cascader .el-input {
    width: 420px;
    margin-top: 10px;
}

::v-deep .ruler-form .el-form-item__label {
    margin-top: 20px;
}

::v-deep .controlValue .el-form-item__label {
    margin-top: 10px;
}

.el-table {
    margin-top: 20px;
}
</style>
