import http from "@/utils/request"

export default {
  //盘点计划
	//盘点计划列表
  list: function (params) {
    return http.get('/archive/warehouse/checkPlan/list', params)
  },
	//保存盘点计划
  save: function (params) {
    return http.post('/archive/warehouse/checkPlan/save', params)
  },
  //删除盘点计划
  delete: function (ids) {
    return http.delete('/archive/warehouse/checkPlan/delete', ids)
  },
  //盘点计划详情
  detail: function (ids) {
    return http.get('/archive/warehouse/checkPlan/queryById', ids)
  },
  //查询当前机构用户列表
  orgList: function (params) {
    return http.get('/sys/user/list', params)
  },

  //盘点记录
  //盘点记录列表
  recordList: function (params) {
    return http.get('/archive/warehouse/checkPlanHistory/list', params)
  },
  //保存盘点记录表单
  recordSave: function (params) {
    return http.post('/archive/warehouse/checkPlanHistory/save', params)
  },
  //保存盘点记录表格部分
  recordTableSave: function (params) {
    return http.post('/archive/warehouse/checkPlanDetails/saveBatch', params)
  },
  //删除盘点记录
  recordDelete: function (ids) {
    return http.delete('/archive/warehouse/checkPlanHistory/delete', ids)
  },
  //盘点记录详情
  recordDetail: function (ids) {
	  return http.get('/archive/warehouse/checkPlanDetails/list', ids)
  },

	//修复记录
  //档号
  numberList: function (ids) {
    return http.get('/archive/info/main/list', ids)
  },
  //修复记录列表
  repairList: function (params) {
    return http.get('/archive/warehouse/replenish/list', params)
  },
  //保存修复记录
  repairSave: function (params) {
    return http.post('/archive/warehouse/replenish/save', params)
  },
  //删除修复记录
  repairDelete: function (ids) {
    return http.delete('/archive/warehouse/replenish/delete', ids)
  },
}
