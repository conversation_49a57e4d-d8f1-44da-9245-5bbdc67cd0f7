<template>
	<el-container>
		<el-aside style="border-right: 0;padding: 10px 0 10px 10px;background-color: #fff" width="21%">
			<el-card :body-style="{ padding: '8px 0 0 0', height: '100%', width: '100%' }"
					 style="height: 100%;width: 100%;">
				<tree/>
			</el-card>
		</el-aside>
		<el-container style="background-color: rgb(255, 255, 255);margin-top: -4px;">
			<el-main ref="main">
				<el-card body-style="padding-bottom: 0;" class="box-card">
					<el-tabs v-model="activeName">
						<el-tab-pane label="出库记录" name="1">
							<el-button icon="Plus" plain size="default" type="primary" @click="movelAdd(moveform)">
								新增
							</el-button>
							<el-table v-loading="loading" :data="moveList"
									  border style="margin-top:10px;">
								<el-table-column align="center" label="序号" width="60">
									<template #default="scope">
										{{ scope.$index + 1 }}
									</template>
								</el-table-column>
								<el-table-column align="center" label="档号" prop="info.num" width="250"/>
								<el-table-column align="center" label="文件名称" min-width="190" prop="info.name" show-overflow-tooltip/>
								<el-table-column align="center" label="出库日期" min-width="110" prop="outInDate" show-overflow-tooltip/>
								<el-table-column align="center" label="出库时间" min-width="100" prop="outInTime" show-overflow-tooltip/>
								<el-table-column align="center" label="状态" prop="outInStatus">
									<template #default="scope">
										{{ scope.row.outInStatus == '1' ? '已出库' : '未出库' }}
									</template>
								</el-table-column>
								<el-table-column :formatter="(row) => formDict(reasonList, row.outInRemark)" align="center" label="出库原因"
												 prop="outInRemark">
								</el-table-column>
								<el-table-column align="center" fixed="right" label="操作" min-width="240" prop="boxYear">
									<template #default="scope">
										<el-button icon="View" link size="small" type="primary" @click="moveDetail(scope.row)">查看</el-button>
										<el-button icon="Edit" link size="small" type="primary" @click="moveleEdit(scope.row, moveeditform)">编辑</el-button>
										<!-- <el-button icon="Upload" link size="small" type="primary" @click="moveLibrary(scope.row)">出库</el-button> -->
										<el-button icon="Delete" link size="small" type="danger" @click="moveDelete(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div style="float: right;">
								<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
											style="padding: 22px" @pagination="getList"/>
							</div>
						</el-tab-pane>

						<el-tab-pane label="入库记录" name="2">
							<el-button icon="Plus" plain size="default" type="primary" @click="enterAdd(enterform)">新增</el-button>
							<el-table v-loading="loading" :data="enterList" border style="margin-top:10px;"
									>
								<el-table-column align="center" label="序号" width="60">
									<template #default="scope">
										{{ scope.$index + 1 }}
									</template>
								</el-table-column>
								<el-table-column align="center" label="档号" prop="info.num" width="250"/>
								<el-table-column align="center" label="文件名称" min-width="190" prop="info.name" show-overflow-tooltip/>
								<el-table-column align="center" label="入库日期" min-width="110" prop="putInDate" show-overflow-tooltip/>
								<el-table-column align="center" label="入库时间" min-width="100" prop="putInTime" show-overflow-tooltip/>
								<el-table-column align="center" label="文件情况" min-width="190" prop="putInFileCondition" show-overflow-tooltip/>
								<el-table-column :formatter="(row) => formDict(reasonList, row.putInRemark)" align="center" label="入库原因"
												 prop="putInRemark">
								</el-table-column>
								<el-table-column align="center" fixed="right" label="操作" min-width="240" prop="boxYear">
									<template #default="scope">
										<el-button icon="View" link size="small" type="primary" @click="enterDetail(scope.row)"> 查看</el-button>
										<el-button icon="Edit" link size="small" type="primary" @click="enterEdit(scope.row, editenterform)"> 编辑 </el-button>
										<!-- <el-button icon="Download" link size="small" type="primary" @click="handleEdit(scope.row)">入库</el-button> -->
										<el-button icon="Delete" link size="small" type="danger" @click="enterDelete(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div style="float: right;">
								<pagination v-model:limit="queryParams2.size" v-model:page="queryParams2.current"
											:total="entertotal"
											style="padding: 22px" @pagination="getenterList"/>
							</div>
						</el-tab-pane>

						<el-tab-pane label="上架记录" name="3">
							<el-table v-loading="loading" :data="upList"
									  border style="margin-top:10px;" @row-click="rowClick">
								<el-table-column align="center" label="档案盒名称" prop="box.boxName" width="190"/>
								<el-table-column align="center" label="保管期限" prop="upYear">
									<template #default="scope">
										{{ scope.row.upYear ? scope.row.upYear + '年' : '永久' }}
									</template>
								</el-table-column>
								<el-table-column align="center" label="上架时间" prop="upTime"/>
								<el-table-column align="center" label="上架货位号" prop="storage.storageName"/>
								<el-table-column align="center" label="上架人" prop="upPerson.name" width="100"/>
								<el-table-column align="center" fixed="right" label="操作" min-width="70">
									<template #default="scope">
										<el-button icon="View" link size="small" type="primary" @click.stop="upDetail(scope.row)">
											查看
										</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div style="float: right;">
								<pagination v-model:limit="upqueryParams.size" v-model:page="upqueryParams.current" :total="uptotal"
											style="padding: 22px" @pagination="upRecords"/>
							</div>
						</el-tab-pane>

						<el-tab-pane label="下架记录" name="4">
							<el-table v-loading="loading" :data="downList"
									  border style="margin-top:10px;"
									  @row-click="rowClick">
								<el-table-column align="center" label="档案盒名称" prop="box.boxName" width="190"/>
								<el-table-column align="center" label="保管期限" prop="downYear">
									<template #default="scope">
										{{ scope.row.downYear ? scope.row.downYear + '年' : '永久' }}
									</template>
								</el-table-column>
								<el-table-column align="center" label="下架时间" prop="downTime"/>
								<el-table-column align="center" label="下架货位号" prop="storage.storageName"/>
								<el-table-column align="center" label="下架人" prop="downPerson.name" width="100"/>
								<el-table-column align="center" fixed="right" label="操作" min-width="70">
									<template #default="scope">
										<el-button icon="View" link size="small" type="primary" @click.stop="downDetail(scope.row)">
											查看
										</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div style="float: right;">
								<pagination v-model:limit="downqueryParams.size" v-model:page="downqueryParams.current"
											:total="downtotal"
											style="padding: 22px" @pagination="downRecords"/>
							</div>
						</el-tab-pane>

					</el-tabs>
				</el-card>
			</el-main>
		</el-container>
	</el-container>
	<!-- 档案详情 -->
	<el-dialog v-model="archivesVisible" title="档案详情" width="35%">
		<el-table :data="archivesData" border stripe>
			<el-table-column align="center" label="档号" prop="num"/>
			<el-table-column align="center" label="档案名称" prop="name"/>
		</el-table>
		<div style="margin-left:25%;">
			<pagination v-model:limit="archivesqueryParams.size" v-model:page="archivesqueryParams.current"
						:total="archivestotal" size="small" @pagination="rowClick"/>
		</div>
	</el-dialog>
	<!-- 出库记录增加弹框 -->
	<el-dialog v-model="moveFormVisible" title="新增出库记录" width="30%">
		<el-form ref="moveform" :model="moveForm" :rules="moverules" label-position="right" label-width="100px"
				 size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档号" prop="num">
					<el-select v-model="moveForm.num" class="form_225" clearable filterable
							   placeholder="请选择档号" style="width:90% !important" @change="numberChange">
						<el-option v-for="item in numberStatus" :key="item.id" :label="item.num" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="文件名称" prop="checkPlanTitle">
					<el-input v-model="moveForm.checkPlanTitle" clearable disabled
							  placeholder="请选择档号"
							  size="normal" style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="出库人员" prop="checkPlanPrincipal">
					<el-select v-model="moveForm.checkPlanPrincipal" class="form_225"
							   clearable
							   placeholder="请选择出库人员" style="width:90% !important;">
						<el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="出库原因" prop="outInRemark">
					<el-select v-model="moveForm.outInRemark" class="form_225" clearable
							   placeholder="请选择出库原因" style="width:90% !important;">
						<el-option v-for="item in reasonList" :key="item.value" :label="item.name"
								   :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="出库日期" prop="outInDate">
					<el-date-picker v-model="moveForm.outInDate" placeholder="请选择出库日期" style="width:90% ;"
									value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="出库时间" prop="outInTime">
					<el-time-picker v-model="moveForm.outInTime" placeholder="请选择出库时间" style="width:90%;"
									value-format="HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="文件情况" prop="remark">
					<el-input v-model="moveForm.remark" clearable placeholder="" size="normal" style="width:90%;"
							  type="textarea"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="moveFormVisible = false">取消</el-button>
			<el-button type="primary" @click="addmove(moveform)">确定</el-button>
		</div>
	</el-dialog>
	<!-- 出库记录详情弹框 -->
	<el-dialog v-model="movedetailFormVisible" title="出库记录详情" width="30%">
		<el-form ref="form" :model="movedetailForm" :rules="rules" label-position="right" label-width="100px"
				 size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档号" prop="info.num">
					<el-input v-model="movedetailForm.info.num" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="文件名称" prop="info.name">
					<el-input v-model="movedetailForm.info.name" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="出库人员" prop="outInPerson.name">
					<el-input v-model="movedetailForm.outInPerson.name" clearable disabled size="normal"
							  style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="出库原因" prop="outInRemark">
					<el-input v-model="movedetailForm.outInRemark" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="出库日期" prop="outInDate">
					<el-date-picker v-model="movedetailForm.outInDate" disabled placeholder="请选择出库日期"
									style="width:90% ;" value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="出库时间" prop="outInTime">
					<el-time-picker v-model="movedetailForm.outInTime" disabled placeholder="请选择出库时间"
									style="width:90%;" value-format="HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="文件情况" prop="outInFileCondition">
					<el-input v-model="movedetailForm.outInFileCondition" clearable disabled
							  placeholder="" size="normal" style="width:90%;" type="textarea"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="movedetailFormVisible = false">取消</el-button>
			<!-- <el-button type="primary" @click="addPlan(form)">确定</el-button> -->
		</div>
	</el-dialog>
	<!-- 修改出库记录弹框 -->
	<el-dialog v-model="moveeditFormVisible" title="修改出库记录" width="30%">
		<el-form ref="moveeditform" :model="moveeditForm" :rules="editmoverules" label-position="right"
				 label-width="100px" size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档号" prop="info.id">
					<el-select v-model="moveeditForm.info.id" class="form_225" clearable
							   filterable placeholder="请选择档号" style="width:90% !important" @change="numberChange">
						<el-option v-for="item in numberStatus" :key="item.id" :label="item.num" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="文件名称" prop="info.name">
					<el-input v-model="moveeditForm.info.name" clearable disabled placeholder="请选择档号"
							  size="normal" style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="出库人员" prop="outInPerson.id">
					<el-select v-model="moveeditForm.outInPerson.id" class="form_225"
							   clearable
							   placeholder="请选择出库人员" style="width:90% !important;">
						<el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="出库原因" prop="outInRemark">
					<el-select v-model="moveeditForm.outInRemark" class="form_225"
							   clearable
							   placeholder="请选择出库原因" style="width:90% !important;">
						<el-option v-for="item in reasonList" :key="item.value" :label="item.name"
								   :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="出库日期" prop="outInDate">
					<el-date-picker v-model="moveeditForm.outInDate" placeholder="请选择出库日期" style="width:90% ;"
									value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="出库时间" prop="outInTime">
					<el-time-picker v-model="moveeditForm.outInTime" placeholder="请选择出库时间" style="width:90%;"
									value-format="HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="文件情况" prop="outInFileCondition">
					<el-input v-model="moveeditForm.outInFileCondition" clearable placeholder=""
							  size="normal" style="width:90%;" type="textarea"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="moveeditFormVisible = false">取消</el-button>
			<el-button type="primary" @click="editMove(moveeditform)">确定</el-button>
		</div>
	</el-dialog>
	<!-- 入库记录增加弹框 -->
	<el-dialog v-model="enterFormVisible" title="新增入库记录" width="30%">
		<el-form ref="enterform" :model="enterForm" :rules="enterrules" label-position="right" label-width="100px"
				 size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档号" prop="num">
					<el-select v-model="enterForm.num" class="form_225" clearable filterable
							   placeholder="请选择档号" style="width:90% !important" @change="numberChange">
						<el-option v-for="item in numberStatus" :key="item.id" :label="item.num" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="文件名称" prop="checkPlanTitle">
					<el-input v-model="enterForm.checkPlanTitle" clearable disabled
							  placeholder="请选择档号"
							  size="normal" style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="入库人员" prop="putInPerson">
					<el-select v-model="enterForm.putInPerson" class="form_225"
							   clearable
							   placeholder="请选择入库人员" style="width:90% !important;">
						<el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="入库原因" prop="putInRemark">
					<el-select v-model="enterForm.putInRemark" class="form_225"
							   clearable
							   placeholder="请选择入库原因" style="width:90% !important;">
						<el-option v-for="item in reasonList" :key="item.value" :label="item.name"
								   :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="入库日期" prop="putInDate">
					<el-date-picker v-model="enterForm.putInDate" placeholder="请选择入库日期" style="width:90% ;"
									value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="入库时间" prop="putInTime">
					<el-time-picker v-model="enterForm.putInTime" placeholder="请选择入库时间" style="width:90%;"
									value-format="HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="文件情况" prop="putInFileCondition">
					<el-input v-model="enterForm.putInFileCondition" clearable placeholder="" size="normal"
							  style="width:90%;" type="textarea"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="enterFormVisible = false">取消</el-button>
			<el-button type="primary" @click="addenter(enterform)">确定</el-button>
		</div>
	</el-dialog>
	<!-- 入库记录详情弹框 -->
	<el-dialog v-model="enterFormDetailVisible" title="入库记录详情" width="30%">
		<el-form ref="form" :model="enterdetailForm" :rules="rules" label-position="right" label-width="100px"
				 size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档号" prop="valueName">
					<el-input v-model="enterdetailForm.info.num" disabled size="normal" style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="文件名称">
					<el-input v-model="enterdetailForm.info.name" disabled size="normal" style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="入库人员">
					<el-input v-model="enterdetailForm.putInPerson.name" disabled size="normal"
							  style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="入库原因" prop="valueName">
					<el-input v-model="enterdetailForm.putInRemark" disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="入库日期">
					<el-date-picker v-model="enterdetailForm.putInDate" disabled placeholder="请选择入库日期"
									style="width:90% ;" value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="入库时间">
					<el-time-picker v-model="enterdetailForm.putInTime" disabled placeholder="请选择入库时间"
									style="width:90%;" value-format="HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="文件情况" prop="valueName">
					<el-input v-model="enterdetailForm.putInFileCondition" clearable disabled
							  size="normal" style="width:90%;" type="textarea"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="enterFormDetailVisible = false">取消</el-button>
			<!-- <el-button type="primary" @click="addPlan(form)">确定</el-button> -->
		</div>
	</el-dialog>
	<!-- 入库记录修改弹框 -->
	<el-dialog v-model="enterFormEditVisible" title="修改入库记录" width="30%">
		<el-form ref="editenterform" :model="entereditForm" :rules="editenterrules" label-position="right"
				 label-width="100px" size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档号" prop="info.id">
					<el-select v-model="entereditForm.info.id" class="form_225" clearable
							   filterable placeholder="请选择档号" style="width:90% !important" @change="numberChange2">
						<el-option v-for="item in numberStatus" :key="item.id" :label="item.num" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="文件名称" prop="info.name">
					<el-input v-model="entereditForm.info.name" clearable disabled
							  placeholder="请选择档号"
							  size="normal" style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="入库人员" prop="putInPerson.id">
					<el-select v-model="entereditForm.putInPerson.id" class="form_225"
							   clearable placeholder="请选择入库人员" style="width:90% !important;">
						<el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="入库原因" prop="putInRemark">
					<el-select v-model="entereditForm.putInRemark" class="form_225"
							   clearable
							   placeholder="请选择入库原因" style="width:90% !important;">
						<el-option v-for="item in reasonList" :key="item.value" :label="item.name"
								   :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="入库日期" prop="putInDate">
					<el-date-picker v-model="entereditForm.putInDate" placeholder="请选择入库日期" style="width:90% ;"
									value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="入库时间" prop="putInTime">
					<el-time-picker v-model="entereditForm.putInTime" placeholder="请选择入库时间" style="width:90%;"
									value-format="HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="文件情况" prop="putInFileCondition">
					<el-input v-model="entereditForm.putInFileCondition" clearable placeholder=""
							  size="normal" style="width:90%;" type="textarea"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="enterFormEditVisible = false">取消</el-button>
			<el-button type="primary" @click="editenter(editenterform)">确定</el-button>
		</div>
	</el-dialog>
	<!-- 上架记录详情弹框 -->
	<el-dialog v-model="upFormEditVisible" title="上架记录详情" width="30%">
		<el-form ref="editenterform" :model="upeditForm" label-position="right" label-width="100px" size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档案盒名称" prop="box.boxName">
					<el-input v-model="upeditForm.box.boxName" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="保管期限" prop="upYear">
					<el-input v-model="upeditForm.upYear" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="上架时间" prop="upTime">
					<el-input v-model="upeditForm.upTime" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="上架货位号" prop="putInRemark">
					<el-input v-model="upeditForm.storage.storageName" clearable disabled size="normal"
							  style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="上架人" prop="upPerson.name">
					<el-input v-model="upeditForm.upPerson.name" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="upFormEditVisible = false">取消</el-button>
			<!-- <el-button type="primary" @click="editenter(editenterform)">确定</el-button> -->
		</div>
	</el-dialog>
	<!-- 下架记录详情弹框 -->
	<el-dialog v-model="downFormEditVisible" title="下架记录详情" width="30%">
		<el-form ref="editenterform" :model="downeditForm" label-position="right" label-width="100px" size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档案盒名称" prop="info.name">
					<el-input v-model="downeditForm.box.boxName" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="保管期限" prop="upYear">
					<el-input v-model="downeditForm.downYear" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="下架时间" prop="upTime">
					<el-input v-model="downeditForm.downTime" clearable disabled size="normal" style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="下架货位号" prop="putInRemark">
					<el-input v-model="downeditForm.storage.storageName" clearable disabled size="normal"
							  style="width:90%;">
					</el-input>
				</el-form-item>
				<el-form-item label="下架人" prop="putInTime">
					<el-input v-model="downeditForm.downPerson.name" clearable disabled size="normal"
							  style="width:90%;">
					</el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="downFormEditVisible = false">取消</el-button>
			<!-- <el-button type="primary" @click="editenter(editenterform)">确定</el-button> -->
		</div>
	</el-dialog>
</template>

<script setup>
import {getCurrentInstance, reactive, ref} from 'vue'
import tree from '../tree'
import storeroomData from "@/api/archive/storeroom/storeroom"
import fileInventory from "@/api/archive/storeroom/fileInventory"
import warehouseBrowsing from "@/api/archive/storeroom/warehouseBrowsing"
import moment from "moment";

const {proxy} = getCurrentInstance();
const moverules = reactive({
	num: [{required: true, message: '请选择档号', trigger: 'blur'},],
	checkPlanTitle: [{required: true, message: '请选择档号', trigger: 'blur'},],
	checkPlanPrincipal: [{required: true, message: '请选择出库人员', trigger: 'blur'},],
	outInRemark: [{required: true, message: '请选择出库原因', trigger: 'blur'},],
	outInDate: [{required: true, message: '请选择出库日期', trigger: 'blur'},],
	outInTime: [{required: true, message: '请选择出库时间', trigger: 'blur'},],
	remark: [{required: true, message: '请填写文件情况', trigger: 'blur'},],
})
const enterrules = reactive({
	num: [{required: true, message: '请选择档号', trigger: 'blur'},],
	checkPlanTitle: [{required: true, message: '请选择档号', trigger: 'blur'},],
	putInPerson: [{required: true, message: '请选择入库人员', trigger: 'blur'},],
	putInRemark: [{required: true, message: '请选择入库原因', trigger: 'blur'},],
	putInDate: [{required: true, message: '请选择入库日期', trigger: 'blur'},],
	putInTime: [{required: true, message: '请选择入库时间', trigger: 'blur'},],
	putInFileCondition: [{required: true, message: '请填写文件情况', trigger: 'blur'},],
})
const editmoverules = reactive({
	'info.id': [{required: true, message: '请选择档号', trigger: 'blur'},],
	'info.name': [{required: true, message: '请选择档号', trigger: 'blur'},],
	'outInPerson.id': [{required: true, message: '请选择出库人员', trigger: 'blur'},],
	outInRemark: [{required: true, message: '请选择出库原因', trigger: 'blur'},],
	outInDate: [{required: true, message: '请选择出库日期', trigger: 'blur'},],
	outInTime: [{required: true, message: '请选择出库时间', trigger: 'blur'},],
	outInFileCondition: [{required: true, message: '请填写文件情况', trigger: 'blur'},],

})
const editenterrules = reactive({
	'info.id': [{required: true, message: '请选择档号', trigger: 'blur'},],
	'info.name': [{required: true, message: '请选择档号', trigger: 'blur'},],
	'putInPerson.id': [{required: true, message: '请选择入库人员', trigger: 'blur'},],
	putInRemark: [{required: true, message: '请选择入库原因', trigger: 'blur'},],
	putInDate: [{required: true, message: '请选择入库日期', trigger: 'blur'},],
	putInTime: [{required: true, message: '请选择入库时间', trigger: 'blur'},],
	putInFileCondition: [{required: true, message: '请填写文件情况', trigger: 'blur'},],

})
const menuList = ref([])
const moveform = ref()
const enterform = ref()
const editenterform = ref()
const moveeditform = ref()
const activeName = ref('1')
const orgId = ref('')
const boxId = ref('')
const editMoveId = ref('')
const editenterId = ref('')
const moveList = ref([])
const orguserList = ref([])
const enterList = ref([])
const upList = ref([])
const archivesData = ref([])
const downList = ref([])
const numberStatus = ref([])
const reasonList = ref([])
const upqueryParams = ref({
	size: 10,
	current: 1
})
const downqueryParams = ref({
	size: 10,
	current: 1
})
const archivesqueryParams = ref({
	size: 10,
	current: 1
})
const moveForm = reactive({})
const movedetailForm = ref({})
const upeditForm = ref({})
const downeditForm = ref({})
const enterdetailForm = ref({})
const enterForm = reactive({})
const moveeditForm = ref({})
const entereditForm = ref({})
const total = ref(0)
const uptotal = ref(0)
const downtotal = ref(0)
const archivestotal = ref(0)
const entertotal = ref(0)
const loading = ref(false);
const moveFormVisible = ref(false)
const archivesVisible = ref(false)
const enterFormVisible = ref(false)
const upFormEditVisible = ref(false)
const enterFormEditVisible = ref(false)
const movedetailFormVisible = ref(false)
const enterFormDetailVisible = ref(false)
const downFormEditVisible = ref(false)
const moveeditFormVisible = ref(false)
const queryParams = ref({
	current: 1,
	size: 10
})
const queryParams2 = ref({
	current: 1,
	size: 10
})
//单元格单击事件
const rowClick = (v) => {
	boxId.value = v.box.id
	console.log('2222222');
	archivesVisible.value = true
	console.log('233434');
	archivesqueryParams.value.box.id = v.box.id
	var params = {
		'box.id': boxId.value,
		...archivesqueryParams.value
	}
	console.log(params);
	warehouseBrowsing.archivesdetail(params).then(res => {
		if (res.code === 200) {
			archivesData.value = res.data.records
			archivestotal.value = res.data.total
		}
	})
}
//侧边栏树结构
const treeList = () => {
	storeroomData.treeData().then(res => {
		if (res.code === 200) {
			menuList.value = res.data;
		}
	})
}
//字典回显
const formDict = (data, val) => {
	return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}
//档号
const numberList = () => {
	fileInventory.numberList({size: -1, current: 1,archiveStatus:'1'}).then(res => {
		if (res.code === 200) {
			numberStatus.value = res.data.records
		}
	});
}
// 档号变动
const numberChange = (v) => {
	numberStatus.value.forEach((i) => {
		if (i.id == v) {
			moveForm.checkPlanTitle = i.name
			enterForm.checkPlanTitle = i.name
			moveeditForm.value.info.name = i.name
		}
	})
}
const numberChange2 = (v) => {
	numberStatus.value.forEach((i) => {
		if (i.id == v) {
			entereditForm.value.info.name = i.name
		}
	})
}
//获取机构成员
const orgList = () => {
	let org = JSON.parse(localStorage.getItem("Organization"));
	org.content.forEach((v) => {
		orgId.value = v.id
	})
	fileInventory.orgList({'sysOrg.id': orgId.value}).then(res => {
		if (res.code === 200) {
			orguserList.value = res.data.records
		}
	});
}
//出库记录列表
const getList = () => {
	warehouseBrowsing.list({...queryParams.value}).then(res => {
		if (res.code === 200) {
			moveList.value = res.data.records
			total.value = res.data.total
		}
	})
}
//出库记录增加按钮
const movelAdd = (formEl) => {
	moveForm.outInDate = moment(new Date()).format('YYYY-MM-DD')
	moveForm.outInTime = moment(new Date()).format('HH:mm:ss')
	moveFormVisible.value = true
	moveForm.checkPlanTitle = ''
	formEl.resetFields()
}
//保存出库记录
const addmove = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			let params = {
				...moveForm
			}
			if (params.num) {
				params.info = {
					id: params.num
				}
				params.outInPerson = params.checkPlanPrincipal
				params.outInFileCondition = params.remark
				delete params.num
				delete params.checkPlanTitle
				delete params.checkPlanPrincipal
				delete params.remark
			}
			warehouseBrowsing.save(params).then(res => {
				if (res.code === 200) {
					moveFormVisible.value = false
					proxy.msgSuccess("保存成功");
					getList();
				}
			})
		}
	});
};
//查看出库记录详情
const moveDetail = (row) => {
	movedetailFormVisible.value = true
	warehouseBrowsing.detail({id: row.id}).then(res => {
		if (res.code === 200) {
			reasonList.value.forEach((v) => {
				if (res.data.outInRemark == v.value) {
					res.data.outInRemark = v.name
				}
			})
			movedetailForm.value = res.data
		}
	})
}
//修改出库记录按钮
const moveleEdit = (row, formEl) => {
	moveeditFormVisible.value = true
	editMoveId.value = row.id
	if (formEl !== undefined) {
		formEl.resetFields()

	}
	warehouseBrowsing.detail({id: row.id}).then(res => {
		if (res.code === 200) {
			moveeditForm.value = res.data
		}
	})
}
//出库记录修改请求
const editMove = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			let params = {
				id: editMoveId.value,
				info: {
					id: moveeditForm.value.info.id
				},
				outInPerson: moveeditForm.value.outInPerson.id,
				outInDate: moveeditForm.value.outInDate,
				outInTime: moveeditForm.value.outInTime,
				outInRemark: moveeditForm.value.outInRemark,
				outInFileCondition: moveeditForm.value.outInFileCondition,
			}
			warehouseBrowsing.save(params).then(res => {
				if (res.code === 200) {
					moveeditFormVisible.value = false
					proxy.msgSuccess("修改成功");
					getList();
				}
			})
		}
	});
}
//删除出库记录
const moveDelete = (row) => {
	proxy.$confirm('是否确认删除此条出库记录?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		warehouseBrowsing.delete({ids: row.id}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess("删除成功");
				getList();
			}
		})
	}).catch(() => {
	});
}
//入库记录列表
const getenterList = () => {
	warehouseBrowsing.enterlist({...queryParams2.value}).then(res => {
		if (res.code === 200) {
			enterList.value = res.data.records
			entertotal.value = res.data.total
		}
	})
}
//入库记录增加按钮
const enterAdd = (formEl) => {
	enterForm.putInDate = moment(new Date()).format('YYYY-MM-DD')
	enterForm.putInTime = moment(new Date()).format('HH:mm:ss')
	enterFormVisible.value = true
	enterForm.checkPlanTitle = ''
	formEl.resetFields()
}
//保存入库记录
const addenter = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			let params = {
				...enterForm
			}
			if (params.num) {
				params.info = {
					id: params.num
				}
				delete params.num
				delete params.checkPlanTitle
			}
			warehouseBrowsing.entersave(params).then(res => {
				if (res.code === 200) {
					enterFormVisible.value = false
					proxy.msgSuccess("保存成功");
					getenterList();
				}
			})
		}
	});
};
//查看入库记录详情
const enterDetail = (row) => {
	enterFormDetailVisible.value = true
	warehouseBrowsing.enterdetail({id: row.id}).then(res => {
		if (res.code === 200) {
			reasonList.value.forEach((v) => {
				if (res.data.putInRemark == v.value) {
					res.data.putInRemark = v.name
				}
			})
			enterdetailForm.value = res.data
		}
	})
}
//修改入库记录按钮
const enterEdit = (row, formEl) => {
	editenterId.value = row.id
	enterFormEditVisible.value = true
	if (formEl !== undefined) {
		formEl.resetFields()

	}
	warehouseBrowsing.enterdetail({id: row.id}).then(res => {
		if (res.code === 200) {
			entereditForm.value = res.data
		}
	})
}
//修改入库记录请求
const editenter = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			let params = {
				id: editenterId.value,
				info: {
					id: entereditForm.value.info.id
				},
				putInPerson: entereditForm.value.putInPerson.id,
				putInDate: entereditForm.value.putInDate,
				putInTime: entereditForm.value.putInTime,
				putInRemark: entereditForm.value.putInRemark,
				putInFileCondition: entereditForm.value.putInFileCondition,
			}
			console.log(params);
			warehouseBrowsing.entersave(params).then(res => {
				if (res.code === 200) {
					enterFormEditVisible.value = false
					proxy.msgSuccess("修改成功");
					getenterList();
				}
			})
		}
	});
}
//删除入库记录
const enterDelete = (row) => {
	proxy.$confirm('是否确认删除此条入库记录?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		warehouseBrowsing.enterdelete({ids: row.id}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess("删除成功");
				getenterList();
			}
		})
	}).catch(() => {
	});
}
//上架记录列表
const upRecords = () => {
	warehouseBrowsing.upRecords({...upqueryParams.value}).then(res => {
		if (res.code === 200) {
			upList.value = res.data.records
			uptotal.value = res.data.total
		}
	})
}
//上架记录详情
const upDetail = (row) => {
	upFormEditVisible.value = true
	warehouseBrowsing.updetail({id: row.id}).then(res => {
		if (res.code === 200) {
			res.data.upYear = res.data.upYear ? res.data.upYear + '年' : '永久';
			upeditForm.value = res.data
			console.log(upeditForm.value);
		}
	})
}
//下架记录列表
const downRecords = () => {
	warehouseBrowsing.downRecords({...downqueryParams.value}).then(res => {
		if (res.code === 200) {
			downList.value = res.data.records
			downtotal.value = res.data.total
		}
	})
}
//下架记录详情
const downDetail = (row) => {
	downFormEditVisible.value = true
	warehouseBrowsing.downdetail({id: row.id}).then(res => {
		if (res.code === 200) {
			res.data.downYear = res.data.downYear ? res.data.downYear + '年' : '永久';
			downeditForm.value = res.data;
		}
	})
}
// 字典
const getDict = async () => {
	reasonList.value = await proxy.getDictList('delivery_reason')
}
getDict()
treeList()
numberList()
orgList()
getList()
getenterList()
upRecords()
downRecords()
</script>

<style lang="scss" scoped>
.dialog-footer {
	display: flex;
	justify-content: end;
	margin: 20px 10px 10px 0px;
}</style>
