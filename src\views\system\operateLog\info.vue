<template>
	<el-main>
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item align="center" label="接口名称" width="120">
				{{ logData.appName }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="操作" width="120">
				{{ logData.operate }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="操作人" width="auto">
				{{ logData.operator }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="日志类型" width="auto">
				{{ logData.logType }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="日志等级" width="auto">
				<el-tooltip v-if="logData.logGrade === 'info'" content="默认" effect="light"
							placement="top">
					<el-tag effect="dark" type="success">info</el-tag>
				</el-tooltip>
				<el-tooltip v-if="logData.logGrade === 'error'" content="错误" effect="light"
							placement="top">
					<el-tag effect="dark" type="danger">error</el-tag>
				</el-tooltip>
			</el-descriptions-item>
			<el-descriptions-item align="center" label="请求接口" width="auto">
				{{ logData.url }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="请求方法" width="auto">
				{{ logData.method }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="状态代码" width="auto">
				<el-tooltip v-if="logData.httpCode === '200'" content="请求成功" effect="light"
							placement="top">
					<el-tag effect="dark" type="success">200</el-tag>
				</el-tooltip>
				<el-tooltip v-else-if="logData.httpCode === '401'" content="未授权" effect="light"
							placement="top">
					<el-tag effect="dark" type="warning">401</el-tag>
				</el-tooltip>
				<el-tooltip v-else-if="logData.httpCode === '404'" content="无法找到" effect="light"
							placement="top">
					<el-tag style="background-color: black;color: white">404</el-tag>
				</el-tooltip>
				<el-tooltip v-else-if="logData.httpCode === '500'" content="内部服务器错误" effect="light"
							placement="top">
					<el-tag effect="dark" type="danger">500</el-tag>
				</el-tooltip>
				<el-tag v-else style="background-color: black;color: white">404</el-tag>
			</el-descriptions-item>
			<el-descriptions-item align="center" label="客户端IP" width="auto">
				{{ logData.clientIp }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="目标类名" width="auto">
				{{ logData.pointClass }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="信息" width="auto">
				{{ logData.pointMessage }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="详细信息" width="auto">
				{{ logData.pointDetailed }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="日志代码" width="auto">
				{{ logData.logCode ? logData.logCode : '暂无' }}
			</el-descriptions-item>
			<el-descriptions-item align="center" label="日志时间" width="auto">
				{{ moment(logData.createDate).format('YYYY-MM-DD HH:mm:ss') }}
			</el-descriptions-item>
		</el-descriptions>
		<el-collapse v-model="activeNames" style="margin-top: 20px;">
			<el-collapse-item name="1" title="请求体">
				<div class="code">
					<el-text size="small" style="color: white">
						{{ logData.requestBody ? logData.requestBody : '暂无' }}
					</el-text>
				</div>
			</el-collapse-item>
			<el-collapse-item name="2" title="响应体">
				<div class="code">
					<el-text size="small" style="color: white">
						{{ logData.responseBody ? logData.responseBody : '暂无' }}
					</el-text>
				</div>
			</el-collapse-item>
		</el-collapse>
	</el-main>
</template>

<script>
import moment from "moment";

export default {
	computed: {
		moment() {
			return moment
		}
	},
	data() {
		return {}
	},
	props: {
		logData: {
			type: Object,
			default: () => {
			}
		}
	},
	mounted() {

	},
	methods: {}
}
</script>

<style scoped>
.code {
	background: #848484;
	padding: 15px;
	color: #fff;
	font-size: 12px;
	border-radius: 4px;
}
</style>
