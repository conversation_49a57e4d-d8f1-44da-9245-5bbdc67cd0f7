<template>
	<div class="register-bg">
		<img src="../../../public/img/registerZhilian.png" alt="" class="imgZhilian">
		<div class="registerRight">
			<p class="top_p">填写信息</p>
			<p class="bottom_p">开启智能化研发管理之路</p>
			<el-form ref="loginForm" :model="form"  label-width="0" size="large" @keyup.enter="login"
				label-position="top" hide-required-asterisk=“true” class="form">
				<el-form-item prop="user" label="手机号" class="el-form-item__label">
					
					<el-input v-model="form.user" clearable placeholder="请输入手机号" class="input">
						<!-- <template #prepend>+86</template> -->
					</el-input>
				
				</el-form-item>
				<el-form-item prop="password" label="手机验证码" class="el-form-item__label2">
					<el-input v-model="form.password" clearable placeholder="请输入手机验证码" class="input2"></el-input>
					<el-button @click="getYzm" :disabled="disabled">{{ this.$t('login.smsGet') }}<span v-if="disabled">
							({{ time }})</span></el-button>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" :loading="islogin" @click="submitLogin" class="login">下一步</el-button>
				</el-form-item>
				<div class="login-reg">
					已有账号？去
					<router-link to="/login" style="color: #2878FF;">登录</router-link>
				</div>
			</el-form>
		</div>
	</div>
</template>
<script>
export default {
	data() {
		return {
			form: {
				user: "",
				password: "",
				password2: "",
				agree: false,
				userName: "",
				email: "",
				userType: "1",
				open: []
			},
			rules: {
				user: [
					{ required: true, message: '请输入账号名' }
				],
				password: [
					{ required: true, message: '请输入密码' }
				],
			}
		}
	},
	mounted() {

	},
	methods: {
		goLogin() {
			this.$router.push({
				path: '/login'
			})
		}
	}
}
</script>

<style scoped>
.register-bg {
	width: 100%;
	height: 100%;
	/* display: flex; */
	background-image: url(../../../public/img/register.png);
	background-size: contain;
}

.imgZhilian {
	width: 499px;
	height: 104px;
	margin: 380px 1142px 0px 279px;
}

.registerRight {
	position: relative;
	margin: -330px 398px 259px 1041px;
	width: 481px;
	height: 563px;
	background: #FFFFFF;
	box-shadow: 0px 5px 35px 0px rgba(0, 0, 0, 0.04);
	border-radius: 4px;
}

.top_p {
	width: 120px;
	height: 31px;
	font-size: 30px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	line-height: 24px;
	position: absolute;
	top: 70px;
	left: 180px;
}

.bottom_p {
	width: 198px;
	height: 19px;
	font-size: 18px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	line-height: 24px;
	position: absolute;
	top: 121px;
	left: 141px;
}
.el-form--large.el-form--label-top .el-form-item .el-form-item__label {
	width: 120px;
	height: 16px;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #666666;
	line-height: 24px;
	margin-bottom: 15px;
	line-height: 22px;
}

.el-form-item__label2 {
	margin-top: 65px;
	position: relative;
}

.el-form-item__label2 button {
	position: absolute;
	/* top: 7px;
        right: 0; */
	left: 240px;
	background: #F5F5F5;
	width: 130px;
	height: 28px;
	background: #F5F5F5;
	border-radius: 4px;
	border: none;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #CACACA;
	line-height: 24px;
}

.login {
	margin-top: 10px;
	width: 417px;
	height: 47px;
	background: linear-gradient(-5deg, #6394FF, #2878FF);
	border-radius: 4px;
}

.form {
	padding:178px 50px 0px 50px;
}

.input {
	width: 418px;
	height: 48px;
	background: #FFFFFF;
	border: 1px solid #D5D5D5;
	border-radius: 4px;
}

.input2 {
	width: 418px;
	height: 48px;
	/* margin-top: 20px; */
	background: #FFFFFF;
	border: 1px solid #D5D5D5;
	border-radius: 4px;
}

.login-reg {
	margin-top: -10px;
}
::v-deep .el-input__wrapper{
	width:379px;
}
</style>
