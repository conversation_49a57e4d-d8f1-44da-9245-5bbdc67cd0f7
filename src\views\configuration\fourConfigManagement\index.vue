<template>
	<el-container>
		<el-header height="auto" style="background: none;border-bottom: none;padding: 10px 10px 5px 10px">
			<el-card style="height: 100%;width: 100%">
				<el-form ref="queryFrom" :inline="true" :model="fourConfig" label-position="left" label-width="auto"
						 style="vertical-align: center">
					<el-form-item label="一级目录" prop="recordFourConfigLevelOne"
								  style="margin: 0;padding-right: 20px;">
						<el-select v-model="form.recordFourConfigLevelOne" class="form_225"
								   placeholder="请选择一级目录">
							<el-option v-for="item in fourConfig.recordFourConfigLevelOne" :key="item.value"
									   :label="item.name" :value="item.value"/>
						</el-select>
					</el-form-item>
					<el-form-item label="二级目录" prop="recordFourConfigLevelTwo"
								  style="margin: 0;padding-right: 20px;">
						<el-select v-model="form.recordFourConfigLevelTwo" class="form_225"
								   placeholder="请选择二级目录">
							<el-option v-for="item in fourConfig.recordFourConfigLevelTwo" :key="item.value"
									   :label="item.name" :value="item.value"/>
						</el-select>
					</el-form-item>
					<el-form-item label="是否开启" prop="recordFourConfigEnable" style="margin: 0;">
						<el-select v-model="form.recordFourConfigEnable" class="form_225" placeholder="请选择是否开启">
							<el-option label="否" value="0"/>
							<el-option label="是" value="1"/>
						</el-select>
					</el-form-item>
					<el-form-item style="margin: 0;padding-left: 10px;">
						<el-button icon="Search" type="primary" @click="handleQuery">
							查询
						</el-button>
						<el-button icon="RefreshRight" plain @click="resetQuery">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px">
			<el-card :body-style="{height: '100%',width: '100%',padding: '20px 20px 0 20px'}"
					 style="height: 100%;width: 100%">
				<el-container>
					<el-header height="auto" style="background: none;border-bottom: none;padding: 0 0 15px 0;
						display: flex;justify-content: flex-start">
						<el-button icon="Right" plain type="warning" @click="handleExport">
							导出配置
						</el-button>
						<el-button icon="Back" plain type="success" @click="handleImport">
							导入配置
						</el-button>
						<el-button icon="Download" plain type="primary" @click="handleDownload">
							下载模版
						</el-button>
					</el-header>
					<el-main style="padding: 0">
						<el-table :data="fourConfigData" border @selection-change="handleSelectionChange_file">
							<el-table-column align="center" min-width="30" type="selection" width="50"/>
							<el-table-column align="center" label="一级目录" prop="recordFourConfigLevelOne" width="80">
								<template #default="scope">
									{{
										formDict(data.fourConfig.recordFourConfigLevelOne, scope.row.recordFourConfigLevelOne)
									}}
								</template>
							</el-table-column>
							<el-table-column align="center" label="二级目录" prop="recordFourConfigLevelTwo"
											 width="100">
								<template #default="scope">
									{{
										formDict(data.fourConfig.recordFourConfigLevelTwo, scope.row.recordFourConfigLevelTwo)
									}}
								</template>
							</el-table-column>
							<!-- <el-table-column label="配置名称" align="center" prop="recordFourConfigName" width="100"/> -->
							<el-table-column align="center" label="编号" prop="recordFourConfigNum" width="100"/>
							<el-table-column align="center" label="四性检测项目" min-width="200"
											 prop="recordFourConfigItem"/>
							<el-table-column align="center" label="四性检测目的" min-width="200"
											 prop="recordFourConfigTarget"/>
							<el-table-column align="center" label="四性检测对象" min-width="200"
											 prop="recordFourConfigObject"/>
							<el-table-column align="center" label="四性检测依据和方法" min-width="320"
											 prop="recordFourConfigMethod"/>
							<el-table-column align="center" label="是否启用" prop="recordFourConfigEnable" width="80">
								<template #default="scope">
									<el-tag :type="scope.row.recordFourConfigEnable === '0' ? 'danger' : 'primary'" disable-transitions
											style="cursor: pointer;"
											@click="modifyEnable(scope.row)">
										{{ scope.row.recordFourConfigEnable === '0' ? '否' : '是' }}
									</el-tag>
								</template>
							</el-table-column>
							<!-- <el-table-column label="排序" align="center" prop="sort" width="150px" /> -->
							<el-table-column align="center" fixed="right" label="操作" width="152">
								<template #default="scope">
									<el-button icon="Edit" link type="primary" @click="handleAdd(scope.row)">编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="handleDelete(scope.row)">删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="pageSize" v-model:page="pageCurrent" :total="total"
										style="padding: 0" @pagination="paginationing"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改配置对话框 -->
		<el-dialog :title="title" v-model="open" width="600px" append-to-body v-if="open">
			<el-form v-loading="loading" ref="formRef" :model="form" :rules="rules" label-width="150px"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="一级目录" prop="recordFourConfigLevelOne">
					<el-select v-model="form.recordFourConfigLevelOne" placeholder="请选择一级目录" class="form_225">
						<el-option :label="item.name" :value="item.value"
								   v-for="item in fourConfig.recordFourConfigLevelOne" :key="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="二级目录" prop="recordFourConfigLevelTwo">
					<el-select v-model="form.recordFourConfigLevelTwo" placeholder="请选择二级目录" class="form_225">
						<el-option :label="item.name" :value="item.value"
								   v-for="item in fourConfig.recordFourConfigLevelTwo" :key="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="四性检测配置名称" prop="recordFourConfigName">
					<el-input v-model="form.recordFourConfigName" placeholder="请输入名称"/>
				</el-form-item>
				<el-form-item label="四性检测编号" prop="recordFourConfigNum">
					<el-input v-model="form.recordFourConfigNum" placeholder="请输入四性检测编号"/>
				</el-form-item>
				<el-form-item label="四性检测项目" prop="recordFourConfigItem">
					<el-input v-model="form.recordFourConfigItem" placeholder="请输入四性检测项目"/>
				</el-form-item>
				<el-form-item label="四性检测目的" prop="recordFourConfigTarget">
					<el-input v-model="form.recordFourConfigTarget" placeholder="请输入四性检测目的"/>
				</el-form-item>
				<el-form-item label="四性检测对象" prop="recordFourConfigObject">
					<el-input v-model="form.recordFourConfigObject" placeholder="请输入四性检测对象"/>
				</el-form-item>
				<el-form-item label="四性检测依据和方法" prop="recordFourConfigMethod">
					<el-input v-model="form.recordFourConfigMethod" placeholder="四性检测依据和方法"/>
				</el-form-item>
				<el-form-item label="四性检测是否启用" prop="recordFourConfigEnable">
					<el-switch v-model="form.recordFourConfigEnable" active-text="开" inactive-text="关"
							   active-value="1"
							   inactive-value="0" width="42">
					</el-switch>
				</el-form-item>
				<el-form-item label="排序" prop="sort">
					<el-input v-model="form.sort" placeholder="请输入排序"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="sava(form)">确 定</el-button>
					<el-button @click="cancellation()">取 消</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 上传的弹框 -->
		<el-dialog v-model="dialogFormVisible" width="30%" title="档案四性检测配置导入">
			<el-upload v-model:file-list="fileList" class="upload-demo" :action="uploadUrl" :on-preview="handlePreview"
					   :before-remove="beforeRemove" :before-upload="before" :headers='headers'
					   :on-exceed="handleExceed" accept="accept"
					   :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList)"
					   :on-progress="uploadVideoProcess">
				<el-button class="upload-button" icon="UploadFilled" plain type="primary">上传</el-button>
			</el-upload>
			<div style="margin-left:30px;" v-if="uploadDiv">
				<p class="upload-p">导入完成，成功导入{{ uploadSucceed }}份文件，导入失败{{ uploadFail }}份文件!</p>
				<div v-if="uploadDiv2">
					<p>错误内容：</p>
					<p style="margin:20px 0px 20px 40px;">1.xxxxxxxxx</p>
					<p style="margin-left:40px;">2.xxxxxxxxxx</p>
				</div>
			</div>
			<template #footer>
				<el-button @click="dialogFormVisible = false">取消</el-button>
				<el-button type="primary" @click="creat">确定</el-button>
			</template>
		</el-dialog>

		<el-dialog v-model="openExport" :title="title" append-to-body width="600px">
			<p>将对以下{{ chooseList.length }}条数据进行导出操作，请确定</p>
			<div v-for="(item, index) in chooseList" :key="index" style="display:flex">
				<p>
					<span>四性检测配置名称：</span>
					<span>{{ item.recordFourConfigName }}，</span>
				</p>
				<p>
					<span>四性检测编号：</span>
					<span>{{ item.recordFourConfigNum }}</span>
				</p>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定</el-button>
					<el-button @click="() => openExport = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from 'vue'
import fourConfigList from '@/api/archive/fourConfigManagement/fourConfig';
import tool from '@/utils/tool';
import {ElMessage} from "element-plus";

const {proxy} = getCurrentInstance();
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 10,
	},
	fourConfig: {
		current: 1,
		size: 10,
	},
});
const pageCurrent = ref(1);
const pageSize = ref(10);
const total = ref(0);
const chooseList = ref([])
const {queryParams, fourConfig, form, rules} = toRefs(data);
// 四性List
const fourConfigData = ref([]);
//弹窗状态
const open = ref(false);
const title = ref("");
const dialogFormVisible = ref(false)
const uploadDiv = ref(false)
const uploadDiv2 = ref(false)
const fileList = ref([])
const uploadUrl = '/archive/config/fourConfig/import'
const uploadSucceed = ref(0)
const uploadFail = ref(0)
const videoUploadPercent = ref()
const headers = {
	Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
	ContentType: 'multipart/form-data',
	clientType: "PC"
}

//字典值表格转换
const formDict = (data, val) => {
	return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}

// 导出openExport
const openExport = ref(false);
// 导出
const handleSelectionChange_file = (key) => {
	chooseList.value = key
}
const newFilArr = ref([])

function handleExport() {
	if (chooseList.value.length === 0) {
		ElMessage({
			message: "请选择要导出的档案四性检测配置",
			type: "warning",
		});
	} else {
		openExport.value = true;
	}
}

function submitForm() {
	let selectIds = "";
	newFilArr.value = [];
	chooseList.value.filter((item) => {
		newFilArr.value.push(item.id)

	})
	selectIds = newFilArr.value.join(",")
	// console.log(newFilArr.value);
	fourConfigList.export({
		mode: 'selected',
		ids: selectIds,
		filename: '档案四性检测配置',
		sheetName: '档案四性检测配置',
		current: queryParams.value.current,
		size: queryParams.value.size,
	}).then(res => {
		openExport.value = false,
			proxy.download(res, "application/vnd.ms-excel", "档案四性检测配置模板.xlsx")
	})
}


//文件上传
function uploadVideoProcess(event) {
	videoUploadPercent.value = Math.floor(event.percent);
}

function before(file) {
	if (!['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type)) {
		ElMessage({
			message: `选择的文件类型 ${file.type} 非Excel文件`,
			type: "warning",
		});
		return false;
	}
	const maxSize = file.size / 1024 / 1024 < 100;
	if (!maxSize) {
		ElMessage({
			message: "上传文件大小不能超过100MB!",
			type: "warning",
		});
		return false;
	}
}

function handleImport() {
	dialogFormVisible.value = true
	uploadDiv.value = false
	fileList.value = []
	uploadSucceed.value = 0
	uploadFail.value = 0
}

//文件上传成功
const handleUploadSuccess = (res, file, fileList) => {
	if (res.code == 200) {
		fileList.value = [{...res.data}]
		uploadDiv.value = true
		uploadSucceed.value += 1
	} else {
		uploadDiv2.value = true
		proxy.msgError(res.msg)
	}
}
// 确定按钮
const creat = () => {
	dialogFormVisible.value = false
	getList()
	proxy.msgSuccess('保存成功')
}

// 搜索
function handleQuery() {
	fourConfig.value.current = pageCurrent,
		fourConfig.value.size = pageSize,
		fourConfigList.list({
			recordFourConfigLevelOne: form.value.recordFourConfigLevelOne,
			recordFourConfigLevelTwo: form.value.recordFourConfigLevelTwo,
			recordFourConfigEnable: form.value.recordFourConfigEnable,
			current: fourConfig.value.current,
			size: fourConfig.value.size,
		}).then(res => {
			if (res.code === 200) {
				fourConfigData.value = res.data.records;
				total.value = res.data.total
			}
		})

}

// 重置
function resetQuery() {
	form.value.recordFourConfigEnable = null;
	form.value.recordFourConfigLevelOne = null;
	form.value.recordFourConfigLevelTwo = null;
	getList();
}

// 下载配置模版
const handleDownload = () => {
	fourConfigList.template().then(res => {
		proxy.download(res, "application/vnd.ms-excel", '档案四性检测配置模板.xlsx')
	})
}

// 分页
function paginationing() {
	if (form.value.recordFourConfigEnable == '' && form.value.recordFourConfigLevelOne == '' && form.value.recordFourConfigLevelTwo == '') {
		// 	console.log(1111111);
		getList()
	} else {
		console.log(222);
		handleQuery()
	}
}

// 查询列表
function getList() {
	queryParams.value.current = pageCurrent,
		queryParams.value.size = pageSize,
		fourConfigList.list(queryParams.value).then(res => {
			if (res.code === 200) {
				fourConfigData.value = res.data.records;
				total.value = res.data.total;
			}
		})

}

// 修改启用状态
function modifyEnable(data) {
	proxy.$confirm('是否修改四性检测的启用状态?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		fourConfigList.save({
			id: data.id,
			recordFourConfigEnable: (data.recordFourConfigEnable == '0' ? '1' : '0')
		}).then(res => {
			if (res.code === 200) {
				getList();
			}
		})
	})
}

// 修改数据
function handleAdd(data) {
	form.value = data;
	open.value = true;
}

function sava() {
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			fourConfigList.save(form.value).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess(`${title.value}成功`);
					open.value = false;
					getList();
				}
			}).catch(() => {
				proxy.msgError('保存失败');
			})
		}
	})
}

// 取消修改
function cancellation() {
	open.value = false;
	getList();
}

// 删除数据
const handleDelete = (row) => {
	proxy.$confirm('是否确认删除该条数据吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		fourConfigList.delete({ids: row.id}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('删除成功')
				getList();
			} else {
				proxy.msgError('删除失败')
			}
		})
	})
}


// 字典值
async function dict() {
	data.fourConfig.recordFourConfigLevelOne = await proxy.getDictList("four_config_link");
	data.fourConfig.recordFourConfigLevelTwo = await proxy.getDictList("four_config_check_type");
}

getList();
dict();
</script>

<style lang="scss" scoped>
.form_130 .el-form-item__label {
	width: none;
}
</style>
