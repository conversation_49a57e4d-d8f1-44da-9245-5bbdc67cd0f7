import http from "@/utils/request"

/*
 *@description: 字典类型
 *@author: 路正宁
 *@date: 2023-03-17 11:24:02
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/dictType/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/dictType/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/dictType/queryById',
      {id: id}
    )
  },

  treeData: function () {
    return http.get(
      '/sys/dictType/treeData'
    )
  },
   /*
   * 修改排序
   * @author: 路正宁
   * @date: 2023-03-23 09:52:48
  */
   updateSort (draggingNodeId,dropNodeId,dropType,sort) {
    return http.get(
      '/sys/dictType/updateSort',
      {draggingNodeId: draggingNodeId,dropNodeId:dropNodeId,dropType:dropType,sort:sort}
    )
  }
}
