import http from "@/utils/request";
// 系统配置  >  审核流程  api

export const applianceApi = {
	drugList: function (params) {
		return http.get("/erp/product/erpCommodity/list", params);
	},
	saveDrug: function (data) {
		return http.post("/erp/product/erpApparatusCommodity/save", data);
	},
	delDrug: function (params) {
		return http.delete("/erp/product/erpCommodity/delete", params);
	},
	searchDrug: function (params) {
		return http.get("/erp/product/erpApparatusCommodity/queryById", params);
	},
	offDrug: function (params) {
		return http.get("/erp/product/erpDrugsCommodity/updateEnable", params);
	},
	getPinyin: function (params) {
		return http.get("/erp/product/erpCommodity/getPinyin", params);
	},
	SelfCode: function (params) {
		return http.get(
			"/erp/product/erpCommodity/getCommoditySelfCode",
			params
		);
	},
	delFiles: function (params) {
		return http.delete("/erp/product/erpCommodity/updateEnable", params);
	},
	typeDrug: function (params) {
		return http.get("/sys/dictValue/list", params);
	},
	natureDrug: function (params) {
		return http.get("/erp/assist/erpBaseCommonValues/listByEnable", params);
	},
	venderDrug: function (params) {
		return http.get("/erp/factory/erpManufacturerInformation/list", params);
	},
	treeDrug: function (params) {
		return http.get("/erp/assist/erpMassRangeSet/list", params);
	},
	siteDrug: function (params) {
		return http.get(
			"/erp/factory/erpManufacturerInformation/queryAddByName",
			params
		);
	},
	TreesDrug: function (params) {
		return http.get("/erp/assist/erpMassRangeSet/treeData", params);
	},
};
