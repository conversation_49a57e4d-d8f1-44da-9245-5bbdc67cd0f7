import http from "@/utils/request"

/*
 *@description: 数据权限
 *@author: 路正宁
 *@date: 2023-03-17 11:23:07
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/dataRules/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/dataRules/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/dataRules/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/sys/dataRules/list',
      params,
      {}
    )
  },

  exportTemplate: function () {
    return http.get(
      '/sys/dataRules/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
     '/sys/dataRules/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/sys/dataRules/import',
      data
    )
  }
}
