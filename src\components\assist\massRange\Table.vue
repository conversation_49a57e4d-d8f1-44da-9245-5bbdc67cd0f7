<template>
	<div>
		<el-table
			:data="props.data.tableData"
			border
			default-expand-all
			row-key="id"
			style="width: 100%; margin-bottom: 20px"
		>
			<el-table-column align="center" label="范围名称" prop="name"/>
			<el-table-column label="范围编号" prop="date"/>

			<el-table-column align="状态" label="名称" prop="name"/>
			<el-table-column align="center" label="备注" prop="address"/>
			<el-table-column
				align="center"
				label="操作"
				prop="address"
				width="283px"
			>
				<el-button
					:icon="CirclePlus"
					text
					type="primary"
					@click="newAdd"
				>新增
				</el-button
				>
				<el-button
					icon="el-icon-edit"
					text
					type="success"
					@click="editData"
				>编辑
				</el-button
				>
				<el-button
					icon="el-icon-delete"
					text
					type="danger"
					@click="delData"
				>删除
				</el-button
				>
			</el-table-column>
		</el-table>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, toRefs, watchEffect,} from "vue";
import {useStore} from "vuex";
import {useRoute, useRouter} from "vue-router";
import {CirclePlus} from "@element-plus/icons-vue";

const props = defineProps({
	data: Object,
});
const emits = defineEmits(["newAdd", "editData", "delData"]);
const newAdd = () => {
	emits("newAdd");
};
const editData = () => {
	emits("editData");
};

const delData = () => {
	emits("delData");
};

/**
 * 仓库
 */
const store = useStore();
/**
 * 路由对象
 */
const route = useRoute();
/**
 * 路由实例
 */
const router = useRouter();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({});
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	...toRefs(data),
});
</script>
<style lang="less" scoped></style>
