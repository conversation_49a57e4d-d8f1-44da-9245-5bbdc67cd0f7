import http from "@/utils/request"

/*
 *@description: 短信邮件配置
 *@author: 路正宁
 *@date: 2023-03-17 11:28:38
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/esConfig/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/esConfig/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/esConfig/queryById',
      {id: id}
    )
  },

  treefunction () {
    return http.get(
      '/sys/esConfig/treeData',
    )
  }
}
