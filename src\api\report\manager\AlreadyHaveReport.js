import http from "@/utils/request"
//系统配置四性检测管理配置
export default {
	list: function (params) {
		return http.get(
			'/report/manager/mainInfo/list',
			params
		)
	},
	listByOpen: function (params) {
		return http.get(
			'/report/manager/mainInfo/listByOpen',
			params
		)
	},
	save: function (data) {
		return http.post(
			'/report/manager/mainInfo/save',
			data
		)
	},
	getInfoById: function (params) {
		return http.get(
			'/report/manager/mainInfo/queryById',
			params
		)
	},
	delete: function (data) {
		return http.delete(
			'/report/manager/mainInfo/delete',
			data
		)
	},
}
