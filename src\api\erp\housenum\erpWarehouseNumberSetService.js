import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/housenum/erpWarehouseNumberSet/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/housenum/erpWarehouseNumberSet/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/housenum/erpWarehouseNumberSet/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/housenum/erpWarehouseNumberSet/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/housenum/erpWarehouseNumberSet/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/housenum/erpWarehouseNumberSet/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/housenum/erpWarehouseNumberSet/import',
      data
    )
  }
}
