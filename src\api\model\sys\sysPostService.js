import http from "@/utils/request"

/*
 *@description: 职务管理
 *@author: 路正宁
 *@date: 2023-03-17 11:53:44
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/post/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/post/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/post/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/sys/post/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/sys/post/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/sys/post/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/sys/post/import',
      data
    )
  }
}
