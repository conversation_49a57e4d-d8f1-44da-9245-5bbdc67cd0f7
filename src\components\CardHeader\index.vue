<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-03-15 15:35:02
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-05-18 14:32:05
 * @FilePath: \tiantunui\src\components\CardHeader\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <div v-if="size == 'default'">
            <div slot="header">
                <div class="titleLayout">
                    <span class="verticalBar"></span>
                    <span class="title">{{ title }}</span>
                </div>
            </div>
            <div class="line" v-if="line"></div>
        </div>
        <div v-if="size == 'mini'">
            <div slot="header">
                <div class="titleLayout_mini">
                    <span class="verticalBar_mini"></span>
                    <span class="title_mini">{{ title }}</span>
                </div>
            </div>
            <div class="line_mini" v-if="line"></div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: ''
        },
        size: {
            type: String,
            default: 'default'
        },
        line:{
            type:Boolean,
            default: true
        }
    }
}
</script>

<style lang="scss" scoped>
.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 10px;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 16px;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
        font-size: 16px;
    }
}

.line {
    height: 1px;
    background-color: #e6ebf5;
    margin-bottom: 15px;
    width: calc(100% + 40px);
    margin-left: -20px;
}
.titleLayout_mini {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 5px;

    .verticalBar_mini {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title_mini {
        color: #5670fe;
        font-size: 14px;
    }
}

.line_mini {
    height: 1px;
    background-color: #e6ebf5;
    margin-bottom: 5px;
    width: calc(100% + 40px);
    margin-left: -20px;
}
</style>