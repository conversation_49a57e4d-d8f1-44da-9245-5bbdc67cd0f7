<template>
	<el-dialog
		v-model="visible"
		:title="titleName"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form
			ref="dialogForm"
			v-loading="dialogLoading"
			:disabled="mode == 'view'"
			:model="form"
			:rules="rules"
			label-width="100px"
		>
			<el-form-item label="上级区域" prop="name">
				<el-input v-model="form.parent.name" :disabled="true" clearable></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '机构名称不能为空', trigger: 'blur' }]"
				label="机构名称"
				prop="name"
			>
				<el-input v-model="form.name" clearable placeholder="请输入机构名称"></el-input>
			</el-form-item>

			<el-form-item
				:rules="[{ required: true, message: '联系人不能为空', trigger: 'blur' }]"
				label="联系人"
				prop="contact"
			>
				<el-input v-model="form.contact" clearable placeholder="请输入联系人"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '电话不能为空', trigger: 'blur' }]"
				label="电话"
				prop="phone"
			>
				<el-input v-model="form.phone" clearable placeholder="请输入电话"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '邮箱不能为空', trigger: 'blur' }]"
				label="邮箱"
				prop="email"
			>
				<el-input v-model="form.email" clearable placeholder="请输入邮箱"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '机构类型不能为空', trigger: 'blur' }]"
				label="机构类型"
				prop="type"
			>
				<el-input v-model="form.type" clearable placeholder="请输入机构类型"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '机构等级不能为空', trigger: 'blur' }]"
				label="机构等级"
				prop="grade"
			>
				<el-input v-model="form.grade" clearable placeholder="请输入机构等级"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '所在区域不能为空', trigger: 'blur' }]"
				label="所在区域"
				prop="areaList"
			>
				<sysAreaCascader
					v-model="form.areaList"
					placeholder="请选择所在区域"
					style="width: 100%;"
				></sysAreaCascader>
			</el-form-item>

			<el-form-item
				:rules="[{ required: true, message: '机构地址不能为空', trigger: 'blur' }]"
				label="机构地址"
				prop="address"
			>
				<el-input
					v-model="form.address"
					clearable
					placeholder="请输入机构地址"
				></el-input>
			</el-form-item>
			<el-form-item label="是否启用" prop="enable">
				<el-switch v-model="form.enable"/>
			</el-form-item>
			<el-form-item label="是否运营端" prop="operate">
				<el-switch v-model="form.operate"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button
				v-if="submitVisible"
				:loading="isSubmiting"
				type="primary"
				@click="submit()"
			>保 存
			</el-button
			>
		</template>
	</el-dialog>
</template>

<script>
export default {
	props: {
		//回调函数
		callback: {type: Function},
	},
	data() {
		return {
			//表单类型
			mode: "add",
			//表单标题
			titleName: "",
			//是否显示或隐藏表单弹框
			visible: false,
			//提交中
			isSubmiting: false,
			//弹框加载中
			dialogLoading: false,
			//是否隐藏提交按钮
			submitVisible: false,
			//表单数据
			form: {},
		};
	},
	mounted() {
	},
	methods: {
		/*
		 * 添加视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:15
		 */
		addView(parent) {
			//设置标题
			this.titleName = "添加";
			this.mode = "add";
			this.form = {
				parent: {
					id: "",
				},
				areaList: [],
			};
			//设置父级节点
			this.form.parent = parent;
			//处理顶级节点
			this.isParentNode();
			//显示表单
			this.visible = true;
			//显示提交按钮
			this.submitVisible = true;
		},
		/*
		 * 编辑视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:35
		 */
		async editView(formData) {
			//设置标题
			this.titleName = "编辑";
			this.mode = "edit";
			//显示表单
			this.visible = true;
			//显示提交按钮
			this.submitVisible = true;
			//释放提交等待
			this.isSubmiting = false;
			//页面加载中
			this.dialogLoading = true;
			//设置表单数据
			this.form = formData;
			//重新查询父级节点
			var res = await this.$API.sysOrgService.queryById(formData.id);
			if (res.code == 200) {
				//设置表单数据
				this.form = res.data;
			} else {
				this.$Response.errorNotice(res, "查询失败");
				//锁定提交按钮
				this.isSubmiting = true;
			}
			//处理顶级节点
			this.isParentNode();
			//所属区域数据处理
			if (this.$ObjectUtils.isNotEmpty(this.form.area)) {
				this.form.areaList = this.form.area.split(",");
			}
			//释放页面加载中
			this.dialogLoading = false;
		},
		/*
		 * 查看视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:21:14
		 */
		async view(formData) {
			//设置标题
			this.titleName = "查看";
			this.mode = "view";
			//显示表单
			this.visible = true;
			//隐藏提交按钮
			this.submitVisible = false;
			//页面加载中
			this.dialogLoading = true;
			//设置表单数据
			this.form = formData;
			//重新查询
			var res = await this.$API.sysOrgService.queryById(formData.id);
			if (res.code == 200) {
				//设置表单数据
				this.form = res.data;
			} else {
				this.$Response.errorNotice(res, "查询失败");
				//锁定提交按钮
				this.isSubmiting = true;
			}
			//所属区域数据处理
			if (this.$ObjectUtils.isNotEmpty(this.form.area)) {
				this.form.areaList = this.form.area.split(",");
			}
			//处理顶级节点
			this.isParentNode();
			//释放页面加载中
			this.dialogLoading = false;
		},
		/*
		 * 判断是否是顶级节点
		 * @author: 路正宁
		 * @date: 2023-04-03 14:06:46
		 */
		isParentNode() {
			if (
				this.$ObjectUtils.isEmpty(this.form.parent) ||
				this.$ObjectUtils.isEmpty(this.form.parent.id) ||
				this.form.parent.id == "0"
			) {
				//设置顶级节点
				this.form.parent = {
					id: "0",
					name: "顶级节点",
				};
				return true;
			} else {
				return false;
			}
		},
		/*
		 * 表单提交
		 * @author: 路正宁
		 * @date: 2023-03-24 14:11:20
		 */
		async submit() {
			//表单校验
			var valid = await this.$refs.dialogForm.validate().catch(() => {
			});
			if (!valid) {
				return false;
			}
			//锁定提交按钮
			this.isSubmiting = true;
			this.form.area = this.form.areaList.join(",");
			var res = await this.$API.sysOrgService.save(this.form);
			if (res.code == 200) {
				//关闭页面
				this.visible = false;
				this.$message.success("操作成功");
				//回调函数
				this.callback(res.data, this.mode);
			} else {
				this.$Response.errorNotice(res, "保存失败");
			}
			//释放提交按钮
			this.isSubmiting = false;
		},
	},
};
</script>

<style></style>
