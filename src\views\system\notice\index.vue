<template>
	<el-container>
		<el-header height="auto" style="background: none;border-bottom: none;padding: 10px 10px 5px 10px">
			<el-card style="height: 100%;width: 100%;padding: 10px 0;" :body-style="{ padding: '0 20px 0 20px' }">
				<el-form ref="queryRef" :inline="true" :model="queryParams" label-width="auto">
					<el-form-item label="公告标题" prop="noticeTitle" style="margin-top: 7px;margin-bottom: 7px;">
						<el-input v-model="queryParams.noticeTitle" clearable placeholder="请输入公告标题"
							style="width: 220px" />
					</el-form-item>
					<el-form-item label="公告内容" prop="noticeContent" style="margin-top: 7px;margin-bottom: 7px;">
						<el-input v-model="queryParams.noticeContent" clearable placeholder="请输入公告内容"
							style="width: 220px" />
					</el-form-item>
					<el-form-item label="公告类型" prop="noticeType" style="margin-top: 7px;margin-bottom: 7px;">
						<el-select v-model="queryParams.noticeType" clearable filterable placeholder="请选择公告类型"
							style="width: 220px">
							<el-option v-for="item in typeList" :key="item.value" :label="item.name"
								:value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="公告状态" prop="status" style="margin-top: 7px;margin-bottom: 7px;">
						<el-select v-model="queryParams.status" clearable filterable placeholder="请选择公告状态"
							style="width: 220px">
							<el-option v-for="item in statusList" :key="item.value" :label="item.name"
								:value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item style="margin-top: 7px;margin-bottom: 7px;">
						<el-button icon="el-icon-search" type="primary" @click="handleQuery">
							搜索
						</el-button>
						<el-button icon="el-icon-refresh-left" @click="resetQuery">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px">
			<el-card :body-style="{ height: '100%', width: '100%', padding: '20px 20px 0 20px' }"
				style="height: 100%;width: 100%">
				<el-container>
					<el-header height="auto" style="background: none;border-bottom: none;padding: 0 0 15px 0">
						<div style="display: flex;justify-content: space-between;height: 100%;width: 100%;">
							<div>
								<el-button icon="el-icon-plus" type="primary" @click="handleAdd">
									新增
								</el-button>
							</div>
							<div style="display: flex;justify-content: end;padding-right: 10px;align-items:center">
								<el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
									<el-icon style="font-size: 20px;margin-right: 10px;color: #2a76f8;cursor: pointer;"
										@click="refreshClick">
										<Refresh />
									</el-icon>
								</el-tooltip>
								<el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
									<el-icon style="font-size: 20px;color: #2a76f8;cursor: pointer;"
										@click="fullScreenClick">
										<FullScreen />
									</el-icon>
								</el-tooltip>
							</div>
						</div>
					</el-header>
					<el-main style="padding: 0">
						<el-table v-loading="loading" :data="list" border style="height: 100%;width: 100%">
							<el-table-column align="center" label="序号" prop="sort" width="80">
								<template #default="scope">
									<span>{{ (queryParams.current - 1) * queryParams.size + scope.$index + 1 }}</span>
								</template>
							</el-table-column>
							<el-table-column :show-overflow-tooltip="true" align="center" label="公告标题" prop="noticeTitle" min-width="200px" />
							<el-table-column align="center" label="公告类型" min-width="100px">
								<template #default="scope">
									<span>{{ formDict(typeList, scope.row.noticeType) }}</span>
								</template>
							</el-table-column>
							<el-table-column :show-overflow-tooltip="true" align="center" label="公告内容" min-width="200px" prop="noticeContent" />
							<el-table-column align="center" label="公告状态" min-width="100px">
								<template #default="scope">
									<span>{{ formDict(statusList, scope.row.status) }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="操作" min-width="200px" fixed="right">
								<template #default="scope">
									<el-button :disabled="scope.row.status == '2'" icon="el-icon-edit" link
										type="primary" @click="handleAdd(scope.row)">
										修改
									</el-button>
									<el-button icon="el-icon-delete" link type="danger"
										@click="handleDelete(scope.row)">
										删除
									</el-button>
									<!-- 发布按钮：未发布(1)和已撤销(3)状态时显示 -->
									<el-button v-if="scope.row.status == '1' || scope.row.status == '3'"
										icon="el-icon-folder-checked" link type="success" @click="release(scope.row)">
										发布
									</el-button>
									<!-- 撤销按钮：已发布(2)状态时显示 -->
									<el-button v-if="scope.row.status == '2'"
										icon="el-icon-folder-delete" link type="warning" @click="release(scope.row)">
										撤销
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
								:total="total" style="padding: 0" @pagination="getList" />
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-model="open" :title="title" align-center append-to-body width="32%">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="100px"
				style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="公告标题" prop="noticeTitle">
					<el-input v-model="form.noticeTitle" clearable placeholder="请输入公告标题" />
				</el-form-item>
				<el-form-item label="公告类型" prop="noticeType">
					<el-select v-model="form.noticeType" clearable filterable placeholder="请选择公告类型" style="width:100%">
						<el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="公告内容" prop="noticeContent">
					<el-input v-model="form.noticeContent" :autosize="{ minRows: 6 }" clearable placeholder="请输入公告内容"
						type="textarea" />
				</el-form-item>
				<el-form-item label="公告状态" prop="status">
					<el-select v-model="form.status" clearable filterable placeholder="请选择公告状态" style="width:100%">
						<el-option v-for="item in statusList" :key="item.value" :label="item.name"
							:value="item.value" />
					</el-select>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定</el-button>
					<el-button @click="() => open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import { getCurrentInstance, nextTick, onBeforeMount, reactive, ref, toRefs } from 'vue'
import sysNoticeService from '@/api/sys/sysNoticeService'
import { FullScreen, Refresh } from "@element-plus/icons-vue";
import tool from "@/utils/tool";

const { proxy } = getCurrentInstance();
//  const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
const list = ref([]);
const open = ref(false);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const statusList = ref([])
const typeList = ref([])
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 20,
	},
	rules: {
		noticeTitle: [{ required: true, message: "请输入公告标题", trigger: "blur" }],
		noticeType: [{ required: true, message: "请选择公告类型", trigger: "change" }],
		noticeContent: [{ required: true, message: "请输入公告内容", trigger: "blur" }],
		status: [{ required: true, message: "请选择公告状态", trigger: "change" }],
	},
});
const { queryParams, form, rules } = toRefs(data);

onBeforeMount(() => {
	dict();
	getList();
})

/** 查询角色列表 */
function getList() {
	loading.value = true
	sysNoticeService.list(queryParams.value).then(res => {
		if (res.code === 200) {
			list.value = res.data.records
			total.value = res.data.total
			loading.value = false
		}
	})
}

const formDict = (data, val) => {
	return proxy.selectDictLabel(data, val)
}

const refreshClick = () => {
	getList();
}

const fullScreenClick = () => {
	// tool.screen(document.querySelectorAll( '.' + className.value)[0])
	tool.screen(document.querySelectorAll('.aminui-body')[0])
}

/** 搜索按钮操作 */
function handleQuery() {
	getList();
}

const handleAdd = (row) => {
	row ? (title.value = '修改') : (title.value = '新增')
	if (row) {
		const { noticeTitle, noticeType, noticeContent, status, id } = row
		form.value = {
			noticeTitle,
			noticeType,
			noticeContent,
			status,
			id
		}
	} else {
		form.value = {}
	}
	open.value = true
	// 清除表单验证状态
	nextTick(() => {
		proxy.$refs["formRef"]?.clearValidate()
	})
}

/** 重置按钮操作 */
function resetQuery() {
	proxy.resetForm("queryRef");
	handleQuery();
}

function release(row) {
	sysNoticeService.release((row.status == '1' || row.status == '3') ? { id: row.id, status: '2' } : {
		id: row.id,
		status: '3'
	}).then(res => {
		if (res.code === 200) {
			proxy.msgSuccess(`${(row.status == '1' || row.status == '3') ? '发布' : '撤销'}成功`)
			getList()
		}
	})
}

function submitForm() {
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			sysNoticeService.save(form.value).then(res => {
				if (res.code == 200) {
					proxy.msgSuccess(`${title.value}成功`)
					open.value = false
					getList()
				}
			})
		}
	})
}

/** 删除按钮操作 */
function handleDelete(row) {
	proxy.$confirm('是否确认删除该数据项?', '提示', {
		cancelButtonText: '取消',
		confirmButtonText: '确认',
		type: 'warning'
	}).then(() => {
		sysNoticeService.delete({ ids: row.id }).then(res => {
			if (res.code == 200) {
				getList();
				proxy.msgSuccess("删除成功");
			}
		})  // 删除数据的接口
	}).catch(() => {
	});
}

async function dict() {
	statusList.value = await proxy.getDictList('sys_notice_status')
	typeList.value = await proxy.getDictList('sys_notice_type')
}
</script>
