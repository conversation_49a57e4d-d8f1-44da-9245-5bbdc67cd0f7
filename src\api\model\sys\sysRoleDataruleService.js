import http from "@/utils/request"

/*
 *@description: 角色数据权限
 *@author: 路正宁
 *@date: 2023-03-17 11:57:40
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/roleDatarule/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/roleDatarule/delete',
       {ids: ids}
    )
  },
  configDataRule: function (roleId,menuId,ruleDataIds) {
    return http.get(
       '/sys/roleDatarule/configDataRule',
       {roleId: roleId,menuId:menuId,ruleDataIds:ruleDataIds}
    )
  },
  queryById: function (id) {
    return http.get(
       '/sys/roleDatarule/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/roleDatarule/list',
       params
    )
  },

  exportTemplate: function () {
    return http.post(
       '/sys/roleDatarule/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/roleDatarule/export',
       params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/sys/roleDatarule/import',
       data
    )
  }
}
