import http from "@/utils/request"

/*
 *@description: 组织机构
 *@author: 路正宁
 *@date: 2023-03-17 11:45:24
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/org/save',
      inputForm
    )
  },
  list: function (params) {
    return http.get(
       '/sys/org/list',
       params
    )
  },
  delete: function (ids) {
    return http.delete(
      '/sys/org/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/org/queryById',
      {id: id}
    )
  },

  // treefunction () {
  //   return http.get(
  //     '/sys/org/treeData'
  //   )
  // }
}
