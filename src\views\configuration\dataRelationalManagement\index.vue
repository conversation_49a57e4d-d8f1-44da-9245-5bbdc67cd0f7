<template>
	<el-container v-loading="loading">
		<el-header height="auto" style="padding: 10px 10px 5px 10px;background: none;border-bottom: none">
			<el-card :body-style="{ height: '100%', width: '100%', display: 'flex', 'align-items': 'center' }"
					 style="height: 100%;width: 100%">
				<el-input v-model="dataNameSearch" class="w-50 m-2" placeholder="请输入需要查询的名称"
						  style="width: 521px;" @keydown.enter="selectList"/>
				<el-button icon="Search" style="margin-left: 20px;" type="primary" @click="selectList">
					查询
				</el-button>
				<el-button icon="RefreshRight" plain @click="() => {dataNameSearch = '';selectList();}">
					重置
				</el-button>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px;">
			<el-card :body-style="{ height: '100%', width: '100%', padding: '20px 20px 0 20px' }"
					 style="height: 100%;width: 100%;">
				<el-container>
					<el-header height="47px" style="padding: 0 0 15px 0">
						<el-button icon="Plus" plain type="primary" @click="createMethod">
							新增
						</el-button>
					</el-header>
					<el-main style="padding: 0">
						<el-table :data="dataList" border style="height: 100%;width: 100%;">
							<el-table-column align="center" label="序号" prop="sort" width="80">
								<template #default="scope">
									{{ scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="数据关系名称" prop="relationName"/>
							<el-table-column align="center" label="主数据分类" prop="masterClassify.name"/>
							<el-table-column align="center" label="主数据数据结构" prop="masterConfig.name"/>
							<el-table-column align="center" label="主数据数据字段" prop="masterDataName"/>
							<el-table-column align="center" label="数据关系描述" prop="relationContext"/>
							<el-table-column align="center" label="从数据分类" prop="slaveClassify.name"/>
							<el-table-column align="center" label="从数据数据结构" prop="slaveConfig.name"/>
							<el-table-column align="center" label="从数据数据字段" prop="slaveDataName"/>
							<el-table-column align="center" label="备注" prop="remark"/>
							<el-table-column align="center" class-name="fixed-width" label="操作" min-width="80">
								<template #default="scope">
									<el-button icon="Edit" link type="primary" @click="editInfo(scope.row)">
										编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="deleteInfo(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:page-sizes="[15,30,45,60]" :total="total" style="padding: 0"
										@pagination="selectList"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-if="open" v-model="open" :title="title" align-center append-to-body width="42%">
			<el-form ref="formRef" v-loading="loading" :model="form" :rules="rules" label-width="auto">
				<el-form-item label="数据关系名称" prop="relationName">
					<el-input v-model="form.relationName" placeholder="请输入数据关系名称"/>
				</el-form-item>
				<el-form-item label="主数据分类" prop="masterClassifyId">
					<el-tree-select v-model="form.masterClassifyId" :data="platformList"
									:props="{ value: 'id', label: 'name' }"
									highlight-current placeholder="请选择主数据分类"
									style="width: 100%;" @change="(val) => {
										dataPlatformChange(val,'1')
									}"/>
				</el-form-item>
				<el-form-item v-if="form.masterClassifyId" label="主数据数据结构" prop="masterConfigId">
					<el-tree-select v-model="form.masterConfigId" :data="dataMasterConfigList"
									:props="{ value: 'id', label: 'name' }" highlight-current
									placeholder="请选择主数据数据结构" style="width: 100%;"
									@change="(val) => {
										dataConfigChange(val,'1')
									}">
						<template #default="{ data }">
							<div style="display:flex;justify-content: space-between">
								<el-text>{{ data.name }}</el-text>
								<el-tag size="small">{{ data.versionName }}</el-tag>
							</div>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item v-if="form.masterConfigId" label="主数据数据字段" prop="masterDataName">
					<el-tree-select ref="masterDataName" v-model="form.masterDataName"
									:data="dataMasterConfigDetailsList"
									:load="selectDetailsList" :multiple="false" :props="{ value: 'id', label: 'remark', isLeaf: 'leaf' }"
									check-on-click-node highlight-current lazy node-key="id"
									placeholder="请选择主数据数据字段" show-checkbox
									style="width: 100%;">
						<template #default="{ data }">
							<div style="display:flex;justify-content: space-between">
								<el-text>{{ data.name }}</el-text>
								<el-tag size="small">{{ data.remark }}</el-tag>
							</div>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item label="数据关系描述" prop="relationContext">
					<el-input v-model="form.relationContext" placeholder="请输入数据关系描述"/>
				</el-form-item>
				<el-form-item label="从数据分类" prop="slaveClassifyId">
					<el-tree-select v-model="form.slaveClassifyId" :data="platformList"
									:props="{ value: 'id', label: 'name' }"
									highlight-current placeholder="请选择从数据分类"
									style="width: 100%;" @change="(val) => {
										dataPlatformChange(val,'2')
									}"/>
				</el-form-item>
				<el-form-item v-if="form.slaveClassifyId" label="从数据对应数据结构" prop="slaveConfigId">
					<el-tree-select v-model="form.slaveConfigId" :data="dataSlaveConfigList"
									:props="{ value: 'id', label: 'name' }"
									highlight-current placeholder="请选择从数据对应数据结构"
									style="width: 100%;"
									@change="(val) => {
										dataConfigChange(val,'2')
									}">
						<template #default="{ data }">
							<div style="display:flex;justify-content: space-between">
								<el-text>{{ data.name }}</el-text>
								<el-tag size="small">{{ data.versionName }}</el-tag>
							</div>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item v-if="form.slaveConfigId" label="从数据对应数据字段" prop="slaveDataName">
					<el-tree-select ref="slaveDataName" v-model="form.slaveDataName" :data="dataSlaveConfigDetailsList"
									:load="selectDetailsList" :multiple="false" :props="{ value: 'id', label: 'remark', isLeaf: 'leaf' }"
									check-on-click-node highlight-current lazy node-key="id"
									placeholder="请选择从数据对应数据字段" show-checkbox
									style="width: 100%;">
						<template #default="{ data }">
							<div style="display:flex;justify-content: space-between">
								<el-text>{{ data.name }}</el-text>
								<el-tag size="small">{{ data.remark }}</el-tag>
							</div>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="form.remark" :autosize="{minRows: 6}" clearable maxlength="500"
							  placeholder="请输入备注"
							  show-word-limit type="textarea"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitData">确 定</el-button>
					<el-button @click="() => {open = false;formRef.resetFields();}">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue';
import dataRelationalManager from '@/api/archive/dataRelationalManager';
import dataManager from "@/api/archive/dataManager";

const {proxy} = getCurrentInstance();
const data = reactive({
	form: {
		relationName: '',
		masterClassifyId: '',
		masterConfigId: '',
		masterDataName: '',
		relationContext: '',
		slaveClassifyId: '',
		slaveConfigId: '',
		slaveDataName: '',
		remark: ''
	},
	queryParams: {
		current: 1,
		size: 15,
	}
});
// 表单校验
const rules = reactive({
	relationName: [{required: true, message: '请输入数据关系名称', trigger: 'blur'},],
	masterClassifyId: [{required: true, message: '请选择主数据所属分类', trigger: 'change'},],
	masterConfigId: [{required: true, message: '请选择主数据所属数据结构', trigger: 'change'},],
	masterDataName: [{required: true, message: '请选择主数据所属数据字段', trigger: 'change'},],
	relationContext: [{required: true, message: '请输入数据关系描述', trigger: 'blur'},],
	slaveClassifyId: [{required: true, message: '请选择从数据所属分类', trigger: 'change'},],
	slaveConfigId: [{required: true, message: '请选择从数据所属数据结构', trigger: 'change'},],
	slaveDataName: [{required: true, message: '请选择从数据所属数据字段', trigger: 'change'},],
	remark: [{required: true, message: '请输入数据备注', trigger: 'blur'},],
})
const dataNameSearch = ref("");
const paramTitle = ref("");
const loading = ref(false);
const total = ref(0);
const {queryParams, fourConfig, form} = toRefs(data);
// 数据集合
const dataList = ref([]);
const paramForm = ref({});
const allTableList = ref([]);
const platformList = ref([]);
const dataMasterConfigList = ref([]);
const dataSlaveConfigList = ref([]);
const paramsList = ref([]);
const dataMasterConfigDetailsList = ref([]);
const dataSlaveConfigDetailsList = ref([]);
const menuList = ref([]);
//弹窗状态
const open = ref(false);
const paramOpen = ref(false);
const title = ref("");
const formRef = ref(null);
const paramFormRef = ref(null);
const masterDataName = ref(null);
const slaveDataName = ref(null);

onBeforeMount(() => {
	selectList();
	selectTableList();
});

//查询数据集合
function selectList() {
	loading.value = true
	dataRelationalManager.getList({
		relationName: dataNameSearch.value,
		current: queryParams.value.current,
		size: queryParams.value.size
	}).then(res => {
		if (res.code === 200) {
			dataList.value = res.data.records;
			total.value = res.data.total;
		} else {
			proxy.msgError(res.msg);
		}
		loading.value = false;
	})
}

//数据分类切换
function dataPlatformChange(data, type) {
	dataManager.versionList({
		platformId: data,
		current: 1,
		size: -1
	}).then((res) => {
		if (res.code === 200) {
			if (type === '1') {
				dataMasterConfigList.value = res.data.records;
			}
			if (type === '2') {
				dataSlaveConfigList.value = res.data.records;
			}
		}
	});
}

//数据结构切换
function dataConfigChange(data, type) {
	console.log(data, 'id');
	console.log(dataMasterConfigList.value, 'idlist');
	console.log(dataSlaveConfigList.value, 'idlist');
	let configId = '';
	if (type === '1') {
		dataMasterConfigList.value.forEach(item => {
			console.log(item.id === data);
			if (item.id === data) {
				configId = item.structureId;
			}
		});
	}
	if (type === '2') {
		dataSlaveConfigList.value.forEach(item => {
			console.log(item.id === data);
			if (item.id === data) {
				configId = item.structureId;
			}
		});
	}
	dataRelationalManager.getDetailsList({
		configId: configId,
		current: 0,
		size: -1
	}).then((res) => {
		if (res.code === 200) {
			if (type === '1') {
				dataMasterConfigDetailsList.value = res.data.records;
				dataMasterConfigDetailsList.value.forEach(item => {
					if (item.source === '2') {
						item.leaf = false;
					} else {
						item.leaf = true;
					}
				});
			}
			if (type === '2') {
				dataSlaveConfigDetailsList.value = res.data.records;
				dataSlaveConfigDetailsList.value.forEach(item => {
					if (item.source === '2') {
						item.leaf = false;
					} else {
						item.leaf = true;
					}
				});
			}
		}
	});
}

//数据深度查询
function selectDetailsList(node, resolve, reject) {
	if (!node.data.configId) return;
	dataManager.dataConfigList({
		id: node.data.configId
	}).then((res) => {
		if (res.code === 200) {
			let dataList = res.data;
			dataList.forEach(item => {
				item.children.forEach(child => {
					if (child.targetName === node.data.type) {
						dataRelationalManager.getDetailsList({
							configId: child.id,
							current: 0,
							size: -1
						}).then(details => {
							let detailList = details.data.records;
							detailList.forEach(deepChild => {
								if (deepChild.source === '2') {
									deepChild.leaf = false;
								} else {
									deepChild.leaf = true;
								}
							});
							resolve(detailList);
						});
					}
				});
			});
		}
	});
}

//新增数据
function createMethod() {
	title.value = '新增';
	form.value = {
		relationName: '',
		masterClassifyId: '',
		masterConfigId: '',
		masterDataName: '',
		relationContext: '',
		slaveClassifyId: '',
		slaveConfigId: '',
		slaveDataName: '',
		remark: ''
	};
	paramForm.value = {};
	paramsList.value = [];
	open.value = true;
}

//查询所有数据表
function selectTableList() {
	dataRelationalManager.getAllTableList().then((res) => {
		allTableList.value = res.data;
	});
	dataRelationalManager.classifyList().then((res) => {
		platformList.value = res.data;
	});
	dataRelationalManager.dataConfigList().then((res) => {
		dataMasterConfigList.value = res.data;
		dataSlaveConfigList.value = res.data;
	});
}

//数据提交
function submitData() {
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			dataRelationalManager.save({
				id: form.value.id,
				relationName: form.value.relationName,
				masterClassify: {
					id: form.value.masterClassifyId
				},
				masterConfig: {
					id: form.value.masterConfigId
				},
				masterDataId: masterDataName.value.getHalfCheckedKeys()[0]
					? masterDataName.value.getHalfCheckedKeys()[0] + ',' + masterDataName.value.getCheckedKeys()[0]
					: masterDataName.value.getCheckedKeys()[0],
				relationContext: form.value.relationContext,
				slaveClassify: {
					id: form.value.slaveClassifyId
				},
				slaveConfig: {
					id: form.value.slaveConfigId
				},
				slaveDataId: slaveDataName.value.getHalfCheckedKeys()[0]
					? slaveDataName.value.getHalfCheckedKeys()[0] + ',' + slaveDataName.value.getCheckedKeys()[0]
					: slaveDataName.value.getCheckedKeys()[0],
				remark: form.value.remark
			}).then((res) => {
				if (res.code === 200) {
					proxy.msgSuccess("操作成功");
					open.value = false;
					selectList();
					selectTableList();
					formRef.value.resetFields();
				}
			}).catch((err) => {
				console.log(err);
				proxy.msgError("操作失败");
			});
		}
	});
}

//修改数据
function editInfo(data) {
	form.value.id = data.id;
	form.value.relationName = data.relationName;
	form.value.masterClassifyId = data.masterClassify.id;
	form.value.masterConfigId = data.masterConfig.id;
	form.value.masterDataName = data.masterDataId;
	form.value.relationContext = data.relationContext;
	form.value.slaveClassifyId = data.slaveClassify.id;
	form.value.slaveConfigId = data.slaveConfig.id;
	form.value.slaveDataName = data.slaveDataId;
	form.value.remark = data.remark;
	dataManager.versionList({
		platformId: data.masterClassify.id,
		current: 1,
		size: -1
	}).then((res) => {
		if (res.code === 200) {
			dataMasterConfigList.value = res.data.records;
			dataSlaveConfigList.value = res.data.records;
			dataConfigChange(data.masterConfig.id, '1');
			dataConfigChange(data.slaveConfig.id, '2');
		}
	});
	title.value = '修改';
	open.value = true;
}

//删除数据
function deleteInfo(data) {
	dataRelationalManager.delete({
		ids: data.id
	}).then((res) => {
		if (res.code === 200) {
			proxy.msgSuccess("删除成功");
			selectList();
			selectTableList();
		}
	});
}
</script>

<style scoped>
</style>
