<template>
  <el-main>
    <el-card shadow="never">
      <el-tabs tab-position="top">
        <el-tab-pane label="系统设置">
          <el-form
            ref="form"
            :model="sys"
            label-width="100px"
            style="margin-top: 20px"
            v-loading="systemLoading"
          >
            <el-form-item label="系统名称">
              <el-input v-model="sys.sysName.value"></el-input>
            </el-form-item>
            <el-form-item label="LogoUrl">
              <el-input v-model="sys.sysLogoUrl.value"></el-input>
            </el-form-item>
            <el-form-item label="登录开关">
              <el-switch v-model="sys.sysLogin.value"></el-switch>
              <div class="el-form-item-msg" data-v-b33b3cf8="">
                关闭后普通用户无法登录，仅允许管理员角色登录
              </div>
            </el-form-item>
            <el-form-item label="密码验证规则">
              <el-input v-model="sys.loginPasswordRules.value"></el-input>
            </el-form-item>
            <el-form-item label="版权信息">
              <el-input
                type="textarea"
                :autosize="{ minRows: 4 }"
                v-model="sys.sysCopyright.value"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :loading="SysConfigSubmiting"
                @click="saveSysConfig()"
                >保存</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="短信配置">
          <el-form ref="form" :model="sms" label-width="100px" style="margin-top: 20px">
            <el-form-item label="短信开关">
              <el-switch v-model="sms.smsOpen.value"></el-switch>
              <div class="el-form-item-msg" data-v-b33b3cf8="">
                关闭后用户无法收到短信，但日志中将记录
              </div>
            </el-form-item>
            <el-form-item label="appKey">
              <el-input v-model="sms.smsAppKey.value"></el-input>
            </el-form-item>
            <el-form-item label="secretKey">
              <el-input v-model="sms.smsSecretKey.value"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :loading="SysConfigSubmiting"
                @click="saveSmsConfig()"
                >保存</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="扩展配置">
          <el-alert
            title="扩展配置为系统业务所有的配置，应该由系统管理员操作，如需用户配置应另起业务配置页面。"
            type="warning"
            style="margin-bottom: 15px"
          ></el-alert>

          <el-table :data="setting" stripe>
            <el-table-column label="#" type="index" width="50"></el-table-column>
            <el-table-column label="Key" prop="name" width="150">
              <template #default="scope">
                <el-input
                  v-if="scope.row.isSet"
                  v-model="scope.row.name"
                  placeholder="请输入内容"
                ></el-input>
                <span v-else>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="值" prop="value" width="350">
              <template #default="scope">
                <template v-if="scope.row.isSet">
                  <el-switch
                    v-if="typeof scope.row.value === 'boolean'"
                    v-model="scope.row.value"
                  ></el-switch>
                  <el-input
                    v-else
                    v-model="scope.row.value"
                    placeholder="请输入内容"
                  ></el-input>
                </template>
                <span v-else>{{ scope.row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类别" prop="category" width="150">
              <template #default="scope">
                <el-input
                  v-if="scope.row.isSet"
                  v-model="scope.row.category"
                  placeholder="请输入内容"
                ></el-input>
                <span v-else>{{ scope.row.category }}</span>
              </template>
            </el-table-column>
            <el-table-column label="描述" prop="description" width="350">
              <template #default="scope">
                <el-input
                  v-if="scope.row.isSet"
                  v-model="scope.row.description"
                  placeholder="请输入内容"
                ></el-input>
                <span v-else>{{ scope.row.description }}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="1"></el-table-column>
            <el-table-column label="操作" fixed="right" width="120">
              <template #default="scope">
                <el-button-group>
                  <el-button
                    @click="table_edit(scope.row, scope.$index)"
                    text
                    type="primary"
                    size="small"
                    >{{ scope.row.isSet ? "保存" : "修改" }}</el-button
                  >
                  <el-button
                    v-if="scope.row.isSet"
                    @click="scope.row.isSet = false"
                    text
                    type="primary"
                    size="small"
                    >取消</el-button
                  >
                  <el-popconfirm
                    v-if="!scope.row.isSet"
                    title="确定删除吗？"
                    @confirm="table_del(scope.row, scope.$index)"
                  >
                    <template #reference>
                      <el-button text type="primary" size="small">删除</el-button>
                    </template>
                  </el-popconfirm>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="table_add"
            style="margin-top: 20px"
          ></el-button>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </el-main>
</template>

<script>
export default {
  name: "system",
  data() {
    return {
      systemLoading: false,
      SysConfigSubmiting: false,
      sys: {
        sysName: { id: "", category: "system", name: "sysName", description: "系统名称" },
        sysLogoUrl: {
          id: "",
          category: "system",
          name: "sysLogoUrl",
          description: "系统Logo",
        },
        sysLogin: {
          id: "",
          category: "system",
          name: "sysLogin",
          description: "登录开关",
        },
        loginPasswordRules: {
          id: "",
          category: "system",
          name: "loginPasswordRules",
          description: "密码规则",
        },
        sysCopyright: {
          id: "",
          category: "system",
          name: "sysCopyright",
          description: "版权信息",
        },
      },
      sms: {
        smsOpen: { id: "", category: "sms", name: "smsOpen", description: "短信开关" },
        smsAppKey: {
          id: "",
          category: "sms",
          name: "smsAppKey",
          description: "smsAppKey",
        },
        smsSecretKey: {
          id: "",
          category: "sms",
          name: "smsSecretKey",
          description: "smsSecretKey",
        },
      },
      setting: [],
    };
  },
  mounted() {
    this.getSysConfig();
    this.getSmsConfig();
    this.getAttributeList();
  },
  methods: {
    /*
     * 获取系统配置
     * @author: 路正宁
     * @date: 2023-03-29 09:28:26
     */
    async getSysConfig() {
      var res = await this.$API.sysPropertiesService.list({
        category: "system",
        //当前页码
        current: 1,
        //每页条数
        size: 1000,
      });
      if (res.code == 200) {
        for (var key in res.data.records) {
          var item = res.data.records[key];
          if ("sysName" == item.name) {
            this.sys.sysName = item;
          }
          if ("sysLogoUrl" == item.name) {
            this.sys.sysLogoUrl = item;
          }
          if ("sysLogin" == item.name) {
            this.sys.sysLogin = item;
          }
          if ("loginPasswordRules" == item.name) {
            this.sys.loginPasswordRules = item;
          }
          if ("sysCopyright" == item.name) {
            this.sys.sysCopyright = item;
          }
        }
      }
    },
    /*
     * 提交系统配置
     * @author: 路正宁
     * @date: 2023-03-29 09:37:00
     */
    async saveSysConfig() {
      //锁定提交按钮
      this.SysConfigSubmiting = true;
      var res1 = await this.$API.sysPropertiesService.save(this.sys.sysName);
      var res2 = await this.$API.sysPropertiesService.save(this.sys.sysLogoUrl);
      var res3 = await this.$API.sysPropertiesService.save(this.sys.sysLogin);
      var res4 = await this.$API.sysPropertiesService.save(this.sys.loginPasswordRules);
      var res5 = await this.$API.sysPropertiesService.save(this.sys.sysCopyright);
      if (res1.code == res2.code && res3.code == res4.code && res5.code == 200) {
        this.$message.success("操作成功");
      } else {
        this.$Response.errorNotice(res5, "保存失败");
      }
      //释放提交按钮
      this.SysConfigSubmiting = false;
    },
    /*
     * 获取短信配置
     * @author: 路正宁
     * @date: 2023-03-29 10:15:19
     */
    async getSmsConfig() {
      var res = await this.$API.sysPropertiesService.list({
        category: "sms", //当前页码
        current: 1,
        //每页条数
        size: 1000,
      });
      if (res.code == 200) {
        for (var key in res.data.records) {
          var item = res.data.records[key];
          if ("smsOpen" == item.name) {
            this.sms.smsOpen = item;
          }
          if ("smsAppKey" == item.name) {
            this.sms.smsAppKey = item;
          }
          if ("smsSecretKey" == item.name) {
            this.sms.smsSecretKey = item;
          }
        }
      }
    },
    /*
     * 提交短信配置
     * @author: 路正宁
     * @date: 2023-03-29 09:37:00
     */
    async saveSmsConfig() {
      //锁定提交按钮
      this.SysConfigSubmiting = true;
      var res1 = await this.$API.sysPropertiesService.save(this.sms.smsOpen);
      var res2 = await this.$API.sysPropertiesService.save(this.sms.smsAppKey);
      var res3 = await this.$API.sysPropertiesService.save(this.sms.smsSecretKey);
      if (res1.code == res2.code && res3.code == 200) {
        this.$message.success("操作成功");
      } else {
        this.$Response.errorNotice(res3, "保存失败");
      }
      //释放提交按钮
      this.SysConfigSubmiting = false;
    },
    /*
     * 获取扩展配置
     * @author: 路正宁
     * @date: 2023-03-29 10:30:32
     */
    async getAttributeList() {
      var res = await this.$API.sysPropertiesService.list({
        //当前页码
        current: 1,
        //每页条数
        size: 100,
        category: "attribute",
      });
      if (res.code == 200) {
        this.setting = res.data.records;
      }
    },
    /*
     * 添加扩展属性
     * @author: 路正宁
     * @date: 2023-03-29 10:57:57
     */
    table_add() {
      var newRow = {
        name: "",
        value: "",
        title: "",
        category: "attribute",
        isSet: true,
      };
      this.setting.push(newRow);
    },
    /*
     * 编辑扩展属性
     * @author: 路正宁
     * @date: 2023-03-29 10:58:16
     */
    async table_edit(row, index) {
      if (row.isSet == true) {
        var res = await this.$API.sysPropertiesService.save(row);
        if (res.code == 200) {
          this.setting[index] = res.data;
          this.$message.success("操作成功");
        } else {
          this.$Response.errorNotice(res, "操作失败");
        }
      }
      this.setting[index].isSet = !row.isSet;
    },
    /*
     * 删除扩展属性
     * @author: 路正宁
     * @date: 2023-03-29 10:58:29
     */
    async table_del(row, index) {
      var res = await this.$API.sysPropertiesService.delete(row.id);
      if (res.code == 200) {
        this.setting.splice(index, 1);
        this.$message.success("操作成功");
      } else {
        this.$Response.errorNotice(res, "操作失败");
      }
    },
  },
};
</script>

<style></style>
