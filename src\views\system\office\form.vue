<template>
  <el-dialog
    :title="titleName"
    v-model="visible"
    :width="800"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      v-loading="dialogLoading"
      :model="form"
      :rules="rules"
      :disabled="mode == 'view'"
      ref="dialogForm"
      label-width="100px"
    >
      <el-form-item label="上级部门" prop="name">
        <el-input v-model="form.parent.name" clearable :disabled="true"></el-input>
      </el-form-item>
      <el-form-item
        label="所属机构"
        prop="sysOrg.name"
        :rules="[{ required: true, message: '所属机构不能为空', trigger: 'blur' }]"
      >
        <div style="display: inline-flex">
          <el-input
            v-model="form.sysOrg.name"
            placeholder="请输入所属机构"
            clearable
            disabled
          ></el-input>
          <el-button type="text" style="padding-bottom: 0px" @click="openSysOrgDialog"
            >选择组织机构</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="部门名称"
        prop="name"
        :rules="[{ required: true, message: '部门名称不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.name" placeholder="请输入部门名称" clearable></el-input>
      </el-form-item>

      <el-form-item
        label="负责人"
        prop="contact"
        :rules="[{ required: true, message: '负责人不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.contact" placeholder="请输入负责人" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="电话"
        prop="phone"
        :rules="[{ required: true, message: '电话不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.phone" placeholder="请输入电话" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="邮箱"
        prop="email"
        :rules="[{ required: true, message: '邮箱不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.email" placeholder="请输入邮箱" clearable></el-input>
      </el-form-item>
      <el-form-item label="是否启用" prop="enable">
        <el-switch v-model="form.enable" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="submitVisible"
        type="primary"
        :loading="isSubmiting"
        @click="submit()"
        >保 存</el-button
      >
    </template>
    <sysOrgSelect
      ref="sysOrgSelected"
      v-if="sysOrgSelectDialog"
      :isMultiple="false"
      draggable
      @closed="sysOrgSelectDialog = false"
      :selectChange="orgSelectChange"
    ></sysOrgSelect>
  </el-dialog>
</template>

<script>
export default {
  props: {
    //回调函数
    callback: { type: Function },
  },
  data() {
    return {
      //表单类型
      mode: "add",
      //表单标题
      titleName: "",
      //是否显示或隐藏表单弹框
      visible: false,
      //提交中
      isSubmiting: false,
      //弹框加载中
      dialogLoading: false,
      //是否隐藏提交按钮
      submitVisible: false,
      //表单数据
      form: {},
      //
      sysOrgSelectDialog: false,
    };
  },
  mounted() {},
  methods: {
    /*
     * 添加视图
     * @author: 路正宁
     * @date: 2023-03-24 13:20:15
     */
    addView(sysOrg, parent) {
      //设置标题
      this.titleName = "添加";
      //表单类型
      this.mode = "add";
      //初始化表单数据结构
      this.form = {
        parent: {
          id: "",
        },
        sysOrg: {
          id: "",
          name: "",
        },
      };
      //设置父级节点
      this.form.parent = parent;
      //顶级节点处理
      this.isParentNode();
      //组织机构处理
      if (this.$ObjectUtils.isEmpty(sysOrg)) {
        this.form.sysOrg = { id: "", name: "" };
      } else {
        this.form.sysOrg = sysOrg;
      }
      //显示表单
      this.visible = true;
      //显示提交按钮
      this.submitVisible = true;
    },

    /*
     * 编辑视图
     * @author: 路正宁
     * @date: 2023-03-24 13:20:35
     */
    async editView(formData) {
      //设置标题
      this.titleName = "编辑";
      //设置表单类型
      this.mode = "edit";
      //显示表单
      this.visible = true;
      //显示提交按钮
      this.submitVisible = true;
      //释放提交等待
      this.isSubmiting = false;
      //页面加载中
      this.dialogLoading = true;
      //重新查询数据
      var res = await this.$API.sysOfficeService.queryById(formData.id);
      if (res.code == 200) {
        //设置表单数据
        this.form = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
        //锁定提交按钮
        this.isSubmiting = true;
        return;
      }
      //顶级节点处理
      this.isParentNode();
      //释放页面加载中
      this.dialogLoading = false;
    },
    /*
     * 查看视图
     * @author: 路正宁
     * @date: 2023-03-24 13:21:14
     */
    async view(formData) {
      //设置标题
      this.titleName = "查看";
      this.mode = "view";
      //显示表单
      this.visible = true;
      //隐藏提交按钮
      this.submitVisible = false;
      //页面加载中
      this.dialogLoading = true;
      //设置表单数据
      this.form = formData;
      //重新查询
      var res = await this.$API.sysOfficeService.queryById(this.form.id);
      if (res.code == 200) {
        //设置表单数据
        this.form = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
        //锁定提交按钮
        this.isSubmiting = true;
      }
      //顶级节点处理
      this.isParentNode();
      //释放页面加载中
      this.dialogLoading = false;
    },
    /*
     * 打开组织机构选择框
     * @author: 路正宁
     * @date: 2023-04-03 10:27:55
     */
    openSysOrgDialog() {
      var datas = [];
      datas.push(this.form.sysOrg);
      this.sysOrgSelectDialog = true;
      this.$nextTick(() => {
        this.$refs.sysOrgSelected.selecteds(datas);
      });
    },

    /*
     * 组织机构弹框回调选择事件
     * @author: 路正宁
     * @date: 2023-04-03 10:02:56
     */
    orgSelectChange(orgList) {
      if (orgList.length > 0) {
        this.form.sysOrg = orgList[0];
      } else {
        this.form.sysOrg = {
          id: "",
          name: "",
        };
      }
    },
    /*
     * 判断是否是顶级节点
     * @author: 路正宁
     * @date: 2023-04-03 13:24:11
     */
    isParentNode() {
      if (
        this.$ObjectUtils.isEmpty(this.form.parent) ||
        this.$ObjectUtils.isEmpty(this.form.parent.id) ||
        this.form.parent.id == "0"
      ) {
        //设置顶级节点
        this.form.parent = {
          id: "0",
          name: "顶级节点",
        };
        return true;
      } else {
        return false;
      }
    },
    /*
     * 表单提交
     * @author: 路正宁
     * @date: 2023-03-24 14:11:20
     */
    async submit() {
      //表单校验
      var valid = await this.$refs.dialogForm.validate().catch(() => {});
      if (!valid) {
        return false;
      }
      //锁定提交按钮
      this.isSubmiting = true;
      var res = await this.$API.sysOfficeService.save(this.form);
      if (res.code == 200) {
        //关闭页面
        this.visible = false;
        this.$message.success("操作成功");
        //回调函数
        this.callback(res.data, this.mode);
      } else {
        this.$Response.errorNotice(res, "保存失败");
      }
      //释放提交按钮
      this.isSubmiting = false;
    },
  },
};
</script>

<style></style>
