<template>
	<el-container>
		<el-aside v-loading="menuLoading" style="border-right: 0;padding: 10px 0 10px 10px;background: none"
				  width="21%">
			<el-card :body-style="{ padding: '6px 0px 0px 0px' }" style="width: 100%;height: 100%;overflow-y: scroll">
				<el-tree ref="menu" :data="menuList" :expand-on-click-node="false" check-strictly class="menu" default-expand-all
						 draggable highlight-current node-key="id" @node-click="menuClick">
					<template #default="{ data }">
						<span class="custom-tree-node">
							<span class="label">
								{{ data.name }}
							</span>
						</span>
					</template>
				</el-tree>
			</el-card>
		</el-aside>
		<el-main ref="main" style="padding: 0">
			<el-container>
				<el-header height="auto" style="padding: 10px;background: none;border-bottom: none">
					<el-card :body-style="{ height: '100%', width: '100%' }" style="height: 100%;width:100%">
						<el-form :model="form" ref="formList" label-position="left" :inline="true" label-width="auto">
							<el-form-item label="档案名称" prop="name" style="margin-bottom: 0;width:32%">
								<el-input v-model="form.name" placeholder="请输入档案名称" clearable
										  @keydown.enter="handleQuery"/>
							</el-form-item>
							<el-form-item style="margin-bottom: 0;">
								<el-button icon="Search" type="primary" @click="handleQuery">
									查询
								</el-button>
								<el-button icon="RefreshRight" plain @click="resetQuery">
									重置
								</el-button>
							</el-form-item>
						</el-form>
					</el-card>
				</el-header>
				<el-main style="padding: 0 10px 10px 10px">
					<el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }"
							 style="height: 100%;width:100%">
						<el-container>
							<el-header height="auto" style="padding: 0 0 10px 0">
								<div style="height: 100%;width:100%;display: flex;justify-content:space-between;
									align-items:center;">
									<el-button icon="Files" plain type="primary" @click="handleExport">
										合并成件
									</el-button>
									<div style="display: flex;justify-content: flex-end;align-items: center;">
										<div style="margin-right: 15px;" @click="getList">
											<el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
												<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
													<Refresh/>
												</el-icon>
											</el-tooltip>
										</div>
										<div @click="screen">
											<el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
												<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
													<FullScreen/>
												</el-icon>
											</el-tooltip>
										</div>
									</div>
								</div>
							</el-header>
							<el-main style="padding: 0">
								<el-table v-loading="dataLoading" :data="receiveData" border
										  style="height: 100%;width:100%"
										  @sort-change="tableSortChange"
										  @selection-change="handleSelectionChange">
									<el-table-column align="center"
													 type="selection"
													 width="50"/>
									<el-table-column align="left"
													 label="档案名称"
													 sortable="custom"
													 prop="name"/>
									<el-table-column align="center"
													 label="保留年限"
													 prop="retentionPeriod"
													 width='100'>
										<template #default="scope">
											<span>{{ originalRetention(scope.row.retentionPeriod) }}</span>
										</template>
									</el-table-column>
									<el-table-column align="center"
													 label="接收时间"
													 prop="createDate"
													 sortable="custom"
													 width='132'>
										<template #default="scope">
											{{ moment(scope.row.createDate).format('YYYY年MM月DD日') }}
										</template>
									</el-table-column>
									<el-table-column align="center"
													 label="来源"
													 prop="remark"
													 width='200'/>
									<el-table-column align="center"
													 class-name="small-padding fixed-width"
													 fixed="right"
													 label="操作"
													 width="120">
										<template #default="scope">
											<el-button icon="View" link type="primary" @click="receiveView(scope.row)">
												查看
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</el-main>
							<el-footer>
								<div style="display: flex;justify-content: flex-end">
									<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :page-sizes="[20,40,60,80]"
												:total="total" style="padding: 0"
												@pagination="getList"/>
								</div>
							</el-footer>
						</el-container>
					</el-card>
				</el-main>
			</el-container>
		</el-main>

		<!-- 查看 -->
		<el-dialog v-if="open" v-model="open" :title="title" align-center append-to-body status_change top="8vh"
				   width="92%">
			<receive :receiveId="receiveId"/>
		</el-dialog>
		<!-- 关联 -->
		<el-dialog v-if="openAssociation" v-model="openAssociation" :title="title" append-to-body width="1300px">
			<association :relevance="relevance" style="height: 600px;" @childRelevance="parentRelevance"/>
		</el-dialog>
		<el-dialog v-if="RelevanceFileList" v-model="RelevanceFileList" :title="title" append-to-body
				   style="min-height: 600px;" width="1300px">
			<relevanceFile :relevanceList="relevanceList"/>
		</el-dialog>
		<!-- 合并 -->
		<el-dialog v-if="openMerge" v-model="openMerge" :title="title" append-to-body custom-class="dialogNoPadding"
				   width="600px">
			<el-divider style="margin: 0;"/>
			<merge :handList="handList" style="height: 600px;" @childEvent="parentMethod"/>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {FullScreen, Refresh} from '@element-plus/icons-vue'
import receive from './receive.vue'
import association from './association.vue'
import merge from './merge.vue'
import relevanceFile from './relevanceFile.vue'
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
// 查询分类
import classificationManagement from "@/api/archive/classificationManagement"
import receiveList from "@/api/archive/archiveReception/receive"
import moment from "moment/moment";
import tool from "@/utils/tool";

const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	}
});
const receiveId = ref();
const total = ref(0);
const {queryParams} = toRefs(data);
const {proxy} = getCurrentInstance();
const menuList = ref([]);
// 接收库List
const receiveData = ref([]);
// 树状列表查询
const menuLoading = ref(false);
const dataLoading = ref(false);
// 点击查询列表
const openTree = ref(true);
const receiveDataList = ref([]);
const originalClassifyId = ref("");
const handList = ref([]);
const openMerge = ref(false);
// 头部查询
const form = ref([]);
// 有关联数据后弹窗显示关联数据
const RelevanceFileList = ref(false);
const relevanceList = ref([]);
// 关联数据
const relevance = ref([]);
//查看弹窗状态
const open = ref(false);
//排序字段
const sortParam = ref('');
// 查看标题
const title = ref('');
// 关联状态
const openAssociation = ref(false);

onMounted(() => {
	treeData();
	getList();
});

// 查询树状列表
function treeData() {
	menuLoading.value = true;
	classificationManagement.treeData().then(res => {
		if (res.code === 200) {
			menuList.value = res.data;
			menuLoading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

function menuClick(data) {
	dataLoading.value = true;
	receiveDataList.value = data;
	originalClassifyId.value = data.id;
	getList();
}

// 查看数据
function receiveView(data) {
	receiveId.value = data.id;
	open.value = true;
	title.value = '查看';
}

function associationList(val) {
	receiveList.queryById({
		current: 1,
		size: -1,
		fileOriginalId: val.id
	}).then(res => {
		if (res.code === 200) {
			if (res.data.records.length == 0) {
				openAssociation.value = true;
				title.value = '关联';
				relevance.value = val;
			} else {
				proxy.msgError('该条数据已有关联文件！');
				getRelevanceFileList(res.data.records[0])
			}
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

function getRelevanceFileList(val) {
	relevanceList.value = val;
	RelevanceFileList.value = true;
	title.value = '已关联数据';
}

function parentRelevance() {
	openAssociation.value = false;
	menuClick(receiveDataList.value);
}

// 保留年限字段筛选
function originalRetention(val) {
	if (val == 'Y') {
		return '永久'
	} else if (val == 'D5') {
		return '5年'
	} else if (val == 'D10') {
		return '10年 '
	} else if (val == 'D20') {
		return '20年'
	} else if (val == 'D30') {
		return '30年'
	}
}

function handleQuery() {
	getList();
}

// 重置
function resetQuery() {
	form.value = [];
	getList();
}

function handleExport() {
	if (handList.value.length > 0) {
		openMerge.value = true;
		title.value = '合并成件';
	} else {
		proxy.msgError('请先选择合并的数据！');
	}

}

function handleSelectionChange(val) {
	handList.value = val;
}

function tableSortChange(data) {
	sortParam.value = '';
	if (data.order) {
		sortParam.value = data.prop + ':' + data.order;
	}
	handleQuery();
}

function parentMethod() {
	openMerge.value = false;
	menuClick(receiveDataList.value);
}

// 进入时查询全部
function getList() {
	dataLoading.value = true;
	receiveList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		name: form.value.name,
		sortParam: sortParam.value ? sortParam.value : null,
		'classifyConfig.id': originalClassifyId.value ? originalClassifyId.value : null
	}).then(res => {
		if (res.code === 200) {
			openTree.value = true;
			receiveData.value = res.data.records;
			total.value = res.data.total
		}
		dataLoading.value = false;
	}).catch(() => {
		dataLoading.value = false;
		proxy.msgError('查询失败');
	})
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}
</script>

<style scoped>
.myHeader {
	font-size: 16px;
	padding: 15px 0 0 20px;
}

:deep(.el-dialog__body) {
	padding: 0;
}

.dialogNoPadding {
	padding: 0;
}
</style>
