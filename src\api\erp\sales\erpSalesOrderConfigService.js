import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/sales/erpSalesOrderConfig/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/sales/erpSalesOrderConfig/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/sales/erpSalesOrderConfig/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrderConfig/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/sales/erpSalesOrderConfig/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrderConfig/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/sales/erpSalesOrderConfig/import',
      data
    )
  }
}
