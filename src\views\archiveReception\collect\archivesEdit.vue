<template>
	<el-dialog
		v-if="props.isOpen"
		v-model="props.isOpen"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:title="props.title"
		align-center
		append-to-body
		destroy-on-close
		width="83%"
		@close="cancellation"
	>
		<el-container>
			<el-main ref="main" v-loading="loading" class="noPadding">
				<el-tabs
					v-model="activeName"
					:before-leave="isBefore"
					class="demo-tabs"
				>
					<el-tab-pane
						label="档案信息"
						name="first"
						style="height: 63vh; overflow-y: scroll"
					>
						<el-main class="noPadding">
							<el-form
								ref="formRef"
								:inline="true"
								:model="archivesData"
								:rules="rules"
								label-width="auto"
							>
								<el-form-item
									label="档案名称:"
									prop="name"
									style="width: 388px"
								>
									<el-input
										v-model="archivesData.name"
										placeholder="请输入档案名称"
									/>
								</el-form-item>
								<el-form-item
									label="档案年份:"
									prop="year"
									style="width: 388px"
								>
									<el-date-picker
										v-model="archivesData.year"
										placeholder="请选择档案年份"
										style="width: 100%"
										type="year"
									/>
								</el-form-item>
								<el-form-item
									label="档案月份:"
									prop="month"
									style="width: 388px"
								>
									<el-date-picker
										v-model="archivesData.month"
										format="MM"
										placeholder="请选择档案月份"
										style="width: 100%"
										type="month"
									/>
								</el-form-item>
								<el-form-item
									label="文件日期:"
									style="width: 388px"
								>
									<el-date-picker
										v-model="archivesData.createDate"
										format="YYYY-MM-DD"
										style="width: 100%"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</el-form-item>
								<el-form-item
									label="保留年限:"
									prop="retentionPeriod"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.retentionPeriod"
										placeholder="请选择保留年限"
										style="width: 100%"
									>
										<el-option label="永久" value="Y" />
										<el-option label="5年" value="D5" />
										<el-option label="10年" value="D10" />
										<el-option label="20年" value="D20" />
										<el-option label="30年" value="D30" />
									</el-select>
								</el-form-item>
								<el-form-item
									label="保密密级:"
									prop="protectLevel"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.protectLevel"
										placeholder="请选择保密密级"
										style="width: 100%"
									>
										<el-option label="公开" value="GK" />
										<el-option label="限制" value="KZ" />
										<el-option label="秘密" value="MOM" />
										<el-option label="机密" value="JM" />
										<el-option label="绝密" value="UM" />
									</el-select>
								</el-form-item>
								<el-form-item
									label="控制等级:"
									prop="controlStatus"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.controlStatus"
										placeholder="请选择控制等级"
										style="width: 100%"
										@change="controlGroupChange"
									>
										<el-option label="公开" value="1" />
										<el-option
											label="公司内部开放"
											value="2"
										/>
										<el-option
											label="部门内部开放"
											value="3"
										/>
										<el-option
											:disabled="!archivesData?.org?.id"
											label="控制"
											value="4"
										>
											<el-text v-if="archivesData?.org?.id"
												>控制</el-text
											>
											<el-tooltip
												v-else
												content="请先选择组织机构"
												effect="dark"
												placement="right"
											>
												<el-text type="info"
													>控制</el-text
												>
											</el-tooltip>
										</el-option>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="archivesData.controlStatus === '4'"
									label="可查看人员:"
									prop="ruleUserId"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.ruleUserId"
										collapse-tags
										collapse-tags-tooltip
										multiple
										placeholder="请选择可查看人员"
										style="width: 100%"
									>
										<el-option
											v-for="item in userByOrg"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									label="所属机构:"
									prop="org.id"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.org.id"
										placeholder="请选择所属机构"
										style="width: 100%"
										@change="departmentList"
									>
										<el-option
											v-for="item in institution"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									label="所属部门:"
									prop="office.id"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.office.id"
										placeholder="请选择所属部门"
										style="width: 100%"
									>
										<el-option
											v-for="item in department"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									label="是否全电文档:"
									prop="isElectronic"
									style="width: 388px"
								>
									<el-select
										v-model="archivesData.isElectronic"
										placeholder="请选择是否全电文档"
										style="width: 100%"
										@change="changeStatus"
									>
										<el-option label="否" value="0" />
										<el-option label="是" value="1" />
									</el-select>
								</el-form-item>
								<el-form-item
									label="附件数量:"
									prop="infoOfFileCount"
									style="width: 388px"
								>
									<el-input
										v-model="archivesData.infoOfFileCount"
										disabled
										placeholder="请输入附件数量"
									/>
								</el-form-item>
								<el-form-item
									label="档案标签:"
									prop="tagManagerInfo"
								>
                                    <div class="tag-selection-container">
									<el-button plain type="primary" @click="openTagsChose = true" style="width: 200px" >选择标签</el-button>
                                        <!-- 已选择标签的回显 -->
                                        <div v-if="selectedTags.length > 0" class="selected-tags">
                                            <template v-for="tag in tagsList">
                                                <el-tag
                                                    v-if="selectedTags.includes(tag.id)"
                                                    :key="tag.id"
                                                    :effect="judgeTheme(tag.tagTheme).type"
                                                    :round="judgeShape(tag.tagRound).type"
                                                    :type="judgeColor(tag.tagColor).type"
                                                    closable
                                                    @close="removeTag(tag.id)"
                                                    class="selected-tag"
                                                >
                                                    {{ tag.tagName }}
                                                </el-tag>
                                            </template>
                                        </div>
                                    </div>
									<tagsListDialog
										v-if="openTagsChose"
										:choseTagsId="
											archivesData.tagManagerInfo
										"
										:dialogIsOpen="openTagsChose"
										@choseTagMethodReturn="
											choseTagMethodReturn
										"
									></tagsListDialog>
								</el-form-item>
								<el-form-item
									label="档案摘要 :"
									prop="digestContent"
									style="width: 96.8%"
								>
									<el-input
										v-model="archivesData.digestContent"
										:autosize="{ minRows: 5 }"
										clearable
										maxlength="500"
										placeholder="请输入档案摘要"
										show-word-limit
										style="width: 100%"
										type="textarea"
									/>
								</el-form-item>
								<el-form-item
									label="备注 :"
									prop="remark"
									style="width: 96.8%"
								>
									<el-input
										v-model="archivesData.remark"
										:autosize="{ minRows: 5 }"
										clearable
										maxlength="500"
										placeholder="请输入备注"
										show-word-limit
										style="width: 100%"
										type="textarea"
									/>
								</el-form-item>
							</el-form>
						</el-main>
					</el-tab-pane>
					<el-tab-pane
						label="数据信息"
						name="second"
						style="height: 63vh; overflow-y: scroll"
					>
						<div style="padding: 0 0 15px 0; font-weight: 600">
							{{ "基本信息" }}
						</div>
						<el-form
							ref="formBasic"
							:inline="true"
							:label-position="labelPosition"
							:model="dataInfoList"
							label-width="auto"
						>
							<div
								style="
									width: 100%;
									display: grid;
									grid-template-columns: 1fr 1fr 1fr;
									justify-items: center;
								"
							>
								<div
									v-for="(item, index) in dataInfoList.filter(
										(item) => !item.children
									)"
									:key="index"
								>
									<el-form-item
										v-if="item?.config?.type === '1'"
										:label="item.config.remark"
										style="width: 388px"
									>
										<el-input
											v-model="item.value"
											:placeholder="item.config.remark"
											clearable
										/>
									</el-form-item>
									<el-form-item
										v-if="item?.config?.type === '2'"
										:label="item.config.remark"
										style="width: 388px"
									>
										<el-upload
											:action="uploadUrl"
											:headers="headers"
											:on-success="
												(res, file, fileList) =>
													(item.value = res.data.url)
											"
											:show-file-list="false"
											class="avatar-uploader"
										>
											<img
												v-if="item"
												:src="item.value"
												alt=""
												class="avatar"
												style="
													height: 30px;
													width: 30px;
													border: 1px solid ghostwhite;
												"
											/>
											<i
												v-else
												class="el-icon-plus avatar-uploader-icon"
											/>
										</el-upload>
									</el-form-item>
									<el-form-item
										v-if="item?.config?.type === '4'"
										:label="item.config.remark"
										style="width: 388px"
									>
										<el-date-picker
											v-model="item.value"
											:placeholder="item.config.remark"
											:format="item.config.dataConfigValue"
											style="width: 100%"
											type="date"
											:value-format="item.config.dataConfigValue"
										/>
									</el-form-item>
									<el-form-item
										v-if="item?.config?.type === '5'"
										:label="item.config.remark"
										style="width: 388px"
									>
										<el-radio-group v-model="item.value">
											<el-radio label="是" />
											<el-radio label="否" />
										</el-radio-group>
									</el-form-item>
									<el-form-item
										v-if="item?.config?.type === 'address'"
										:label="item.config.remark"
										style="width: 388px"
									>
										<el-cascader
											v-model="item.value"
											:options="dataList"
											:props="configProps"
											style="width: 100%"
										/>
									</el-form-item>
								</div>
							</div>
							<div
								v-for="(item, index) in dataInfoList.filter(
									(item) => item.children
								)"
								:key="index"
							>
								<el-divider
									style="margin: 0 0 18px 0; width: 100%"
								/>
								<div
									style="
										display: flex;
										justify-content: space-between;
										align-items: center;
										margin-bottom: 18px;
									"
								>
									<h4 style="font-weight: 600">
										{{ item.config.remark }}
									</h4>
									<div
										v-if="
											archivesData?.controlCategory
												?.name !== '人员档案'
										"
									>
										<el-button
											plain
											type="primary"
											@click="addOtherOneInfo(item)"
										>
											添加
										</el-button>
									</div>
								</div>
								<el-divider
									style="margin: 14px 0 16px 0; width: 100%"
								/>
								<EditRecursiveInfoForm :childrenInfo="item" />
							</div>
						</el-form>
					</el-tab-pane>
					<el-tab-pane label="附件" name="three" style="height: 63vh">
						<div v-if="tableData.length > 0">
							附件总数: {{ tableData.length }}
						</div>
						<el-container class="resizable-container">
							<el-aside
								:width="leftWidth + 'px'"
								style="padding-left: 0px"
							>
								<div
									v-for="item in tableData"
									:key="item"
									class="fileUrl"
									@click="handleViewFile(item.fileUrl)"
								>
									<div
										style="
											display: flex;
											justify-content: space-between;
										"
									>
										<el-tooltip
											:content="item.fileName"
											class="box-item"
											effect="dark"
											placement="right"
										>
											<el-text
												style="cursor: pointer"
												truncated
											>
												{{ item.fileName }}
											</el-text>
										</el-tooltip>
										<el-button
											icon="Delete"
											link
											type="danger"
											@click="handleDelete(item)"
										/>
									</div>
								</div>
								<div style="margin: 20px">
									<el-upload
										v-model:file-list="fileList"
										:accept="accept"
										:action="uploadUrl"
										:before-remove="beforeRemove"
										:headers="headers"
										:on-exceed="handleExceed"
										:on-progress="uploadVideoProcess"
										:on-success="
											(res, file, filList) =>
												handleUploadSuccess(
													res,
													file,
													filList
												)
										"
										class="upload-demo"
										drag
										multiple
										style="height: 100px"
									>
										<el-icon class="el-icon--upload">
											<upload-filled />
										</el-icon>
										<div
											class="el-upload__text"
											style="font-size: 12px"
										>
											将文件拖到此处，或<em>点击上传</em>
										</div>
									</el-upload>
								</div>
							</el-aside>
							<!-- 拖动条 -->
							<div
								class="resize-handle"
								@mousedown="startResize"
							></div>
							<el-main
								style="background-color: #e4e7ed; padding: 5px"
							>
								<el-scrollbar
									ref="scrollbar"
									style="border-radius: 5px"
								>
									<div
										v-if="tableData.length > 0"
										ref="main"
										v-loading="pdfLoading"
										style="width: 100%; height: 100%"
									>
										<PDFViewer
											:src="pdfRef"
											height="100%"
											pageScale="page-fit"
											theme="light"
											width="100%"
											@loaded="onLoaded"
                                            @error="onPdfError"
										/>
									</div>
                                    <div
                                        v-else-if="tableData?.length > 0 && pdfError"
                                        style="
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;
								height: 100%;
								padding: 20px;
							"
                                    >
                                        <el-result
                                            :icon="pdfErrorType === 'download' ? 'info' : 'warning'"
                                            :title="pdfErrorType === 'download' ? 'PDF文件仅支持下载' : 'PDF预览失败'">
                                            <template #sub-title>
                                                <div v-if="pdfErrorType === 'download'">
                                                    <p>该PDF文件被服务器配置为下载模式，无法在线预览</p>
                                                    <p>请点击下载按钮获取文件后查看</p>
                                                </div>
                                                <div v-else-if="pdfErrorType === 'network'">
                                                    <p>PDF文件被浏览器阻止加载，可能的解决方案：</p>
                                                    <ul style="text-align: left; margin: 10px 0;">
                                                        <li>检查浏览器的广告拦截器设置</li>
                                                        <li>刷新页面重新加载</li>
                                                        <li>检查网络连接</li>
                                                    </ul>
                                                </div>
                                                <div v-else-if="pdfErrorType === 'notfound'">
                                                    <p>PDF文件不存在或已被删除</p>
                                                    <p>请联系管理员确认文件状态</p>
                                                </div>
                                                <div v-else-if="pdfErrorType === 'permission'">
                                                    <p>没有权限访问该PDF文件</p>
                                                    <p>请联系管理员获取访问权限</p>
                                                </div>
                                                <div v-else>
                                                    <p>PDF文件无法在浏览器中预览，可能的原因：</p>
                                                    <ul style="text-align: left; margin: 10px 0;">
                                                        <li>服务器配置问题</li>
                                                        <li>文件格式不兼容</li>
                                                        <li>网络连接异常</li>
                                                    </ul>
                                                </div>
                                            </template>
                                            <template #extra>
                                                <el-button
                                                    v-if="pdfErrorType !== 'notfound'"
                                                    type="primary"
                                                    @click="downloadPdf">
                                                    下载文件
                                                </el-button>
                                            </template>
                                        </el-result>
                                    </div>
									<div
										v-else
										style="
											display: flex;
											flex-direction: row;
											flex-wrap: nowrap;
											align-content: center;
											justify-content: center;
											align-items: center;
										"
									>
										<el-result icon="info" title="温馨提醒">
											<template #sub-title>
												<p>此档案无相关附件</p>
											</template>
										</el-result>
									</div>
								</el-scrollbar>
							</el-main>
						</el-container>
					</el-tab-pane>
				</el-tabs>
			</el-main>
		</el-container>
		<template #footer>
			<div
				v-if="activeName === 'three'"
				style="float: right; margin-left: 20px"
			>
				<el-button type="primary" @click="() => determine()"
					>确定</el-button
				>
			</div>
			<div
				v-if="activeName !== 'three'"
				style="float: right; margin-left: 20px"
			>
				<el-button type="primary" @click="() => nextStep()"
					>下一步</el-button
				>
			</div>
			<div
				v-if="activeName !== 'first'"
				style="float: right; margin-left: 20px"
			>
				<el-button type="primary" @click="() => nextBack()"
					>上一步</el-button
				>
			</div>
			<div style="float: right">
				<el-button plain @click="() => cancellation()">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import sysOrgService from "@/api/model/sys/sysOrgService";
import sysOfficeService from "@/api/model/sys/sysOfficeService";
import sysAreaService from "@/api/model/sys/sysAreaService";
import completeManagement from "@/api/archive/systemConfiguration/completeManagement";
import { UploadFilled } from "@element-plus/icons-vue";
import view from "@/api/archive/managementFile";
import collectList from "@/api/archive/archiveReception/collect";
import tagsListDialog from "@/views/archiveReception/common/tagsListDialog.vue";
import {
	defineProps,
	getCurrentInstance,
	onBeforeUnmount,
	reactive,
	ref,
	watch,
} from "vue";
import tool from "@/utils/tool";
import EditRecursiveInfoForm from "@/views/archiveReception/collect/editRecursiveInfoForm.vue";
import moment from "moment";
import { node } from "@/api/model/systemDeploy/auditAndFlow";
import PDFViewer from "@/views/archiveReception/common/PDFViewer.vue";
import tagsManagement from "@/api/archive/tagsManagement";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["childEvent"]);
const activeName = ref("first");
const props = defineProps({
	title: {
		type: String,
		value: "",
	},
	isOpen: {
		type: Boolean,
		value: false,
	},
	receiveInfo: {
		type: Object,
		value: {},
	},
	accept: {
		type: String,
		default:
			"image/gif,image/jpeg,image/png,image/jpg,application/msword,application/msword,text/plain,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf",
	},
});
// 信息判断List
const receiveData = ref([]);
// 原名称
const originalName = ref("");
//form 表单对齐方式
const labelPosition = ref("left");
// 表选选择状态
const openTagsChose = ref(false);
const list = ref([]);
const pdfRef = ref("");
const loading = ref(true);
const pdfLoading = ref(true);
const pdfError = ref(false);
const pdfErrorType = ref(''); // 错误类型：'download', 'network', 'permission', 'notfound', 'unknown'
//标签信息集合
const tagsList = ref([])
// 已选择的标签
const selectedTags = ref([])
// 接收库List
const dataInfoList = ref([]);
//记录等数据集合
const historyList = ref([]);
// 左侧面板初始宽度
const leftWidth = ref(300);

// 拖动相关状态
const isResizing = ref(false);
const startPosition = { x: 0 };

function startResize(e) {
	isResizing.value = true;
	startPosition.x = e.clientX;

	// 添加全局事件监听
	window.addEventListener("mousemove", handleMouseMove);
	window.addEventListener("mouseup", stopResize);

	// 防止文本选中
	document.body.style.userSelect = "none";
}

function handleMouseMove(e) {
	if (!isResizing.value) return;

	// 计算新的宽度
	const dx = e.clientX - startPosition.x;
	const newWidth = leftWidth.value + dx;

	// 设置最小和最大宽度限制
	if (newWidth > 100 && newWidth < window.innerWidth - 100) {
		leftWidth.value = newWidth;
	}

	// 更新起始位置
	startPosition.x = e.clientX;
}

function stopResize() {
	isResizing.value = false;

	// 移除全局事件监听
	window.removeEventListener("mousemove", handleMouseMove);
	window.removeEventListener("mouseup", stopResize);

	// 恢复文本选中
	document.body.style.userSelect = "";
}

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
	window.removeEventListener("mousemove", handleMouseMove);
	window.removeEventListener("mouseup", stopResize);
});
//表单校验
const rules = reactive({
	name: [
		{ required: true, message: "请输入档案名称", trigger: "blur" },
		{
			validator: async (rule, value, callback) => {
				await handleQuery(value);
				if (originalName.value === value) {
					callback();
				} else if (receiveData.value.length > 0) {
					callback(new Error("此名称已有重复, 请重新输入!"));
				} else {
					callback();
				}
			},
		},
	],
	digestContent: [
		// {required: true, message: '请输入档案摘要', trigger: 'blur'},
	],
	year: [
		{ required: true, message: "请选择档案年份", trigger: "blur" },
		{
			validator: async (rule, value, callback) => {
				await handleQuery(archivesData.value?.name);
				if (receiveData.value.length <= 0) {
					proxy.$refs["formRef"]?.clearValidate("name");
				}
				callback();
			},
		},
	],
	month: [
		{ required: true, message: "请输入档案月份", trigger: "blur" },
		{
			validator: async (rule, value, callback) => {
				await handleQuery(archivesData.value?.name);
				if (receiveData.value.length <= 0) {
					proxy.$refs["formRef"]?.clearValidate("name");
				}
				callback();
			},
		},
	],
	retentionPeriod: [
		{ required: true, message: "请选择保留年限", trigger: "change" },
	],
	createDate: [
		{ required: true, message: "请选择文件日期", trigger: "blur" },
	],
	controlGroup: {
		id: [{ required: true, message: "请输入全宗号", trigger: "blur" }],
	},
	controlStatus: [
		{ required: true, message: "请选择控制等级", trigger: "change" },
	],
	office: {
		id: [{ required: true, message: "请输入所属部门", trigger: "change" }],
	},
	protectLevel: [
		{ required: true, message: "请选择保密密级", trigger: "change" },
	],
	org: {
		id: [{ required: true, message: "请选择所属机构", trigger: "change" }],
	},
	isElectronic: [
		{ required: true, message: "请选择是否电子档案", trigger: "blur" },
	],
});
// 附件上传
const uploadUrl = process.env.VUE_APP_API_UPLOAD;
const fileList = ref([]);
const scrollbar = ref(null);
const headers = {
	Authorization: "Bearer " + tool.cookie.get("TOKEN"),
	ContentType: "multipart/form-data",
	clientType: "PC",
};
const videoUploadPercent = ref();
const fileUrl = ref([]);
const configProps = {
	value: "id",
	label: "name",
	children: "children",
	leaf: "leaf",
	lazy: false,
};
// 档案信息
const archivesData = ref({
    tagManagerInfo: '',
    org:{
        id:''
    },
    office:{
        id:''
    }
});
const tableData = ref([]);
const dataList = ref([]);
// 选择部门
const department = ref([]);
// 所属机构
const institution = ref([]);
const userByOrg = ref([]);
const state = reactive({
	pageNum: 1, //当前页面
	scale: 1, // 缩放比例
	numPages: 0, // 总页数
	loading: false, //加载效果
	rotation: 0, // 旋转角度
});

watch(
	() => props.isOpen,
	() => {
		activeName.value = "first";
		pdfRef.value = "";
		fileUrl.value = [];
		fileList.value = [];
        selectedTags.value = []; // 清空已选择标签
		institutionList();
		getInfoByMasterId();
		fileFist();
		queryById();
		getList();
		getDataList();
        getTagsList();
	},
	{ deep: true }
);
// 监听标签数据变化，用于编辑时的回显
watch(() => archivesData.value.tagManagerInfo, (newTagIds) => {
    if (newTagIds && typeof newTagIds === 'string') {
        selectedTags.value = newTagIds.split(',').filter(id => id.trim() !== '');
    }
}, { immediate: true });


//文件上传成功
const handleUploadSuccess = (res, file, fileList) => {
	if (res.code == 200) {
		fileList.value = [{ ...res.data }];
		fileUrl.value.push(fileList.value[0].url);
	}
};

function changeStatus(value) {
	if (value === "0") {
		archivesData.value.boxStatus = 2;
	} else if (value === "1") {
		archivesData.value.boxStatus = 1;
	}
}

function onLoaded(pdfApp) {
	pdfLoading.value = false;
	console.log("loaded app:", pdfApp);
}
function onPdfError(error) {
    pdfLoading.value = false;
    pdfError.value = true;
    console.error('PDF加载失败:', error);

    // 检查错误类型
    const errorMessage = error?.message || error?.toString() || JSON.stringify(error) || '';
    console.log('错误信息详情:', errorMessage);

    if (errorMessage.includes('ERR_BLOCKED_BY_CLIENT') || errorMessage.includes('net::')) {
        pdfErrorType.value = 'network';
        proxy.msgError('PDF文件被浏览器阻止加载，请检查广告拦截器设置或尝试刷新页面');
    } else if (errorMessage.includes('404')) {
        pdfErrorType.value = 'notfound';
        proxy.msgError('PDF文件不存在或已被删除');
    } else if (errorMessage.includes('403')) {
        pdfErrorType.value = 'permission';
        proxy.msgError('没有权限访问该PDF文件');
    } else {
        pdfErrorType.value = 'download';
        // 默认认为是服务器配置问题（下载模式）
        proxy.msgWarning('PDF文件无法在线预览，可能是服务器配置为下载模式。请使用下载功能查看文件。');
    }
}


function getInfoByMasterId() {
	view.getDataById({
		id: props.receiveInfo.id,
	})
		.then((res) => {
			if (res.code === 200) {
				dataInfoList.value = sortTreeNodes(res.data);
				//保存记录等数据
				historyList.value = dataInfoList.value.filter((label) => {
					return (
                        label?.name && (
						label.name === "操作记录" ||
						label.name === "供应商审核记录" ||
						label.name.includes("审核记录")
                        )
					);
				});
				//循环去除数据
				dataInfoList.value = dataInfoList.value.filter((label) => {
					return (
                        label?.name &&
						label.name !== "操作记录" &&
						label.name !== "供应商审核记录" &&
						!label.name.includes("审核记录")
					);
				});
				loading.value = false;
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

/**
 * 递归去除隐藏字段
 * @param data
 * @returns {*}
 */
function sortTreeNodes(data) {
    // 首先对当前层级的节点进行排序
    data.sort((a, b) => a?.config?.sort - b?.config?.sort);
    // 过滤不显示的内容
    data = data.filter(item => item?.config?.isView !== '1');

    // 遍历当前层级的每个节点
    for (let node of data) {
        // 如果当前节点有子节点
        if (node.children) {
            if (Array.isArray(node.children) && !Array.isArray(node.children[0])) {
                // 如果子节点是一维数组
                node.children = sortTreeNodes(node.children);
            } else if (Array.isArray(node.children) && Array.isArray(node.children[0])) {
                // 如果子节点是二维数组
                node.children = node.children.map(childArray => sortTreeNodes(childArray));
            }
        }
    }

    return data;
}

function isBefore(nowName, oldName) {
	return new Promise((resolve, reject) => {
		if (oldName === "first") {
			proxy.$refs["formRef"].validate((valid) => {
				if (valid) {
                    if (nowName === "three") {
                        if (tableData.value.length > 0) {
                            if (!pdfRef.value) {
                                pdfLoading.value = true;
                                pdfRef.value = tableData.value[0].fileUrl;
                            }
                        } else {
                            pdfLoading.value = false;
                        }
                        resolve();
                    }
				} else {
					proxy.msgError("请填写完毕当页数据后再切换");
					reject();
				}
			});
		} else if (nowName === "three") {
			if (tableData.value.length > 0) {
				if (!pdfRef.value) {
					pdfLoading.value = true;
					pdfRef.value = tableData.value[0].fileUrl;
				}
			} else {
				pdfLoading.value = false;
			}
			resolve();
		} else {
			resolve();
		}
	});
}

// 查询全宗
function getList() {
	completeManagement
		.getList({
			current: 1,
			size: -1,
		})
		.then((res) => {
			if (res.code == 200) {
				list.value = res.data.records;
			}
		});
}

//标签选择回调方法
function choseTagMethodReturn(data) {
	archivesData.value.tagManagerInfo = data.join(",");
	openTagsChose.value = false;
}

function fileFist() {
	collectList
		.fileFist({
			"recordInfo.id": props.receiveInfo.id,
			fileType: "pdf",
			current: 1,
			size: -1,
		})
		.then((res) => {
			if (res.code === 200) {
				tableData.value = res.data.records;
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

function queryById() {
	view.queryById({
		id: props.receiveInfo.id,
		showDeleteInfo: true,
	})
		.then((res) => {
			if (res.code === 200) {
				loading.value = false;
				archivesData.value = res.data;
				archivesData.value.ruleUserId = archivesData.value?.ruleUserId
					? archivesData.value.ruleUserId?.split(",")
					: "";
				originalName.value = archivesData.value?.name;
				if (archivesData.value.controlStatus === "4") {
					node.staff({ "sysOrg.id": archivesData.value?.org?.id }).then(
						(res) => {
							userByOrg.value = res.data.records;
						}
					);
				}
                if(!archivesData.value?.org?.name) archivesData.value.org.name = '';
                if(!archivesData.value?.office?.name) archivesData.value.office.name = '';
				departmentList(archivesData.value?.org?.id);
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

// 查看pdf
function handleViewFile(url) {
	if (pdfRef.value !== url) {
		pdfLoading.value = true;
		pdfRef.value = url;
		state.pageNum = 1;
		state.scale = 1;

		// const loadingTask = createLoadingTask(pdfRef.value);
		// state.loading = true; // 添加一个loading状态
		// loadingTask.promise.then((pdf) => {
		// 	state.numPages = pdf.numPages;
		// 	// 加载完成后将loading状态设置为false
		// 	state.loading = false;
		// });
	}
}

//查询地域信息
function getDataList() {
	//初始化数据列表
	dataList.value = [];
	//请求接口
	sysAreaService.treeDataByParent({ parentId: "100000" }).then((res) => {
		if (res.code === 200) {
			dataList.value = res.data;
		}
	}).catch(()=>{
        proxy.msgError("查询失败");
    });
}

//添加数据结构
function addOtherOneInfo(data) {
	let newArray = JSON.parse(JSON.stringify(data.children[0]));
	newArray.forEach((item) => {
		item.id = "";
		item.value = "";
	});
	data.children.push(newArray);
	proxy.msgSuccess("添加成功");
}

// //递归数据结构
// function deleteData(data, deleteDataIds) {
// 	data.children.forEach((item) => {
// 		deleteDataIds += item.id + ",";
// 		if (item.children) {
// 			deleteData(item.children, deleteDataIds);
// 		}
// 	});
// }
// 删除附件
function beforeRemove(file){
    fileUrl.value = fileUrl.value.filter(item=>item !== file?.response?.data?.url)
}
// 删除附件
function handleDelete(val) {
	proxy
		.$confirm("是否要删除附件?", "提示", {
			type: "warning",
			confirmButtonText: "确定",
			cancelButtonText: "取消",
		})
		.then(() => {
			collectList
				.fileDelete({
					ids: val.id,
				})
				.then((res) => {
					if (res.code === 200) {
						fileFist();
						proxy.msgSuccess("删除成功");
					}
				})
				.catch(() => {
					proxy.msgError("删除失败");
				});
		})
		.catch((error) => {
			console.log(error);
		});
}

// 取消
function cancellation() {
	loading.value = false;
	emit("childMove");
}

// 下一步
function nextStep() {
	if (activeName.value === "first") {
		proxy.$refs["formRef"].validate((valid) => {
			if (valid) {
				activeName.value = "second";
			} else {
				proxy.msgError("请填写完毕当页数据后再点击下一步");
			}
		});
	} else if (activeName.value === "second") {
		proxy.$refs["formBasic"].validate((valid) => {
			if (valid) {
				activeName.value = "three";
			} else {
				proxy.msgError("请填写完毕当页数据后再点击下一步");
			}
		});
	}
}

// 上一步
function nextBack() {
	if (activeName.value === "second") {
		activeName.value = "first";
	} else if (activeName.value === "three") {
		activeName.value = "second";
	}
}

//查询信息
async function handleQuery(value) {
	collectList
		.list({
			current: 1,
			size: -1,
			namePrecise: value,
			year: archivesData.value.year
				? moment(archivesData.value.year).format("YYYY")
				: null,
			month: archivesData.value.month
				? moment(archivesData.value.month).format("MM")
				: null,
			delFlag: 0,
		})
		.then((res) => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

//文件上传
function uploadVideoProcess(event) {
	videoUploadPercent.value = Math.floor(event.percent);
}

// 确定修改
function determine() {
	loading.value = true;
	saveList();
}

// 档案信息
function saveList() {
	let data = {};
	data.id = props?.receiveInfo?.id;
	data.name = archivesData.value?.name;
	data.groupId = archivesData.value?.controlGroup.id;
	data.digestContent = archivesData.value?.digestContent;
	data.year = moment(archivesData.value?.year).format("YYYY");
	data.month = moment(archivesData.value?.month).format("MM");
	data.createDate = archivesData.value?.createDate;
	data.retentionPeriod = archivesData.value?.retentionPeriod;
	data.protectLevel = archivesData.value?.protectLevel;
	data.isElectronic = archivesData.value?.isElectronic;
	data.controlStatus = archivesData.value?.controlStatus;
	data.org = {
		id: archivesData.value?.org?.id,
	};
	data.office = {
		id: archivesData.value?.office?.id,
	};
	data.tagManagerInfo = archivesData.value?.tagManagerInfo;
	data.infoOfFileCount = archivesData.value?.infoOfFileCount;
	data.remark = archivesData.value?.remark;
	data.ruleUserId =
		archivesData.value.controlStatus === "4"
			? archivesData.value.ruleUserId.join(",")
			: null;
	collectList
		.save(data)
		.then((res) => {
			if (res.code === 200) {
				saveInfoData();
			}
		})
		.catch(() => {
			loading.value = false;
			proxy.msgError("档案信息编辑失败");
		});
}

// 基本信息
function saveInfoData() {
	historyList.value.forEach((item) => {
		dataInfoList.value.push(item);
	});
	collectList
		.updateJsonData({
			recordId: props?.receiveInfo?.id,
			dataValueList: dataInfoList.value,
		})
		.then((res) => {
			if (res.code === 200) {
				uploadFile();
			}
		})
		.catch(() => {
			loading.value = false;
			proxy.msgError("基本信息编辑失败");
		});
}

function institutionList() {
	sysOrgService
		.list({
			current: 1,
			size: -1,
		})
		.then((res) => {
			if (res.code === 200) {
				institution.value = res.data.records;
			}
		})
		.catch((error) => {
			console.log(error);
		});
}

function departmentList(val) {
	if (val) {
		sysOfficeService
			.list({
				current: 1,
				size: 10,
				"sysOrg.id": val,
				"pparent.id": 0,
			})
			.then((res) => {
				if (res.code === 200) {
					department.value = res.data.records;
				}
			})
			.catch(() => {
				proxy.msgError("val");
			});
	} else {
		sysOfficeService
			.list({
				current: 1,
				size: 10,
				"sysOrg.id": archivesData.value?.org.id,
				"pparent.id": 0,
			})
			.then((res) => {
				if (res.code === 200) {
					department.value = res.data.records;
				}
			})
			.catch(() => {
				proxy.msgError("val-null");
			});
	}
}

// 附件上传
function uploadFile() {
	if (fileUrl.value.length > 0) {
		collectList
			.infoFileUpload({
				recordInfo: { id: props.receiveInfo.id },
				urlList: fileUrl.value,
			})
			.then((res) => {
				if (res.code === 200) {
					proxy.msgSuccess("档案信息编辑成功");
					cancellation();
				}
			})
			.catch(() => {
				loading.value = false;
				proxy.msgError("上传失败");
			});
	} else {
		proxy.msgSuccess("档案信息编辑成功");
		cancellation();
	}
}

//指定档案可查看人员
function controlGroupChange(chooseValue) {
	if (chooseValue === "4") {
		node.staff({ "sysOrg.id": archivesData.value?.org?.id }).then((res) => {
			userByOrg.value = res.data.records;
		});
	}
}

// 获取新增数据结构
function getTagsList() {
    tagsManagement.getList({
        current: 1,
        size: -1
    }).then(res => {
        if (res.code === 200) {
            tagsList.value = res.data.records;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}
// 删除标签
function removeTag(tagId) {
    // 从已选择标签中移除
    selectedTags.value = selectedTags.value.filter(tag => tag !== tagId);
    // 更新标签ID字符串
    const remainingTagIds = selectedTags.value.map(tag => tag);
    archivesData.value.tagManagerInfo = remainingTagIds.join(',');
}

// 判断主题
function judgeTheme(type) {
    if (type === '1') {
        return {label: '深色', type: 'dark'};
    } else if (type === '2') {
        return {label: '浅色', type: 'light'};
    } else {
        return {label: '默认', type: 'plain'};
    }
}

// 判断形状
function judgeShape(type) {
    if (type === '1') {
        return {label: '圆角', type: false};
    } else if (type === '2') {
        return {label: '椭圆', type: true};
    } else {
        return {label: '圆角', type: false};
    }
}

// 判断颜色
function judgeColor(type) {
    if (type === '1') {
        return {label: '蓝色', type: ""};
    } else if (type === '2') {
        return {label: '绿色', type: 'success'};
    } else if (type === '3') {
        return {label: '灰色', type: 'info'};
    } else if (type === '4') {
        return {label: '红色', type: 'danger'};
    } else if (type === '5') {
        return {label: '橙色', type: 'warning'};
    } else {
        return {label: '蓝色', type: ""};
    }
}

</script>
<style scoped>
.el-dialog__header {
	padding-bottom: 0;
}

.el-dialog__body {
	padding-top: 0;
}

.demo-image__error .image-slot {
	font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
	font-size: 30px;
}

.demo-image__error .el-image {
	width: 100%;
	height: 200px;
}

.fileUrl {
	cursor: pointer;
	border-top: 1px solid #e4e7ed;
	border-left: 1px solid #e4e7ed;
	border-bottom: 1px solid #e4e7ed;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.resizable-container {
	position: relative;
	display: flex;
	height: 100%;
}

.resize-handle {
	width: 6px;
	height: 100%;
	background-color: #e0e0e0;
	cursor: col-resize;
	position: relative;
	z-index: 10;
}

.resize-handle:hover,
.resize-handle.resizing {
	background-color: #1890ff;
}

.fileUrl {
	cursor: pointer;
	border-top: 1px solid #e4e7ed;
	border-left: 1px solid #e4e7ed;
	border-bottom: 1px solid #e4e7ed;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

p {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

:deep(.el-scrollbar__view) {
	width: 100%;
	height: 100%;
}

.tag-selection-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.selected-tag {
    margin: 0;
    transition: all 0.3s ease;
}

.selected-tag:hover {
    transform: scale(1.05);
}
</style>
