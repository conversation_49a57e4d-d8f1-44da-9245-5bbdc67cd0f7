<!--
 * @Descripttion: 系统计划任务配置
 * @version: 1.2
 * @Author: sakuya
 * @Date: 2021年7月7日09:28:32
 * @LastEditors: sakuya
 * @LastEditTime: 2021年7月10日20:56:47
-->
<template>
	<el-dialog v-model="visible"
			   :close-on-click-modal="false"
			   :title="titleMap[mode]"
			   :width="400"
			   destroy-on-close
			   @closed="$emit('closed')"
			   align-center
	>
		<el-form
			ref="dialogForm"
			:model="form"
			:rules="rules"
			label-position="left"
			label-width="100px"
		>
			<el-form-item label="描述" prop="name">
				<el-input v-model="form.name" clearable placeholder="计划任务标题"></el-input>
			</el-form-item>
			<el-form-item label="执行类" prop="executeClass">
				<el-input v-model="form.executeClass" clearable placeholder="计划任务执行类名称"/>
			</el-form-item>
			<el-form-item label="是否带参" prop="value">
				<el-select v-model="value" placeholder="是否带参数" style="width: 100%">
					<el-option label="是" value="0"/>
					<el-option label="否" value="1"/>
				</el-select>
			</el-form-item>
			<el-form-item v-if="value === '0'" label="参数" prop="param">
				<el-input v-model="form.param" clearable placeholder="请输入参数"/>
			</el-form-item>
			<el-form-item label="定时规则" prop="timingRules">
				<sc-cron v-model="form.timingRules" :shortcuts="shortcuts" clearable placeholder="请输入定时规则"/>
			</el-form-item>
			<el-form-item label="是否启用" prop="isEnable">
				<el-switch v-model="form.isEnable"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">
				取 消
			</el-button>
			<el-button :loading="isSaveing" type="primary" @click="submit()">
				保 存
			</el-button>
		</template>
	</el-dialog>
</template>

<script>
import scCron from "@/components/scCron";

export default {
	components: {
		scCron,
	},
	emits: ["success", "closed"],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: "新增计划任务",
				edit: "编辑计划任务",
			},
			form: {
				param: ''
			},
			value: '',
			rules: {
				name: [{required: true, message: "请填写标题"}],
				executeClass: [{required: true, message: "请填写执行类"}],
				timingRules: [{required: true, message: "请填写定时规则"}],
			},
			visible: false,
			isSaveing: false,
			shortcuts: [
				{
					text: "每天8点和12点 (自定义追加)",
					value: "0 0 8,12 * * ?",
				},
			],
		};
	},
	mounted() {
	},
	methods: {
		/*
		 * 添加视图
		 * @author: 路正宁
		 * @date: 2023-03-29 11:29:14
		 */
		addView() {
			this.mode = "add";
			this.visible = true;
			return this;
		},
		/*
		 * 编辑视图
		 * @author: 路正宁
		 * @date: 2023-03-29 11:29:36
		 */
		editView(task) {
			this.mode = "edit";
			this.visible = true;
			this.form = task;
			return this;
		},
		//表单提交方法
		async submit() {
			var valid = await this.$refs.dialogForm.validate().catch(() => {
			});
			if (!valid) {
				return false;
			}
			//锁定提交按钮
			this.isSaveing = true;
			var res = await this.$API.sysTaskService.save(this.form);
			if (res.code == 200) {
				//关闭页面
				this.visible = false;
				this.$message.success("操作成功");
				//回调函数
				this.$emit("success", res.data, this.mode);
			} else {
				this.$Response.errorNotice(res, "保存失败");
			}
			//释放提交按钮
			this.isSaveing = false;
		},
	},
};
</script>

<style></style>
