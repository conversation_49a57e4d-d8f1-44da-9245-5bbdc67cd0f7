<template>
    <div style="padding: 0 20px;">
        <el-tabs v-model="activeName" class="container-tabs" style="width: 100%;" @tab-click="handleClick">
            <el-tab-pane label="档案室" name="first">
                <img :src="houseInfoUrl" class="image">
            </el-tab-pane>
            <el-tab-pane label="档案柜" name="second">
                <el-table :data="list" border style="width: 100%;">
                    <el-table-column align="center" label="档案柜名称" prop="containerName"></el-table-column>
                    <el-table-column align="center" label="档案柜编号" prop="containerNum"></el-table-column>
                    <el-table-column align="center" label="档案柜类型" prop="containerType">
                        <template #default="scope">
                            <span>{{ scope.row.containerType == '1' ? '固定栏位' : '不固定栏位' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="所属档案室区域" min-width="100" prop="containerRegion">
                    </el-table-column>
                    <el-table-column align="center" label="档案柜层数" prop="containerFloors">
                    </el-table-column>
                    <el-table-column align="center" label="层起始位置" prop="containerFloorsStart">
                        <template #default="scope">
                            <span>{{ scope.row.containerFloorsStart == '1' ? '从上到下' : '从下到上' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="档案柜节数" prop="containerPitch">
                    </el-table-column>
                    <el-table-column align="center" label="节起始位置" prop="containerPitchStart">
                        <template #default="scope">
                            <span>{{ scope.row.containerPitchStart == '1' ? '从左到右' : '从右到左' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="上架盒数量" prop="boxCount">
                    </el-table-column>
                    <el-table-column align="center" fixed="right" label="操作" width="100">
                        <template #default="scope">
                            <el-button icon="View" link type="primary" @click="Viewe(scope.row)">查看</el-button>
                            <!-- <el-button link type="primary" @click="handleAdd(scope.row)">
                        <img src="@/assets/icons/update.png" style="margin-right:5px" alt="#" />编辑
                    </el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination">
                    <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
                        @pagination="containerList" />
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import storeroomData from "@/api/archive/storeroom/storeroom";

export default {
    name: 'container',
    props: {
        parameters: Array,
    },
    data() {
        return {
            activeName: 'first',
            queryParams: {
                current: 1,
                size: 10
            },
            total: 0,
            list: [],
            houseInfoUrl: '',
        }
    },
    mounted() {
        this.containerList();
        this.roomQueryById();
    },
    methods: {
        // tab切换处理
        handleClick() {
            // 可以在这里添加tab切换的逻辑
        },
        // 查询所有档案柜信息
        containerList() {
            if (this.parameters.recordHouseInfoTargetId) {
                storeroomData.containerList({
					'admsRoomDTO.id': this.parameters.recordHouseInfoTargetId,
                    ...this.queryParams,
                }).then(result => {
                    if (result.code === 200) {
                        this.list = result.data.records;
                        console.log(this.list);
                        this.total = result.data.total;
                    }
                }).catch(error => {
                    console.log(error);
                });
            } else {
                storeroomData.containerList({
					'admsRoomDTO.id': this.parameters.id,
                    ...this.queryParams,
                }).then(result => {
                    if (result.code === 200) {
                        this.list = result.data.records;
                        console.log(this.list);
                        this.total = result.data.total;
                    }
                }).catch(error => {
                    console.log(error);
                });
            }

        },
        // 查询档案室信息
        roomQueryById() {
            if (this.parameters.recordHouseInfoTargetId) {
                storeroomData.roomQueryById({
                    id: this.parameters.recordHouseInfoTargetId,
                }).then(result => {
                    if (result.code === 200) {
                        this.houseInfoUrl = result.data.roomPlaneUrl;
                    }
                }).catch(error => {
                    console.log(error);
                });
            } else {
                storeroomData.roomQueryById({
                    id: this.parameters.id,
                }).then(result => {
                    if (result.code === 200) {
                        this.houseInfoUrl = result.data.roomPlaneUrl;
                    }
                }).catch(error => {
                    console.log(error);
                });
            }
        },
        // 货柜信息编辑
        handleAdd(row) {
            this.form = row;
            this.open = true;
        },
        // 查看格子
        Viewe(data) {
            this.$emit('handleView', data);
        }
    }
}
</script>

<style scoped>
.image {
    width: 100%;
    height: 100%;
    max-height: 82vh;
    object-fit: contain;
    display: block;
}

/* 标签页容器 */
.container-tabs {
    height: calc(100vh - 120px); /* 减去外层padding等高度 */
    display: flex;
    flex-direction: column-reverse;
}

.container-tabs :deep(.el-tabs__header) {
    flex-shrink: 0;
    margin-bottom: 0;
}

.container-tabs :deep(.el-tabs__content) {
    flex: 1;
    overflow: hidden;
    padding: 0;
    height: 0; /* 重要：强制flex子元素计算高度 */
}

.container-tabs :deep(.el-tab-pane) {
    height: 100%;
    overflow: hidden;
    padding: 10px 0 0 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

/* 分页区域 - 固定高度，靠右显示 */
.pagination {
    flex-shrink: 0;
    align-self: flex-end;
    padding: 10px 0;
}
</style>
