<template>
	<el-container style="box-shadow: var(--el-box-shadow-light)">
		<el-main v-loading="loading" style="padding: 0">
			<RelationGraph ref="graphRef" :on-line-click="onLineClick" :on-node-click="onNodeClick" class="cardBox"
						   :options="graphOptions">
				<template #node="{ node }">
					<el-popover ref="popover" :width="72" trigger="contextmenu" :popper-style="{'min-width': '72px'}">
						<template #reference>
							<div style="height: 80px;min-width: 12vw">
								<div style="height: 25%;width: 100%;">
									{{ node.data.remark }}
								</div>
								<el-card
									:body-style="{
										height: '100%',
										width: '100%',
										padding: '0',
										'display': 'flex',
										'flex-direction': 'row',
										'flex-wrap': 'nowrap',
										'align-items': 'center',
										'justify-content': 'center'
									}"
									style="height: 75%;width: 100%;">
									{{ node.text }}
								</el-card>
							</div>
						</template>
						<template #default>
							<el-button link size="small" style="width: 100%" type="info"
									   @click="onNodeRelationship(node)">
								关联查询
							</el-button>
						</template>
					</el-popover>
				</template>
			</RelationGraph>
		</el-main>

		<!-- 查看 -->
		<el-dialog v-if="openView" v-model="openView" append-to-body title="查看档案" top="8vh" width="92%">
			<viewFiles :receiveId="receiveId" @childMove="parentView"/>
		</el-dialog>
	</el-container>
</template>

<script setup>
// 关系图谱
import RelationGraph from 'relation-graph/vue3';
// G6
import {Graph} from '@antv/g6';
// 检索
import retrieval from '@/api/archive/retrieval';
// 查看弹窗
import viewFiles from '../../managementFile/view.vue';
import {getCurrentInstance, onMounted, ref} from "vue";

const {proxy} = getCurrentInstance();
const props = defineProps({
	searchString: {
		type: String,
		default: ''
	}
});
defineEmits(["returnBack"]);
const graphRef = ref(null);
const openView = ref(false);
const loading = ref(false);
const receiveId = ref('');
const graphOptions = ref({
	debug: false,
	allowSwitchLineShape: true,
	allowSwitchJunctionPoint: true,
	allowShowDownloadButton: true,
	moveToCenterWhenRefresh: false,
	zoomToFitWhenRefresh: true,
	defaultJunctionPoint: 'border',
	isMoveByParentNode: true,
	placeOtherGroup: true,
	placeSingleNode: true,
	layout: {
		layoutName: 'force',
		centerOffset_x: 0,
		centerOffset_y: 0,
		distance_coefficient: 1.2,
		force_node_repulsion: 3,
		force_line_elastic: 0.5,
		maxLayoutTimes: 520
	}
});

defineExpose({
	showGraph,
	resetQuery
});

onMounted(() => {
	//G6
	// initGraph();
	//relation-graph
	showGraph();
});

function initGraph() {
	loading.value = true;
	retrieval.getRelationalData({
		relationalString: props.searchString
	}).then(result => {
		if (result.code === 200) {
			const graphInstance = new Graph({
				container: document.getElementById("graphBox"),
				autoResize: true,
				behaviors: ['drag-canvas', 'zoom-canvas', 'drag-element'],
				plugins: ['tooltip', 'fullscreen', 'toolbox'],
				autoFit: 'center',
				data: {
					nodes: result.data.nodes.map(item => {
						return {
							...item,
							type: 'rect'
						}
					}),
					edges: result.data.lines.map(item => {
						return {
							source: item.from,
							target: item.to,
							label: item.text,
							type: 'line',
							style: {
								startArrow: true
							}
						};
					}),
				},
				layout: {
					type: 'd3-force',
					collide: {
						strength: 0.5,
					},
				},
			});
			graphInstance.render();
			loading.value = false;
		}
	}).catch(error => {
		console.log(error);
		loading.value = false;
		proxy.msgError('获取关系图谱数据失败');
	});
}

function showGraph() {
	loading.value = true;
	retrieval.getRelationalData({
		relationalString: props.searchString
	}).then(result => {
		if (result.code === 200) {
			graphRef.value.setJsonData(result.data.nodes.length > 0 ? result.data : {
				nodes: [{
					id: "1",
					text: "查无数据",
					nodeShape: "1"
				}], lines: [], rootId: "1"
			}, (graphInstance) => {
				// 这些写上当图谱初始化完成后需要执行的代码
				graphInstance.refresh();
			});
			loading.value = false;
		}
	}).catch(error => {
		console.log(error);
		loading.value = false;
		proxy.msgError('获取关系图谱数据失败');
	});
}

// 重置
function resetQuery() {
	showGraph();
}

// 打开查看
function collectFile(val) {
	receiveId.value = val;
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

function onNodeClick(nodeObject) {
	collectFile(nodeObject);
	console.log('onNodeClick:', nodeObject);
}

//关联查询
function onNodeRelationship(nodeObject) {

}

function onLineClick(lineObject, linkObject, $event) {
	console.log('onLineClick:', lineObject);
}
</script>

<style lang="scss" scoped>
.cardBox {
	width: 100%;
	height: 100%;
	box-shadow: none;
}
</style>
