import http from "@/utils/request"
//查询档案盒列表数据
export default {
    list: function (params) {
        return http.get(
            '/archive/info/box/list',
            params
        )
    },
    //保存或更新档案盒
    save: function (data) {
        return http.post(
            '/archive/info/box/save',
            data
        )
    },
     //装盒
     infoOperate: function (data) {
        return http.post(
            '/archive/info/main/infoOperate',
            data
        )
    },
    //查询是否有相同的编号和名称
    findListConditions: function (data) {
        return http.post(
            '/archive/info/box/findListConditions',
            data
        )
    },

}
