{"name": "scui", "version": "1.6.9", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build --report", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/g6": "^5.0.35", "@element-plus/icons-vue": "^2.3.1", "@imengyu/vue3-context-menu": "^1.4.1", "@tinymce/tinymce-vue": "5.0.0", "@vue/devtools-api": "6.5.0", "@wangeditor/editor": "^5.1.23", "@pureadmin/utils": "^1.9.7", "@wangeditor/editor-for-vue": "^5.1.12", "amfe-flexible": "^2.2.1", "axios": "^1.7.4", "codemirror": "5.65.5", "core-js": "3.29.0", "cropperjs": "1.5.13", "crypto-js": "^4.2.0", "default-passive-events": "^2.0.0", "echarts": "^5.4.2", "echarts-liquidfill": "^3.1.0", "element-china-area-data": "^6.0.2", "element-plus": "^2.4.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "jquery": "^3.7.0", "konva": "^9.3.6", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "nprogress": "0.2.0", "pdfjs-dist": "^5.0.375", "postcss-pxtorem": "^6.0.0", "qrcodejs2": "0.0.2", "relation-graph": "^2.1.20", "sortablejs": "^1.15.0", "tinymce": "^6.8.2", "vue": "^3.3.4", "vue-i18n": "9.2.2", "vue-konva": "^3.0.2", "vue-pdf-embed": "^1.2.1", "vue-router": "^4.1.6", "vue-uuid": "^3.0.0", "vue3-ace-editor": "^2.2.3", "vue3-pdf-app": "^1.0.3", "vue3-seamless-scroll": "^2.0.1", "vue3-tree-org": "^4.2.2", "vuedraggable": "4.0.3", "vuex": "^4.1.0", "xgplayer": "2.32.2", "xgplayer-hls": "2.5.2"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/eslint-parser": "^7.22.15", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "^5.0.8", "eslint": "8.35.0", "eslint-plugin-vue": "9.9.0", "js-md5": "^0.7.3", "raw-loader": "^4.0.2", "sass": "^1.62.1", "sass-loader": "^12.6.0", "typescript": "^5.0.0", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.25.0", "vue2-ace-editor": "^0.0.15"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"APP_CONFIG": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"indent": 0, "no-tabs": 0, "no-mixed-spaces-and-tabs": 0, "vue/no-unused-components": 0, "vue/multi-word-component-names": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}