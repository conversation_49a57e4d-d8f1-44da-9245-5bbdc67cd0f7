<!--
 * @Author: saya
 * @Date: 2023-07-24 13:45:13
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-08-25 11:54:14
 * @FilePath: \archive-manage-front\src\views\managementFile\approve\examine.vue
 * @Description:
-->
<template>
	<el-descriptions :column="3" border style="margin-bottom: 20px" title="申请信息">
		<el-descriptions-item align="center" label="鉴定类型" width="80">
			{{ controlType(descriptionsLabel.controlType) }}
		</el-descriptions-item>
		<el-descriptions-item align="center" label="申请人" width="80">
			{{ descriptionsLabel.controlApply.name }}
		</el-descriptions-item>
		<el-descriptions-item align="center" label="申请人所属部门" width="80">
			{{ descriptionsLabel.controlApply.sysOffice.name }}
		</el-descriptions-item>
		<el-descriptions-item align="center" label="申请时间" width="80">
			{{ moment(descriptionsLabel.createDate).format('YYYY-MM-DD HH:mm:ss') }}
		</el-descriptions-item>
		<el-descriptions-item v-if="descriptionsLabel.controlType == '1'" align="center" label="延期时间" width="80">
			<span v-if="descriptionsLabel.controlContextObject.delayTime === 'Y'">永久</span>
			<span v-else-if="descriptionsLabel.controlContextObject.delayTime === 'D5'">5年</span>
			<span v-else-if="descriptionsLabel.controlContextObject.delayTime === 'D10'">10年</span>
			<span v-else-if="descriptionsLabel.controlContextObject.delayTime === 'D20'">20年</span>
			<span v-else-if="descriptionsLabel.controlContextObject.delayTime === 'D30'">30年</span>
			<span v-else>暂无</span>
		</el-descriptions-item>
		<el-descriptions-item align="center" label="申请理由" width="80">
			{{ descriptionsLabel.controlRemark }}
		</el-descriptions-item>
	</el-descriptions>
	<h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">档案列表</h4>
	<el-table :data="controlList">
		<el-table-column label="序号" prop="sort" width="80" align="center">
			<template #default="scope">
				{{ scope.$index + 1 }}
			</template>
		</el-table-column>
		<el-table-column label="档案名称" align="left" prop="name"/>
		<el-table-column align="center" label="档案号" prop="num"/>
		<el-table-column align="center" label="开放状态" prop="controlStatus" width="120">
			<template #default="scope">
				{{ control(scope.row.controlStatus) }}
			</template>
		</el-table-column>
		<el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="80px">
			<template #default="scope">
				<el-button link type="primary" @click="collectFile(scope.row)">查看
				</el-button>
			</template>
		</el-table-column>
	</el-table>
	<div v-if="props.receiveType === '1'">
		<h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">审批意见</h4>
		<auditForms ref="auditRef3" @refresh="refresh3"/>
	</div>
	<div v-if="props.receiveType === '2'">
		<h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">操作记录</h4>
		<LogQuery ref="logRef3" />
	</div>
	<el-main>
		<el-container>
			<el-main>
				<div style="float: right;">
					<el-button plain @click="() => cancellation()">取消</el-button>
					<el-button v-if="props.receiveType === '1'" type="primary" @click="rightOrder()">确认</el-button>
				</div>
			</el-main>
		</el-container>
	</el-main>
	<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
		<viewFiles @childMove="parentView" :receiveId="receiveId"></viewFiles>
	</el-dialog>
</template>
<script setup>
import approveList from '@/api/archive/managementFile/approve';
import {defineProps, getCurrentInstance, onMounted, ref} from 'vue'
// 操作流程
import LogQuery from "@/components/detailsForm/logQuery.vue";
import moment from 'moment'
// 查看弹窗
import viewFiles from '../view.vue';

const {proxy} = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const props = defineProps({
	receiveId: {
		type: String
	},
	receiveType: {
		type: String
	},
});
// 标题
const title = ref('')
// 查看receiveId
const receiveId = ref('')
const openView = ref(false)
const loading = ref(true);
// 操作流程
const logRef3 = ref(null)
// 审批
const auditRef3 = ref(null)
const refresh3 = () => {
    emit("childMove");
	getList();
}
const rightOrder = () => {
	auditRef3.value.formSub(descriptionsLabel.value.id);
}
// 接收库List
const descriptionsLabel = ref([]);
// 查询审批档案
const controlList = ref([]);

onMounted(() => {
	getControl();
	getList();
	console.log(props.receiveType);
	if (props.receiveType !== '1') {
		auditList();
	}
});

async function auditList() {
	approveList.auditList({
		'control.id': props.receiveId.id
	}).then(res => {
		if (res.code === 200) {
			let records = [];
			res.data.records.forEach(record => {
				records.push({
					name: record.controlAuditPerson.name,
					updateDate: record.updateDate,
					remark: record.remark
				});
			});
			logRef3.value.timeFns(records);
		}
	}).catch(err => {
		console.log(err);
		proxy.msgError('加载失败');
	});
}

// 控制等级
function control(val) {
	if (val == '1') {
		return '公开'
	} else if (val == '2') {
		return '公司内部开放'
	} else if (val == '3') {
		return '部门内部开放 '
	} else if (val == '4') {
		return '控制'
	} else {
		return '暂无'
	}
}

// 回显鉴定类型
function controlType(val) {
	if (val == '1') {
		return '鉴定延期';
	} else if (val == '2') {
		return '鉴定销毁';
	} else if (val == '3') {
		return '开放鉴定';
	}
}

// 取消
function cancellation() {
	emit("childMove");
}

function collectFile(val) {
	title.value = '查看';
	receiveId.value = val;
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 查询原数据
function getList() {
	approveList.queryById({
		id: props.receiveId.id
	}).then(res => {
		if (res.code === 200) {
			loading.value = false;
			descriptionsLabel.value = res.data
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

function getControl() {
	approveList.queryByIds({
		ids: props.receiveId.recordInfoIds,
		isAll: true
	}).then(res => {
		if (res.code === 200) {
			controlList.value = res.data
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}
</script>
<style scoped>
.fileUrl {
	margin-bottom: 10px;
	cursor: pointer;
}

.fileUrl:hover {
	color: #2a76f8;
}

p {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
