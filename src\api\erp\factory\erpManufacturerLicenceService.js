import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/factory/erpManufacturerLicence/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/factory/erpManufacturerLicence/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/factory/erpManufacturerLicence/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerLicence/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/factory/erpManufacturerLicence/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerLicence/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/factory/erpManufacturerLicence/import',
      data
    )
  }
}
