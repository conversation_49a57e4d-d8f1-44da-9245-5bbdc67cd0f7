import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/audit/auditProcessForm/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/audit/auditProcessForm/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/audit/auditProcessForm/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/audit/auditProcessForm/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/audit/auditProcessForm/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/audit/auditProcessForm/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/audit/auditProcessForm/import',
      data
    )
  }
}
