<template>
	<context-menu
		v-model:show="show"
		:options="optionsComponent"
	>
		<context-menu-item @click.stop="onMenuClick(1)">
			<div style="width: 100%;display: flex;justify-content: center">
				<el-button link>全选</el-button>
			</div>
		</context-menu-item>
		<context-menu-item @click.stop="onMenuClick(2)">
			<div style="width: 100%;display: flex;justify-content: center">
				<el-button link>取消</el-button>
			</div>
		</context-menu-item>
	</context-menu>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watch, watchEffect} from 'vue';
import {ContextMenu, ContextMenuItem} from '@imengyu/vue3-context-menu';

/**
 * 数据部分
 */
const childRef = ref(null);
const data = reactive({});
const emit = defineEmits(['onMenuClick']);
const props = defineProps({});
const show = ref(false);
const optionsComponent = ref({
	zIndex: 3,
	minWidth: 200,
	x: 0,
	y: 0
});
const node_data = ref(null);

watch(() => show.value, () => {
	if (!show.value) {
		node_data.value = null
	}
});

onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
});

onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});

watchEffect(() => {
});

const onMenuClick = (index) => {
	emit('onMenuClick', index, node_data.value)
};
const onButtonClick = (e, data) => {
	//显示组件菜单
	show.value = true;
	node_data.value = data
	optionsComponent.value.x = e.x;
	optionsComponent.value.y = e.y;
};

defineExpose({
	onButtonClick
});
</script>

<style lang='scss' scoped>

</style>
