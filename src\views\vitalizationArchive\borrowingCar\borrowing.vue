<template>
	<div class="borrowing-container">
		<!-- 表单区域 -->
		<div class="form-section">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
				<div class="form-row">
					<div class="form-item">
						<el-form-item label="借阅人:" prop="name">
							<el-input v-model="form.name" disabled placeholder="请输入借阅人" />
						</el-form-item>
					</div>
					<div class="form-item">
						<el-form-item label="借阅部门:" prop="sysOfficeName">
							<el-input v-model="form.sysOfficeName" disabled placeholder="请输入借阅部门" />
						</el-form-item>
					</div>
				</div>
				<div class="form-row">
					<div class="form-item">
						<el-form-item label="借阅起始日期:" prop="borrowStartTime">
							<el-date-picker v-model="form.borrowStartTime" format="YYYY-MM-DD HH:mm:ss"
								placeholder="请选择借阅起始日期" style="width: 100%" type="datetime"
								value-format="YYYY-MM-DD HH:mm:ss" />
						</el-form-item>
					</div>
					<div class="form-item">
						<el-form-item label="借阅归还日期:" prop="borrowEndTime">
							<el-date-picker v-model="form.borrowEndTime" format="YYYY-MM-DD HH:mm:ss"
								placeholder="请选择借阅归还日期" style="width: 100%" type="datetime"
								value-format="YYYY-MM-DD HH:mm:ss" />
						</el-form-item>
					</div>
				</div>
				<div class="form-row">
					<div class="form-item">
						<el-form-item label="是否包含水印:" prop="borrowIsWatermark">
							<el-radio-group v-model="form.borrowIsWatermark">
								<el-radio label="是" value="1" />
								<el-radio label="否" value="2" />
							</el-radio-group>
						</el-form-item>
					</div>
				</div>
				<div class="form-row full-width">
					<div class="form-item-full">
						<el-form-item label="借阅理由:" prop="borrowRemark">
							<el-input v-model="form.borrowRemark" :autosize="{ minRows: 3, maxRows: 4 }" clearable maxlength="500"
								placeholder="请输入借阅理由" show-word-limit type="textarea" />
						</el-form-item>
					</div>
				</div>
			</el-form>
		</div>

		<!-- 分隔线 -->
		<el-divider style="margin: 16px 20px;" />

		<!-- 表格区域 -->
		<div class="table-section">
			<h4 class="table-title">借阅明细</h4>
			<div class="table-wrapper">
				<el-table :data="props.handList" border empty-text="暂无" height="100%"
					@selection-change="handleSelectionChange">
					<el-table-column align="center" type="selection" width="50" fixed="left"/>
					<el-table-column align="center" label="序号" width="60">
						<template #default="scope">
							{{ scope.$index + 1 }}
						</template>
					</el-table-column>
					<el-table-column align="left" label="档案名称" min-width="200" prop="info.name" show-overflow-tooltip />
					<el-table-column align="center" label="借阅方式" min-width="320">
						<template #default="scope">
							<el-checkbox-group v-model="scope.row.checkList">
								<el-checkbox checked label="1">电子</el-checkbox>
								<el-checkbox label="2">纸质</el-checkbox>
								<el-checkbox label="3">下载</el-checkbox>
								<el-checkbox label="4">打印</el-checkbox>
							</el-checkbox-group>
						</template>
					</el-table-column>
					<el-table-column align="center" label="档案存址" show-overflow-tooltip min-width="300">
						<template #default="scope">
							{{ scope.row.info?.storageAddress || '暂无' }}
						</template>
					</el-table-column>
					<el-table-column align="center" label="所属部门" show-overflow-tooltip min-width="180">
						<template #default="scope">
							{{ scope.row.info?.office?.name || '暂无' }}
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>

		<!-- 按钮区域 -->
		<div class="button-section">
			<el-button @click="cancellation">取消</el-button>
			<el-button type="primary" @click="determine">确定</el-button>
		</div>
	</div>
</template>

<script setup>
import { defineEmits, defineProps, getCurrentInstance, onMounted, reactive, ref } from 'vue'
import vitalizationArchiveList from '@/api/archive/vitalizationArchive'
import tool from '@/utils/tool';

const emit = defineEmits(["childMove"]);
const props = defineProps({
	handList: {
		type: Array
	},
})
// 表单校验
const rules = reactive({
	name: [{ required: true, message: '请输入借阅人', trigger: 'blur' }],
	sysOfficeName: [{ required: true, message: '请输入借阅部门', trigger: 'blur' }],
	borrowStartTime: [
		{ required: true, message: '请选择借阅起始日期', trigger: 'change' },
		{ validator: validateDateRange, trigger: 'change' },
	],
	borrowEndTime: [
		{ required: true, message: '请选择借阅归还日期', trigger: 'change' },
		{ validator: validateDateRange, trigger: 'change' },
	],
	borrowIsWatermark: [{ required: true, message: '请选择是否包含水印', trigger: 'change' }],
	borrowRemark: [{ required: true, message: '请输入借阅理由', trigger: 'blur' }],
})
const { proxy } = getCurrentInstance();
const form = ref({})
const selectedItems = ref([])

//初始方法
onMounted(() => {
	getList();
});

// 处理数据
function getList() {
	props.handList.forEach(row => {
		row.checkList = ['1']; // 默认选中电子借阅
	})
	const userInfo = tool.data.get('USER_INFO');
	if (userInfo) {
		form.value.name = userInfo.name;
		form.value.sysOfficeName = userInfo.sysOffice?.name;
	}
}

// 表格选择变化处理
function handleSelectionChange(selection) {
	selectedItems.value = selection;
}

// 统一的时间范围验证
function validateDateRange(rule, value, callback) {
	// 忽略未使用的参数警告
	void rule;
	void value;

	if (!form.value.borrowStartTime || !form.value.borrowEndTime) {
		callback();
		return;
	}

	const startTime = new Date(form.value.borrowStartTime);
	const endTime = new Date(form.value.borrowEndTime);

	if (startTime >= endTime) {
		callback(new Error('借阅起始日期必须早于归还日期'));
	} else {
		callback();
	}
}

// 确定提交新增
function determine() {
	// 验证表单
	proxy.$refs["formRef"].validate(valid => {
		if (!valid) {
			return;
		}

		// 验证是否选择了借阅项目
		if (!selectedItems.value || selectedItems.value.length === 0) {
			proxy.msgError('请至少选择一项档案进行借阅');
			return;
		}

		// 验证选中项目是否都选择了借阅方式
		const invalidItems = selectedItems.value.filter(item => !item.checkList || item.checkList.length === 0);
		if (invalidItems.length > 0) {
			proxy.msgError('请为所有选中的档案选择借阅方式');
			return;
		}

		// 构建提交数据
		const tableArray = selectedItems.value.map(row => ({
			infoId: row.info.id,
			infoType: row.checkList.join(','),
		}));

		const userInfo = tool.data.get('USER_INFO');
		if (!userInfo) {
			proxy.msgError('用户信息获取失败，请重新登录');
			return;
		}

		const data = {
			borrowApply: userInfo.id,
			borrowIsWatermark: form.value.borrowIsWatermark,
			borrowRemark: form.value.borrowRemark,
			borrowStartTime: form.value.borrowStartTime,
			borrowEndTime: form.value.borrowEndTime,
			infoList: tableArray,
		};

		// 提交借阅申请
		vitalizationArchiveList.recordBorrowApply(data).then(res => {
			if (res.code === 200) {
				tool.data.set('CAR', []);
				proxy.msgSuccess('借阅申请提交成功');
				emit("childMove");
			} else {
				proxy.msgError(res.message || '借阅申请提交失败');
			}
		}).catch(error => {
			console.error('借阅申请提交失败:', error);
			proxy.msgError('借阅申请提交失败，请稍后重试');
		});
	});
}

// 取消
function cancellation() {
	emit("childMove");
}
</script>

<style scoped>
.borrowing-container {
	display: flex;
	flex-direction: column;
	height: auto;
	min-height: 500px;
	max-height: 80vh;
	padding: 0;
	box-sizing: border-box;
	overflow: visible;
}

.form-section {
	flex-shrink: 0;
	margin-bottom: 0;
	padding: 20px 20px 0 20px;
}

.form-row {
	display: flex;
	gap: 16px;
	margin-bottom: 16px;
	align-items: flex-start;
}

.form-row.full-width {
	flex-direction: column;
}

.form-item {
	flex: 1;
	min-width: 0;
}

.form-item-full {
	width: 100%;
}

.table-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 200px;
	max-height: 350px;
	margin-bottom: 16px;
	padding: 0 20px;
}

.table-title {
	font-weight: bold;
	color: #303133;
	margin: 0 0 12px 0;
	flex-shrink: 0;
}

.table-wrapper {
	flex: 1;
	min-height: 180px;
	overflow: auto;
}

.button-section {
	flex-shrink: 0;
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding: 16px 20px 0 20px;
	border-top: 1px solid #ebeef5;
	background-color: #fff;
	position: relative;
	z-index: 10;
}

/* 确保表单项不会过度拉伸 */
:deep(.el-form-item) {
	margin-bottom: 0;
	width: 100%;
}

:deep(.el-form-item__label) {
	white-space: nowrap;
	flex-shrink: 0;
}

:deep(.el-form-item__content) {
	flex: 1;
	min-width: 0;
}

/* 优化文本域样式 */
:deep(.el-textarea) {
	width: 100%;
}

/* 确保输入框和选择器不会溢出 */
:deep(.el-input),
:deep(.el-date-editor),
:deep(.el-radio-group) {
	width: 100%;
	max-width: 100%;
}

/* 优化表格样式 */
:deep(.el-table) {
	font-size: 14px;
}

:deep(.el-table .el-checkbox-group) {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

:deep(.el-table .el-checkbox) {
	margin-right: 0;
}

/* 优化单选按钮组样式 */
:deep(.el-radio-group) {
	display: flex;
	gap: 16px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
	.borrowing-container {
		max-height: 70vh;
	}

	.table-section {
		max-height: 300px;
	}

	.form-row {
		flex-direction: column;
		gap: 8px;
	}

	:deep(.el-table .el-checkbox-group) {
		flex-direction: column;
		gap: 4px;
	}
}

/* 确保弹窗内容不会超出视口 */
@media (max-height: 700px) {
	.borrowing-container {
		max-height: 90vh;
	}

	.table-section {
		max-height: 250px;
	}

	.form-section {
		padding: 15px 20px 0 20px;
	}

	.button-section {
		padding: 12px 20px 15px 20px;
	}
}
</style>
