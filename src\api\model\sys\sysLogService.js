import http from "@/utils/request"

/*
 *@description: 系统日志
 *@author: 路正宁
 *@date: 2023-03-17 11:30:16
 *@version: V1.0
*/
export default {
	/*
	 * 保存系统日志
	 * @author: 路正宁
	 * @date: 2023-03-23 11:10:28
	*/
	save: function (inputForm) {
		return http.post(
			'/sys/log/save',
			inputForm
		)
	},

	delete: function (ids) {
		return http.delete(
			'/sys/log/delete',
			{ids: ids}
		)
	},

	queryById: function (id) {
		return http.get(
			'/sys/log/queryById',
			{id: id}
		)
	},

	list: function (params) {
		return http.get(
			'/sys/log/list',
			params
		)
	},

	selectCountByDate: function () {
		return http.get('/sys/log/selectCountByDate')
	},

	exportTemplate: function () {
		return http.get(
			'/sys/log/import/template',
			'blob'
		)
	},

	exportExcel: function (params) {
		return http.get(
			'/sys/log/export',
			params,
			'blob'
		)
	},

	importExcel: function (data) {
		return http.post(
			'/sys/log/import',
			data
		)
	}
}
