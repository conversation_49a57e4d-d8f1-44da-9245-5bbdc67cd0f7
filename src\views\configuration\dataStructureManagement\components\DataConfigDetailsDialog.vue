<template>
	<el-dialog v-if="props.open" v-model="props.open" :title="props.title" align-center append-to-body
			   width="720px" @close="closeDialog">
		<el-form ref="detailsForm" :model="data.formDetails" :rules="data.rulesDetails" label-width="150px"
				 style="margin-top: 0;padding-right: 20px;">
			<el-form-item label="数据名称" prop="name">
				<el-input v-model="data.formDetails.name" placeholder="请输入配置详情名称"/>
			</el-form-item>
			<el-form-item label="数据类型来源" prop="source">
				<el-select v-model="data.formDetails.source" placeholder="请选择数据类型来源" style="width: 100%"
						   @change="() => data.formDetails.type = ''">
					<el-option label="默认" value="1"/>
					<el-option label="自定义" value="2"/>
				</el-select>
			</el-form-item>
			<el-form-item v-if="data.formDetails.source === '2'" label="数据展示方式" prop="showMethod">
				<el-select v-model="data.formDetails.showMethod" placeholder="请选择数据类型来源" style="width: 100%"
						   @change="() => data.formDetails.type = ''">
					<el-option label="折叠面板" value="1"/>
					<el-option label="表格" value="2"/>
				</el-select>
			</el-form-item>
			<el-form-item v-if="data.formDetails.source" label="数据类型" prop="type">
				<el-select v-if="data.formDetails.source === '1'" v-model="data.formDetails.type" placeholder="请选择数据类型"
						   style="width: 100%" @change="dataTypeChoose">
					<el-option label="文本" value="1"/>
					<el-option label="照片" value="2"/>
					<el-option label="下拉框" value="3"/>
					<el-option label="日期" value="4"/>
					<el-option label="选择(是/否)" value="5"/>
				</el-select>
				<el-tree-select v-if="data.formDetails.source === '2'"
								v-model="data.formDetails.type"
								:data="data.menuList"
								:props="{ value: 'targetName', label: 'name' }"
								check-strictly
								highlight-current
								placeholder="请选择数据类型"
								style="width: 100%;">
				</el-tree-select>
			</el-form-item>
			<el-form-item v-if="data.formDetails.type === '3' || data.formDetails.type === '4' || data.formDetails.type === '5'"
						  label="数据对照值" prop="dataConfigValue">
				<el-button v-if="data.formDetails.type === '3'" icon="plus" plain style="margin-bottom: 9px" type="primary"
						   @click="() => {
						   		data.dataDialogIsView = true;
								data.dataDialogTitle = '添加数据对照定义';
						   }">
					添加
				</el-button>
				<el-table v-if="data.formDetails.type === '3'" :data="data.dataTableList" border style="width: 100%">
					<el-table-column align="center" label="值名称" prop="name"/>
					<el-table-column align="center" label="值数据" prop="value"/>
					<el-table-column align="center" label="操作">
						<template #default="scope">
							<el-button size="small"
									   type="warning"
									   @click="() => {
										   data.dataModel = scope.row;
										   data.dataDialogIsView = true;
										   data.dataDialogTitle = '修改数据对照定义';
									   }">
								编辑
							</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="() => data.dataTableList = data.dataTableList
										.filter(item => item.value !== scope.row.value && item.name !== scope.row.name)">
								<template #reference>
									<el-button size="small" type="danger">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
				<el-select v-if="data.formDetails.type === '4'" v-model="data.formDetails.dataConfigValue"
						   placeholder="请选择日期类型" style="width: 100%">
					<el-option label="年-月-日 时:分:秒" value="YYYY-MM-DD HH:mm:ss"/>
					<el-option label="年-月-日" value="YYYY-MM-DD"/>
				</el-select>
				<el-table v-if="data.formDetails.type === '5'" :data="data.dataTableList" border style="width: 100%">
					<el-table-column align="center" label="值名称" prop="name"/>
					<el-table-column align="center" label="值数据" prop="value">
						<template #default="scope">
							<el-input v-model="scope.row.value" placeholder="请输入值数据"/>
						</template>
					</el-table-column>
				</el-table>
			</el-form-item>
			<el-form-item label="是否隐藏" prop="isView">
				<el-switch
					v-model="data.formDetails.isView"
					active-text="是"
					active-value="1"
					inactive-text="否"
					inactive-value="0"
					inline-prompt
				/>
			</el-form-item>
			<el-form-item v-if="data.formDetails.source === '1'" label="列展示方式" prop="displayMode">
				<el-select v-model="data.formDetails.displayMode" placeholder="请选择列展示方式">
					<el-option label="一列" value="1"/>
					<el-option label="半行" value="2"/>
					<el-option label="占满一行" value="3"/>
				</el-select>
			</el-form-item>
			<el-form-item v-if="data.formDetails.type === '1'" label="数据长度" prop="length">
				<el-input v-model="data.formDetails.length" placeholder="请输入数据长度"/>
			</el-form-item>
			<el-form-item label="数据排序" prop="sort">
				<el-input-number v-model="data.formDetails.sort" :min="1" controls-position="right"/>
			</el-form-item>
			<el-form-item label="数据描述" prop="remark">
				<el-input v-model="data.formDetails.remark" placeholder="请输入配置详情描述"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="savaDetails(data.formDetails)">
					确 定
				</el-button>
				<el-button @click="closeDialog">
					取 消
				</el-button>
			</div>
		</template>

		<el-dialog v-model="data.dataDialogIsView" :show-close="false" :title="data.dataDialogTitle" align-center width="500">
			<el-form ref="dataModelForm" :model="data.dataModel" :rules="data.dataModelFormRules">
				<el-form-item label="值名称" prop="name">
					<el-input v-model="data.dataModel.name" placeholder="请输入值名称"/>
				</el-form-item>
				<el-form-item label="值数据" prop="value">
					<el-input v-model="data.dataModel.value" placeholder="请输入值数据"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="data.dataDialogIsView = false;data.dataModel = {};">
						取消
					</el-button>
					<el-button type="primary" @click="addDataItemToList">
						确认
					</el-button>
				</div>
			</template>
		</el-dialog>
	</el-dialog>
</template>

<script setup>
import dataStructure from "@/api/archive/dataStructureManagement/dataStructure";
import {getCurrentInstance, reactive, ref, watch} from "vue";

const {proxy} = getCurrentInstance();
const props = defineProps({
	title: {
		type: String,
		default: '新增'
	},
	open: {
		type: Boolean,
		default: false
	},
	menuList: {
		type: Array,
		default: () => {
			return []
		}
	},
	data: {
		type: Object,
		default: () => {
			return {}
		}
	}
});
const emit = defineEmits(['close']);
const data = reactive({
	openDetails: false,
	detailBoxTitle: '',
	detailBoxOpen: false,
	formDetails: {},
	dataTableList: [],
	menuList: [],
	dataDialogIsView: false,
	dataModel: {},
	dataDialogTitle: '',
	queryParams: {
		current: 1,
		size: 10,
		configId: ''
	},
	dataModelFormRules: {
		name: [
			{required: true, message: '值名称不能为空', trigger: 'blur'}
		],
		value: [
			{required: true, message: '值数据不能为空', trigger: 'blur'}
		]
	},
	rulesDetails: {
		name: [
			{required: true, message: "数据名称不能为空", trigger: "blur"}
		],
		source: [
			{required: true, message: "数据类型来源不能为空", trigger: "change"}
		],
		showMethod: [
			{required: true, message: "数据展示方式不能为空", trigger: "change"}
		],
		type: [
			{required: true, message: "数据类型不能为空", trigger: "change"},
			{required: true, message: "数据类型不能为空", trigger: "blur"}
		],
		length: [
			{required: true, message: "数据长度不能为空", trigger: "blur"},
			{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
		],
		remark: [
			{required: true, message: "数据描述不能为空", trigger: "blur"}
		],
		displayMode: [
			{required: true, message: "列展示方式不能为空", trigger: "change"}
		],
		sort: [
			{required: true, message: "数据排序不能为空", trigger: "blur"}
		],
	},
});
const dataModelForm = ref(null);
const detailsForm = ref(null);

watch(() => props.data, (newData) => {
	data.formDetails = newData;
}, {deep: true});

watch(() => props.menuList, (newData) => {
	data.menuList = newData;
}, {deep: true});

function closeDialog() {
	emit('close', '');
}

function addDataItemToList() {
	dataModelForm.value.validate((valid) => {
		if (valid) {
			let filterData = data.dataTableList.filter(item => item.name === data.dataModel.name);

			if (filterData.length > 0) {
				data.dataTableList.forEach((item) => {
					if (item.name === data.dataModel.name) {
						item.value = data.dataModel.value;
					}
				})
			} else {
				data.dataTableList.push(data.dataModel);
			}
			data.dataDialogIsView = false;
			data.dataModel = {};
		}
	})
}

function dataTypeChoose(value) {
	if (value === '5') {
		data.dataTableList = [
			{
				"name": "是",
				"value": 1
			},
			{
				"name": "否",
				"value": 0
			}
		];
	} else {
		data.dataTableList = [];
	}
}

function getDetailsInfoList() {
	data.menuLoading = true;
	dataStructure.getList({
		...data.queryParams
	}).then(res => {
		if (res.code === 200) {
			data.detailsList = res.data.records;
			data.total = res.data.total;
		} else {
			proxy.msgError(res.msg, "查询失败");
		}
	}).catch(error => {
		proxy.msgError(error, "查询失败");
	});
	//释放页面加载
	data.menuLoading = false;
}

/*
 * 更新数据详情配置信息
 */
async function savaDetails(dataInfo) {
	let dataViewValue = null;
	if (data.formDetails.type === '3' || data.formDetails.type === '5') {
		dataViewValue = JSON.stringify(data.dataTableList);
	}
	if (data.formDetails.type === '4') {
		dataViewValue = data.formDetails.dataConfigValue;
	}
	let newMenuData = {
		//  id
		id: dataInfo.id ? dataInfo.id : null,
		//  配置名称
		name: dataInfo.name,
		//  数据类型来源
		source: dataInfo.source,
		//  数据展示方式
		showMethod: dataInfo.showMethod ? dataInfo.showMethod : "",
		//  数据类型
		type: dataInfo.type,
		//  数据长度
		length: dataInfo.length,
		//  父级ID
		configId: dataInfo.configId,
		//  是否隐藏
		isView: dataInfo.isView ? dataInfo.isView : 0,
		//  列展示方式
		displayMode: dataInfo.displayMode,
		//  数据对照值
		dataConfigValue: dataViewValue,
		//  数据排序
		sort: dataInfo.sort,
		//  配置描述
		remark: dataInfo.remark
	};

	//页面加载中
	data.menuLoading = true;
	await detailsForm.value.validate((valid) => {
		if (valid) {
			dataStructure.saveDetails(newMenuData).then(res => {
				if (res.code === 200) {
					data.openDetails = false;
					data.menuLoading = true;
					getDetailsInfoList();
					closeDialog();
					proxy.msgSuccess("新增成功");
				}
			}).catch(error => {
				proxy.msgError(error, "新增失败");
			});
		}
	});
	//释放页面加载
	data.menuLoading = false;
}
</script>

<style scoped>

</style>
