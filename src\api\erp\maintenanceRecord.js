import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/stock/conserve/erpConserveRecord/list',params)
  },
  downLoad: function (params) {
    return http.get('/erp/stock/conserve/erpConserveRecord/import/template',params,{responseType: 'blob'},true)
  },
  import: function () {
    return http.post('/erp/stock/conserve/erpConserveRecord/import')
  },
}