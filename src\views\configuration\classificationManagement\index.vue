<template>
	<el-container>
		<el-main style="padding: 10px">
			<el-card :body-style="{ height: '100%',width: '100%' }" style="height: 100%;width: 100%;">
				<el-container>
					<el-header height="52px" style="padding: 0">
						<div style="margin: 0 15px 15px 0">
							<el-button icon="Plus" plain type="primary" @click="save">
								新增
							</el-button>
							<el-button icon="Delete" plain type="danger" @click="deleteDetailsInfo">
								删除
							</el-button>
						</div>
					</el-header>
					<el-main style="padding: 0">
						<el-table :data="tableData" border default-expand-all lazy row-key="id"
								  style="height: 100%;width: 100%;" @selection-change="handleSelectionChange">
							<el-table-column align="center" min-width="30" type="selection" width="50"/>
							<el-table-column align="left" label="分类名称" prop="name"/>
							<el-table-column align="center" label="分类描述" prop="recordClassifyRemark"/>
							<el-table-column align="center" header-align="center" label="操作" width="220">
								<template #default="scope">
									<el-button icon="Plus" link type="success" @click="add(scope.row)">
										新增
									</el-button>
									<el-button icon="Edit" link type="primary" @click="savaList(scope.row)">
										编辑
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改配置对话框 -->
		<el-dialog v-show="openDetails" v-model="openDetails" :title="title" align-center append-to-body width="520px"
				   @close="closeDialog">
			<el-form ref="formInfo" v-loading="loading" :model="form" :rules="rules" label-width="auto">
				<el-form-item label="分类名称" prop="name">
					<el-input v-model="form.name" clearable placeholder="请输入分类名称"/>
				</el-form-item>
				<el-form-item label="序号" prop="sort">
					<el-input v-model="form.sort" clearable placeholder="请输入序号"/>
				</el-form-item>
				<el-form-item label="分类描述" prop="recordClassifyRemark">
					<el-input v-model="form.recordClassifyRemark" :autosize="{minRows: 5}" clearable maxlength="500"
							  placeholder="请输入分类描述"
							  show-word-limit type="textarea"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="savaDetails('1')">确 定</el-button>
					<el-button @click="closeDialog()">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue'
import classificationManagement from "@/api/archive/classificationManagement"

const {proxy} = getCurrentInstance();
const loading = ref(false);
const load = ref(false);
const formInfo = ref(null);
const openDetails = ref(false);
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 10,
	}
});
const parentId = ref('');
const {form} = toRefs(data);
const title = ref("");
const rules = ref({
	name: [
		{required: true, message: "分类名称不能为空", trigger: "blur"}
	],
	sort: [
		{required: true, message: "序号不能为空", trigger: "blur"},
		{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
	],
	recordClassifyRemark: [
		{required: true, message: "分类描述不能为空", trigger: "blur"}
	]
});
const multipleSelection = ref([]);
const tableData = ref([])

onBeforeMount(() => {
	treeData();
});

// 新增
function savaDetails() {
	proxy.$refs["formInfo"].validate(valid => {
		if (valid) {
			classificationManagement.save({
				parent: {
					id: parentId.value,
				},
				id: form.value.id,
				name: form.value.name,
				recordClassifyRemark: form.value.recordClassifyRemark,
				sort: form.value.sort
			}).then(res => {
				if (res.code === 200) {
					form.value = {};
					form.value = {};
					treeData();
					openDetails.value = false;
					proxy.msgSuccess(`${title.value}成功`);
				}
			}).catch((res) => {
				console.log(res);
				proxy.msgError('新增失败');
			})
		}
	})
}

function save() {
	title.value = "新增";
	form.value = {};
	openDetails.value = true;
}

function add(row) {
	title.value = "新增";
	form.value = {};
	parentId.value = row.id;
	openDetails.value = true;
}

// 修改
function savaList(row) {
	form.value = row;
	title.value = "修改";
	parentId.value = row.parent.id;
	openDetails.value = true;
}

// 删除
function handleSelectionChange(val) {
	multipleSelection.value = val
}

function deleteDetailsInfo() {
	proxy.$confirm('是否确认删除该条数据吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		let idStr = multipleSelection.value.map((v) => v.id);
		//拼接的数组字符串，接口传参
		let ids = idStr.join(",");
		classificationManagement.delete({ids}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('删除成功')
				treeData();
			} else {
				proxy.msgError('删除失败')
			}
		})
	}).catch(() => {
		proxy.msgError('取消删除');
	})
}

// 关闭弹窗
function closeDialog() {
	openDetails.value = false;
	form.value = {};
}

// 查询列表
function treeData() {
	load.value = true;
	classificationManagement.treeData().then(res => {
		if (res.code === 200) {
			tableData.value = res.data;
			load.value = false;
		}
	}).catch(() => {
		proxy.msgError('保存失败');
	})
}
</script>
