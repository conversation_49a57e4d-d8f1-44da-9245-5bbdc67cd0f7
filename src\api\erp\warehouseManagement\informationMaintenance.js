import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/storage/erpWarehouse/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/storage/erpWarehouse/delete',ids)
  },
  save: function (params) {
    return http.post('/erp/storage/erpWarehouse/save',params)
  },
  detail: function (id) {
    return http.get('/erp/storage/erpWarehouse/queryById',id)
  },
  logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
}