<template>
	<div class="common-layout">
		<el-container>
			<el-header class="headerBox">
				<el-form :inline="true" :model="formInfo" class="demo-form-inline" label-position="right"
						 style="height: 100%;">
					<el-form-item label="报表名称: ">
						<el-input v-model="formInfo.reportInfoName" placeholder="请输入需要搜索的内容" clearable
								  style="width: 220px"/>
					</el-form-item>
					<el-form-item>
						<el-button plain type="primary" @click="getList">
							查询
						</el-button>
						<el-button plain type="warning" @click="() => {formInfo.reportInfoName = '';getList()}">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-header>
			<el-main>
				<div class="reportPublicBox bodyBox">
					<h3 style="border-bottom: 1px solid #2878FF; padding: 8px 5px 10px 5px;margin-bottom: 10px;font-size: 16px">
						公用报表
					</h3>
					<div>
						<div v-if="reportPublicData.length > 0" class="reportBox">
							<el-card v-for="item in reportPublicData" :key="item.id" :body-style="{ padding: '0px' }"
									 class="datasource" shadow="hover">
								<template #header>
									<div class="card-header-tag">
										<div
											style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;justify-content: flex-start;align-items: center;width: 72%">
											<el-avatar :icon="MessageBox"/>
											<el-tooltip :content="item.reportName" class="box-item" effect="dark"
														placement="top">
												<div
													style="margin-left: 10px;font-size: 14px;max-width: 72%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
													{{ item.reportName }}
												</div>
											</el-tooltip>
										</div>
										<el-tag class="ml-2" effect="dark">{{
												item.reportPlatform ? formDict(platFormList, item.reportPlatform) : "无所属平台"
											}}
										</el-tag>
									</div>
								</template>
								<div style="text-align: left; padding: 15px 15px 10px 15px;font-size: 12px">
									<p>创建人: {{ item.createBy.name }}</p>
									<p>
										最后更新时间: {{
											moment(item.updateDate ? item.updateDate : item.createDate).format('YYYY-MM-DD HH:mm:ss')
										}}
									</p>
								</div>
								<el-divider style="margin: 0"/>
								<div class="toolBox">
									<el-tooltip class="box-item" content="预览" effect="dark" placement="top">
										<el-button link type="primary" @click="preview(item.reportUrl, 1)">
											<el-icon>
												<Search/>
											</el-icon>
										</el-button>
									</el-tooltip>
								</div>
							</el-card>
						</div>
						<div v-else style="width: 100%;height: 150px;display: flex;flex-direction: row;flex-wrap: nowrap;
							align-content: center;justify-content: center;align-items: center;">
							<img alt="暂无数据" src="../../../public/img/empty.png">
							<p style="text-align: center;font-size: 16px;font-weight: bold;margin-left: 10px">
								暂无数据
							</p>
						</div>
					</div>
					<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
						justify-content: flex-end;align-items: center;">
						<pagination style="padding: 20px" :total="publicTotal" v-model:page="publicQueryParams.current"
									v-model:limit="publicQueryParams.size"
									@pagination="getList()"/>
					</div>
				</div>
				<div class="reportPrivateBox bodyBox">
					<div style="border-bottom: 1px solid #2878FF; padding: 8px 5px 10px 5px;margin-bottom: 10px;display: flex;
					flex-direction: row;flex-wrap: nowrap;align-content: center;justify-content: space-between;
    				align-items: center;">
						<h3 style="font-size: 16px">私有报表</h3>
					</div>
					<div>
						<div v-if="reportPrivateData.length > 0" class="reportBox">
							<el-card v-for="item in reportPrivateData" :key="item.id" :body-style="{ padding: '0px' }"
									 class="datasource" shadow="hover">
								<template #header>
									<div class="card-header-tag">
										<div
											style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;justify-content: flex-start;align-items: center;width: 71%">
											<el-avatar :icon="MessageBox"/>
											<el-tooltip :content="item.reportName" class="box-item" effect="dark"
														placement="top">
												<div
													style="margin-left: 10px;font-size: 14px;max-width: 72%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
													{{ item.reportName }}
												</div>
											</el-tooltip>
										</div>
										<el-tag class="ml-2" effect="dark">{{
												item.reportPlatform ? formDict(platFormList, item.reportPlatform) : "无所属平台"
											}}
										</el-tag>
									</div>
								</template>
								<div style="text-align: left; padding: 15px 15px 10px 15px;font-size: 12px">
									<p>创建人: {{ item.createBy.name }}</p>
									<p>最后更新时间: {{
											moment(item.updateDate ? item.updateDate : item.createDate).format('YYYY-MM-DD HH:mm:ss')
										}}</p>
								</div>
								<el-divider style="margin: 0"/>
								<div class="toolBox">
									<el-tooltip class="box-item" content="预览" effect="dark" placement="top">
										<el-button link type="primary" @click="preview(item.reportUrl, 2)">
											<el-icon>
												<Search/>
											</el-icon>
										</el-button>
									</el-tooltip>
									<el-divider direction="vertical" style="height: 80%;margin: 0"/>
									<el-tooltip class="box-item" content="设计" effect="dark" placement="top">
										<el-button link type="success" @click="design">
											<el-icon>
												<EditPen/>
											</el-icon>
										</el-button>
									</el-tooltip>
								</div>
							</el-card>
						</div>
						<div v-else style="width: 100%;height: 150px;display: flex;flex-direction: row;flex-wrap: nowrap;
							align-content: center;justify-content: center;align-items: center;">
							<img alt="暂无数据" src="../../../public/img/empty.png">
							<p style="text-align: center;font-size: 16px;font-weight: bold;margin-left: 10px">
								暂无数据</p>
						</div>
					</div>
					<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
						justify-content: flex-end;align-items: center;">
						<pagination style="padding: 20px" :total="privateTotal"
									v-model:page="privateQueryParams.current"
									v-model:limit="privateQueryParams.size"
									@pagination="getList()"/>
					</div>
				</div>
			</el-main>
		</el-container>
	</div>
</template>
<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref} from "vue";
import {MessageBox} from '@element-plus/icons-vue';
import moment from "moment/moment";
import AlreadyHaveReport from "@/api/report/manager/AlreadyHaveReport";
import router from "@/router";
import tool from '@/utils/tool';

const publicTotal = ref(0);
const privateTotal = ref(0);
const reportPublicData = ref([]);
const reportPrivateData = ref([]);
const publicQueryParams = ref({
	current: 1,
	size: 10,
});
const privateQueryParams = ref({
	current: 1,
	size: 10,
});
const formInfo = reactive({
	reportInfoName: '',
	reportPlatform: ''
});
const {proxy} = getCurrentInstance();
const platFormList = ref([]);
const formDict = (data, val) => {
	return proxy.selectDictLabel(data, val);
}

//页面挂载方法
onBeforeMount(() => {
	platFormList.value = proxy.getDictList("report_platform").then((res) => platFormList.value = res);
	getList();
});

//查询方法
function getList() {
	let userInfo = tool.data.get("USER_INFO");
	AlreadyHaveReport.listByOpen({
		reportEnable: "1",
		reportOpenLevel: "1",
		reportPlatform: "3",
		reportName: formInfo.reportInfoName,
		current: publicQueryParams.value.current,
		size: publicQueryParams.value.size
	}).then((res) => {
		if (res.code === 200) {
			reportPublicData.value = res.data.records;
			publicTotal.value = res.data.total;
		}
	}).catch((error) => {
		console.log(error);
		proxy.msgError('公有报表查询失败');
	});
	AlreadyHaveReport.list({
		reportEnable: "1",
		reportOpenLevel: "2",
		reportPlatform: "3",
		reportName: formInfo.reportInfoName,
		current: privateQueryParams.value.current,
		'createBy.id': userInfo.id,
		size: privateQueryParams.value.size
	}).then((res) => {
		if (res.code === 200) {
			reportPrivateData.value = res.data.records;
			privateTotal.value = res.data.total;
		}
	}).catch((error) => {
		console.log(error);
		proxy.msgError('私有报表查询失败');
	});
}

function addReport() {
	router.push({path: "/report/ureport/designer"});
}

function preview(report, type) {
	let userInfo = tool.data.get("USER_INFO");
	let url = `/ureport/preview?_u=file:${report}`;
	if (type === 1) {
		url += `&org=${userInfo.sysOrg.id}`;
	}
	router.push({
		path: `/report/ureport/preview`,
		query: {
			iframeUrl: url,
			title: '预览报表'
		}
	});
}

function design() {
	let token = tool.cookie.get("TOKEN");
	let url = `${process.env.VUE_APP_REPORT + '85'}/#/login?systemCode=${token}`;
	window.open(url, '_blank');
}
</script>
<style scoped>
.demo-form-inline .el-input {
	--el-input-width: 220px;
}

.bodyBox {
	background-color: white;
	border-radius: 10px;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
}

.reportBox {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 12px;
}

/deep/ .card-header {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: flex-start;
	align-items: center;
}

/deep/ .card-header-tag {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: space-between;
	align-items: center;
}

/deep/ .el-card__header {
//padding: 0 15px 0 10px;
}

/deep/ .el-card__body {
	padding: 15px 15px 10px 15px;
}

.boxBottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
	align-content: center;
	flex-wrap: nowrap;
	flex-direction: row;
}

.previewBtn {
//background-color: #2878FF; border-radius: 0 0 0 12px; padding: 15px 20px; //color: white; position: relative; //top: -8px; right: -15px;
}

.reportHeaderBox {
	padding: 15px;
	margin-bottom: 15px;
}

.reportPublicBox {
	padding: 5px 12px;
	margin-bottom: 15px;
//height: calc(50% - 20px);
}

.reportPrivateBox {
	padding: 5px 12px;
	height: auto;
}

.toolBox {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: space-evenly;
	align-items: center;
	height: 45px;
}

.imageBox {
	display: block;
	width: 100%;
	object-fit: contain;
}

.datasource {
	margin-bottom: 12px;
}
</style>
