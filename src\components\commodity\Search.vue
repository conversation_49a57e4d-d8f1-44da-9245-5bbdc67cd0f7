<template>
	<el-card body-style="padding-bottom:2px" class="box-card">
		<el-form
			ref="queryRef"
			:inline="true"
			:model="searchForm"
			class="form_130"
			label-width="130px"
		>
			<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
				<el-form-item label="通用名" prop="noticeTitle">
					<el-input
						v-model="searchForm.n1"
						class="form_225"
						clearable
						placeholder="请输入通用名"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item label="商品名" prop="noticeContent">
					<el-input
						v-model="searchForm.n2"
						class="form_225"
						clearable
						placeholder="请输入商品名"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item label="拼音码" prop="noticeContent">
					<el-input
						v-model="searchForm.n3"
						class="form_225"
						clearable
						placeholder="请输入拼音码"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item v-show="showSearch" label="自编码" prop="noticeContent">
					<el-input
						v-model="searchForm.n4"
						class="form_225"
						clearable
						placeholder="请输入自编码"
						style="width: 220px"
					/>
				</el-form-item>

				<el-form-item
					v-show="showSearch"
					label="审核状态"
					prop="noticeContent"
				>
					<el-select
						v-model="searchForm.status"
						class="form_225"
						placeholder="请选择审核状态"
						style="width: 220px"
					>
						<el-option label="全部" value=""/>
						<el-option v-for="(item,index) in statusType" :key="index" :label="item.name"
								   :value="item.value"/>
					</el-select>
				</el-form-item>

				<el-form-item
					v-show="showSearch"
					label="创建时间"
					prop="noticeContent"
				>
					<div class="xBox">
						<el-date-picker
							v-model="searchForm.n6"
							class="form_225"
							end-placeholder=""
							format="YYYY/MM/DD HH:mm:ss"
							range-separator="至"
							size="default"
							start-placeholder=""
							style="width: 100%"
							type="datetimerange"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</div>
				</el-form-item>

				<el-form-item
					v-show="showSearch"
					label="修改时间"
					prop="noticeContent"
				>
					<div class="xBox">
						<el-date-picker
							v-model="searchForm.n7"
							class="form_225"
							end-placeholder=""
							format="YYYY/MM/DD HH:mm:ss"
							range-separator="至"
							size="default"
							start-placeholder=""
							style="width: 100%"
							type="datetimerange"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</div>


				</el-form-item>

			</TopTitle>
		</el-form>

	</el-card>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import TopTitle from "@/components/topTitle/index.vue";

const {proxy} = getCurrentInstance();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const searchForm = ref({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	status: "",
	n6: "",
	n7: "",
	n8: "",
	n9: "",
});
const showSearch = ref(false);
const data = reactive({});
const statusType = ref(null)
const emit = defineEmits(["handleQuery"]);

async function dict() {
	statusType.value = await proxy.getDictList('product_status')
}

const handleQuery = () => {
	emit("handleQuery");
};
const resetQuery = () => {
	for (let i in searchForm.value) {
		searchForm.value[i] = "";
	}
	emit("handleQuery");
};
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	dict()
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	searchForm
});
</script>
<style lang="scss" scoped>
.butns {
	text-align: center;
}

.xBox {
	width: 220px;
}
</style>
