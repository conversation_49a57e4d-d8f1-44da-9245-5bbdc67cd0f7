import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/storage/erpInventory/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/storage/erpInventory/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/storage/erpInventory/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/storage/erpInventory/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/storage/erpInventory/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/storage/erpInventory/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/storage/erpInventory/import',
      data
    )
  }
}
