import http from "@/utils/request"

/*
 *@description: 系统配置
 *@author: 路正宁
 *@date: 2023-03-17 11:56:12
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/properties/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/properties/delete',
       {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
       '/sys/properties/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/properties/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/sys/properties/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/properties/export',
       params,
       'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/sys/properties/import',
       data
    )
  }
}
