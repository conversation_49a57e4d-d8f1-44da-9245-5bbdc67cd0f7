/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-11 10:08:03
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-07 16:22:03
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\manufacturerManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
  getList: function (inputForm) {
    return http.get('/erp/customer/erpCustomers/list', inputForm)
  },

  delete: function (ids) {
    return http.delete('/erp/customer/erpCustomers/delete', ids)
  },

  detail: function (id) {
    return http.get('/erp/customer/erpCustomers/queryById', id)
  },

  save: function (data) {
    return http.post('/erp/customer/erpCustomers/save', data)
  },

  delFlag: function (params) {
    return http.get('/erp/customer/erpCustomers/update', params)
  },

  exportExcel: function (params) {
    return http.get('/sys/notice/export', params, 'blob')
  },

  importExcel: function (data) {
    return http.post('/sys/notice/import', data)
  },
  release: function ({ id, status }) {
    return http.get(`/sys/notice/updateStatusById?id=${id}&status=${status}`)
  },
  shopList: function (params) {
    return http.get(`/erp/product/erpCommodity/list`, params)
  },
  getTerat: function (params) {
    return http.get(`/erp/assist/erpTreatmentScopeSet/treeData`, params)
  },
  getRange: function (params) {
    return http.get(`/erp/assist/erpMassRangeSet/treeData`, params)
  },
  getRangeId: function (params) {
    return http.get(`/erp/assist/diagnosisTreatmentScopeConfig/listByCus`, params)
  },
  getWightConfig: function (params) {
    return http.get(`/erp/assist/erpMassRangeSet/treeDataEnableByDictid`, params)
  },
  getWightConfigDetail: function (params) {
    return http.get(`/erp/assist/erpMassRangeSet/treeDataEnable`, params)
  },
  // 删除许可证信息
  deleteLicence: function (params) {
    return http.delete(`/erp/customer/erpCustomersLicence/delete`, params)
  },
  // 删除客户委托
  erpCustomersDelegate: function (ids) {
    return http.delete('/erp/customer/erpCustomersDelegate/delete', ids)
  },
  // 删除客户委托商品
  erpCustomersDelegateCommodity: function (ids) {
    return http.delete('/erp/customer/erpCustomersDelegateCommodity/delete', ids)
  },
  // 删除质保协议
  erpCustomersQuality: function (ids) {
    return http.delete('/erp/customer/erpCustomersQuality/delete', ids)
  },
  // 删除附件
  erpCommonFile: function (ids) {
    return http.delete('/erp/common/erpCommonFile/delete', ids)
  },
  // 审核记录
  erpCustomersApproval: function (params) {
    return http.get(`/erp/customer/erpCustomersApproval/list`, params)
  },
  checkLicenseCode: function (params) {
    return http.get(`/erp/customer/erpCustomers/checkLicenseCode`, params)
  },
  
}
