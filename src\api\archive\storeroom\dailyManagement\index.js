import http from "@/utils/request"
export default {
  //人员进出记录列表 
  list: function (params) {
    return http.get('/archive/warehouse/personEnterOrOut/list', params)
  },
  //保存人员进出记录   
  save: function (params) {
    return http.post('/archive/warehouse/personEnterOrOut/save', params)
  },
  //删除人员进出记录
  delete: function (ids) {
    return http.delete('/archive/warehouse/personEnterOrOut/delete', ids)
  },
  
  //温湿度记录列表 
  humitureList: function (params) {
    return http.get('/archive/warehouse/thermography/list', params)
  },
  //保存温湿度记录   
  humitureSave: function (params) {
    return http.post('/archive/warehouse/thermography/save', params)
  },
  //删除温湿度记录
  humitureDelete: function (ids) {
    return http.delete('/archive/warehouse/thermography/delete', ids)
  },
}