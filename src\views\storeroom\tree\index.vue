<template>
    <div class="comDoorTable" v-loading="loading">
        <el-header>
            <el-input placeholder="输入关键字进行过滤" v-model="menuFilterText" clearable></el-input>
        </el-header>
        <el-tree ref="treeRef" :data="menuList" :props="menuProps" :filter-node-method="menuFilterNode" default-expand-all
            @node-click="leftHandleCurrentChange">
            <template #default="{ data }">
                <span class="custom-tree-node">
                    <span class="label">
                        {{ data.name }}
                    </span>
                </span>
            </template>
        </el-tree>
    </div>
</template>
<script setup>
import storeroomData from "@/api/archive/storeroom/storeroom";
import { reactive, ref, getCurrentInstance, toRefs, onBeforeMount, defineEmits, watch } from 'vue'
const treeRef = ref()
const menuList = ref();
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    }
})
const menuFilterText = ref('')
const menuProps = ref({
    children: 'children',
    label: 'name'
})
watch(menuFilterText, (val) => {
    treeRef.value.filter(val);
})
const menuFilterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
}

const loading = ref(true)
const total = ref(0)
const { queryParams } = toRefs(data)
/** 查询全宗列表 */
async function getList() {
    await storeroomData.treeData(queryParams.value).then(res => {
        if (res.code === 200) {
            menuList.value = res.data;
            loading.value = false;
            total.value = res.data.total;
        }
    });
}
const emit = defineEmits(['clickChild'])
function leftHandleCurrentChange(val) {
    emit('clickChild', val)
}
onBeforeMount(() => {
    getList();
});
</script>
<style scoped>
.comDoorTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}

.comDoorTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}

.comDoorTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
</style>
