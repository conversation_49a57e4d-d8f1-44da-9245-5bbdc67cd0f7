import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/supplier/erpSupplierProduction/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/supplier/erpSupplierProduction/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/supplier/erpSupplierProduction/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProduction/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/supplier/erpSupplierProduction/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProduction/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/supplier/erpSupplierProduction/import',
      data
    )
  }
}
