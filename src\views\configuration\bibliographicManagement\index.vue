<template>
	<el-container>
		<el-header height="auto" style="background: none;border-bottom: none;padding: 10px 10px 5px 10px">
			<el-card style="height: 100%;width: 100%">
				<el-input v-model="dataNameSearch" class="w-50 m-2" placeholder="请输入需要查询的名称"
						  style="width: 521px;" @keydown.enter="getRecordList"/>
				<el-button icon="Search" style="margin-left: 20px;" type="primary" @click="getRecordList">
					查询
				</el-button>
				<el-button icon="RefreshRight" plain @click="() => {dataNameSearch = '';getRecordList();}">
					重置
				</el-button>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px">
			<el-card :body-style="{height: '100%',width: '100%',padding: '20px 20px 0 20px'}"
					 style="height: 100%;width: 100%">
				<el-container>
					<el-header height="auto" style="background: none;border-bottom: none;padding: 0 0 15px 0">
						<el-button icon="Plus" plain type="primary" @click="handleAdd()">
							新增
						</el-button>
					</el-header>
					<el-main style="padding: 0">
						<el-table v-loading="loading" :data="list" border style="height: 100%;width: 100%">
							<el-table-column align="center" label="著录名称" prop="recordRuleName"/>
							<el-table-column align="center" label="著录项字段" prop="recordRuleConfigItem" width="162">
								<template #default="props">
									<span>{{ matchInfoByConfigItem(props.row.recordRuleConfigItem) }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="著录类型" prop="recordRuleType" width="162">
								<template #default="props">
									<span>{{ props.row.recordRuleType === '1' ? '全宗' : '门类' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="著录目标ID" prop="recordRuleTargetId"/>
							<el-table-column align="center" label="著录内容" prop="recordRuleContent">
								<template #default="scope">
									{{ matchInfoByItem(scope.row.recordRuleContent) }}
								</template>
							</el-table-column>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
											 width="162">
								<template #default="scope">
									<el-button icon="Edit" link type="primary" @click="handleAdd(scope.row)">
										编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="handleDelete(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 0" @pagination="getRecordList"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-show="open" :key="componentKey" v-model="open" :title="title" align-center append-to-body
				   width="720px">
			<el-form v-loading="loading" ref="formRef" :model="form" :rules="rules" label-width="150px"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="著录名称" prop="recordRuleName">
					<el-input v-model="form.recordRuleName" placeholder="请输入著录名称"/>
				</el-form-item>
				<el-form-item label="著录项字段" prop="recordRuleConfigItem">
					<el-select v-model="form.recordRuleConfigItem" placeholder="请选择著录项字段" style="width: 100%;">
						<el-option v-for="item in data.ruleConfigItem" :key="item.value" :label="item.name"
								   :value="item.value"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="著录类型" prop="recordRuleType">
					<el-select v-model="form.recordRuleType" placeholder="请选择著录类型" style="width: 100%;"
							   @change="() => form.recordRuleTargetId = ''">
						<el-option label="全宗" value="1"/>
						<el-option label="门类" value="2"/>
					</el-select>
				</el-form-item>
				<el-form-item label="著录目标" prop="recordRuleTargetId" v-if="form.recordRuleType === '1'">
					<el-select v-model="form.recordRuleTargetId" placeholder="请选择著录目标" style="width: 100%;">
						<el-option :label="item.recordGroupName" :value="item.id" v-for="item in respectList"
								   :key="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="著录目标" prop="recordRuleTargetId" v-if="form.recordRuleType === '2'">
					<el-tree-select v-model="form.recordRuleTargetId" :data="categoryList" highlight-current
									check-strictly
									:props="{ value: 'id', label: 'name' }" style="width: 100%">
						<template #default="{ data }">
							<span style="float: left;">{{ data.name }}</span>
							<el-tooltip class="box-item" effect="dark" :content="matchInfoById(data.recordGroupId)"
										placement="right">
								<span style="float: right;">所属全宗</span>
							</el-tooltip>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item label="著录内容" prop="recordRuleContent">
					<el-select v-model="form.recordRuleContent"
							   :popper-append-to-body="false"
							   clearable
							   collapse-tags
							   collapse-tags-tooltip
							   multiple
							   placeholder="请选择著录内容"
							   style="width: 100%;"
							   @change="handleSelectionChange">
						<el-option v-for="item in data.reviewStatus" :key="item.value" :label="item.name"
								   :value="item.value"/>
					</el-select>
					<span v-if="recordSelect.length !== '0'">{{ matchInfoByItem(recordSelect) }}</span>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定</el-button>
					<el-button @click="() => { open = false; getRecordList(); }">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue'
import bibliographic from '@/api/archive/bibliographicManagement/bibliographic.js';
import category from '@/api/archive/categoryManagement/category';
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';

const {proxy} = getCurrentInstance();
const list = ref([{status: '1'}]);
// 全宗List
const respectList = ref([]);
// 门类List
const categoryList = ref([]);
// 著录内容选中的标签
const recordSelect = ref('');
const open = ref(false);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const groupId = ref('');
const componentKey = ref(0);
const dataNameSearch = ref("");
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 10,
	},
	rules: {
		recordRuleName: [{required: true, message: "著录名称不能为空", trigger: "blur"}],
		recordRuleConfigItem: [{required: true, message: "著录项字段不能为空", trigger: "blur"}],
		recordRuleTargetId: [{required: true, message: "著录目标不能为空", trigger: ""}],
		recordRuleContent: [{required: true, message: "著录内容不能为空", trigger: "blur"}],
		recordRuleType: [{required: true, message: "著录类型不能为空", trigger: "blur"}],
	},
	reviewStatus: [],
	ruleConfigItem: []
});
const {queryParams, form, rules} = toRefs(data);

onBeforeMount(() => {
	dict();
	getGroupList();
	getCategoryList();
	getRecordList();
})

function submitForm() {
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			let oldRecordRuleContent = form.value.recordRuleContent;
			form.value.recordRuleContent = oldRecordRuleContent.join(',');
			form.value.id = groupId.value;
			bibliographic.save(form.value).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess(`${title.value}成功`);
					open.value = false;
					getRecordList();
				}
			}).catch(() => {
				proxy.msgError('保存失败');
			})
		}
	})
}

//著录内容选中的标签
function handleSelectionChange(selectedItems, type) {
	if (type === 1) {
		return recordSelect.value = selectedItems;
	} else {
		// selectedItems是一个数组，包含当前选中的所有值
		return recordSelect.value = selectedItems.join(',');
	}
}

function matchInfoById(id) {
	let groupName = '';
	respectList.value.forEach(item => {
		if (item.id === id) {
			groupName = item.recordGroupName;
		} else {
			groupName = '未绑定全宗';
		}
	})
	return groupName;
}

function matchInfoByConfigItem(configItem) {
	let groupName = '';
	data.ruleConfigItem.forEach(item => {
		if (item.value === configItem) {
			groupName = item.name;
		}
	})
	return groupName;
}


function matchInfoByItem(item) {
	if (!item) return '';
	let groupItem = item.split(",");
	for (let i = 0; i < data.reviewStatus.length; i++) {
		for (let j = 0; j < groupItem.length; j++) {
			if (data.reviewStatus[i].value === groupItem[j]) {
				groupItem[j] = data.reviewStatus[i].name;
			}
		}
	}
	if (groupItem.length > 1) {
		groupItem = groupItem.join('-');
	} else {
		groupItem = groupItem.toString();
	}
	return groupItem;
}

const handleDelete = (row) => {
	proxy.$confirm('是否确认删除该条数据吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		bibliographic.delete({ids: row.id}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('删除成功')
				getRecordList();
			} else {
				proxy.msgError('删除失败')
			}
		})
	})
}

/** 查询列表 */
function getRecordList() {
	loading.value = true
	bibliographic.getRecordList({
		...queryParams.value,
		recordRuleName: dataNameSearch.value ? dataNameSearch.value : null
	}).then(res => {
		if (res.code === 200) {
			list.value = res.data.records;
			total.value = res.data.total
			loading.value = false
		}
	})
}

function handleAdd(row) {
	componentKey.value += 1;
	row ? (title.value = '修改') : (title.value = '新增');
	if (row) {
		groupId.value = row.id;
		const {recordRuleName, recordRuleConfigItem, recordRuleType, recordRuleTargetId, recordRuleContent} = row
		form.value = {
			recordRuleName,
			recordRuleConfigItem,
			recordRuleType,
			recordRuleTargetId,
			recordRuleContent
		}
		handleSelectionChange(form.value.recordRuleContent, 1)
		let oldRecordRuleContent = row.recordRuleContent;
		if (oldRecordRuleContent.indexOf(",") >= 0) {
			form.value.recordRuleContent = row.recordRuleContent.split(",")
		} else if (oldRecordRuleContent.indexOf(",") < 0) {
			form.value.recordRuleContent = Array.of(row.recordRuleContent);
		}
	} else {
		form.value = {};
		handleSelectionChange('', 1)
	}
	open.value = true;
}

async function dict() {
	data.reviewStatus = await proxy.getDictList("rule_config");
	data.ruleConfigItem = await proxy.getDictList("rule_config_item");
}

// 查询全宗列表
function getGroupList() {
	completeManagement.getList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			respectList.value = res.data.records;
		}
	})
}

// 查询门类列表
function getCategoryList() {
	category.list({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			categoryList.value = res.data
		}
	})
}
</script>

<style scoped>

</style>
