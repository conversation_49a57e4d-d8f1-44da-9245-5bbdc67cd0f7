import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/storage/erpWarehouseTurnoverBox/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/storage/erpWarehouseTurnoverBox/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/storage/erpWarehouseTurnoverBox/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/storage/erpWarehouseTurnoverBox/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/storage/erpWarehouseTurnoverBox/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/storage/erpWarehouseTurnoverBox/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/storage/erpWarehouseTurnoverBox/import',
      data
    )
  }
}
