# archive-manage-front

#### 1.介绍

档案管理系统前端

#### 2.技术栈

- Vue
- Vuex
- Vue-router
- Element-ui
- axios
- vue-i18n
- vue-awesome-swiper
- vue-lazyload
- vuex-persistedstate
- vue-cropper
- vue-echarts
- vue-amap
- vue-amap-plugin-markerclusterer

#### 3.软件架构

[public](public) 目录下为静态资源文件

[src](src) 目录下为源码文件

[src/api](src/api) 目录下为接口文件

[src/assets](src/assets) 目录下为静态资源文件

[src/components](src/components) 目录下为组件文件

[src/config](src/config) 目录下为配置文件

[src/directives](src/directives) 目录下为工具文件

[src/layout](src/layout) 目录下为布局文件

[src/router](src/router) 目录下为路由文件

[src/store](src/store) 目录下为状态管理文件

[src/utils](src/utils) 目录下为工具文件

[src/views](src/views) 目录下为页面文件

[src/views/login](src/views/login) 目录下为登录页面文件

#### 4.安装教程

```sh
npm i
```

或者

```sh
npm install
```

#### 5.使用说明

在安装完成后使用

```sh
npm run serve
```

#### 6.参与贡献

1. Fork 本仓库
2. 新建 Feat_xxx 分支
3. 提交代码
4. 新建 Pull Request
