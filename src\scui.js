import config from "./config";
import api from "./api";
import tool from "./utils/tool";
import objectUtils from "./utils/objectUtils";
import messageBox from "./utils/messageBox";
import response from "./utils/response";
import http from "./utils/request";
import { permission, rolePermission } from "./utils/permission";

import scTable from "./components/scTable";
import ytzhTable from "./components/ytzhTable";
import comDoorTable from "./components/comDoorTable";
import scTableColumn from "./components/scTable/column.js";
import scFilterBar from "./components/scFilterBar";
import scUpload from "./components/scUpload";
import scUploadMultiple from "./components/scUpload/multiple";
import scUploadFile from "./components/scUpload/file";
import scFormTable from "./components/scFormTable";
import scTableSelect from "./components/scTableSelect";
import scPageHeader from "./components/scPageHeader";
import scSelect from "./components/scSelect";
import scDialog from "./components/scDialog";
import scForm from "./components/scForm";
import scTitle from "./components/scTitle";
import scWaterMark from "./components/scWaterMark";
import scQrCode from "./components/scQrCode";
import sysAreaCascader from "./components/system/sysAreaCascader";
import sysOrgSelect from "./components/system/sysOrgSelect";
import sysMenuSelect from "./components/system/sysMenuSelect";
import sysOfficeSelect from "./components/system/sysOfficeSelect";
import sysRoleSelect from "./components/system/sysRoleSelect";
import ytzhImageUpload from "./components/ytzhImageUpload";
import ytzhEditor from "./components/ytzhEditor";
import ytzhVideoUpload from "./components/ytzhVideoUpload";

import scStatusIndicator from "./components/scMini/scStatusIndicator";
import scTrend from "./components/scMini/scTrend";
import viewImg from "./components/viewImg";

import auth from "./directives/auth";
import auths from "./directives/auths";
import authsAll from "./directives/authsAll";
import role from "./directives/role";
import time from "./directives/time";
import copy from "./directives/copy";
import clickOutside from "./directives/clickOutside";
import errorHandler from "./utils/errorHandler";
import TopTitle from '@/components/topTitle'
//ERP>辅助功能>诊疗范围>树形表格
import diagnosisTreeTable from "./components/assist/diagnosisRange/Table.vue";
import massTreeTable from "./components/assist/massRange/Table.vue";
//ERP>商品管理>药品>搜索栏
import Sizer from "./components/commodity/Search.vue";
//ERP>销售管理>销售订单>搜素
import SearchCom from "./components/salesManagement/Search.vue";
//ERP>商品模块详情页
import compile from "./components/detailsForm/compile.vue";
//ERP>审核表单?
import auditForms from "./components/detailsForm/audit.vue";
//ERP>操作日志?
import logQuery from "./components/detailsForm/logQuery.vue";

import DragTableColumn from "./components/dragTableColumn";
import rightToptipBarV2 from './components/rightToptipBarV2'

import * as elIcons from "@element-plus/icons-vue";
import * as scIcons from "./assets/icons";

export default {
	install(app) {
		//挂载全局对象
		app.config.globalProperties.$CONFIG = config;
		app.config.globalProperties.$TOOL = tool;
		app.config.globalProperties.$ObjectUtils = objectUtils;
		app.config.globalProperties.$MessageBox = messageBox;
		app.config.globalProperties.$Response = response;
		app.config.globalProperties.$HTTP = http;
		app.config.globalProperties.$API = api;
		app.config.globalProperties.$AUTH = permission;
		app.config.globalProperties.$ROLE = rolePermission;

		//注册全局组件ytzhTable
		app.component("scTable", scTable);
		app.component("ytzhTable", ytzhTable);
		app.component("comDoorTable", comDoorTable);
		app.component("scTableColumn", scTableColumn);
		app.component("scFilterBar", scFilterBar);
		app.component("scUpload", scUpload);
		app.component("scUploadMultiple", scUploadMultiple);
		app.component("scUploadFile", scUploadFile);
		app.component("scFormTable", scFormTable);
		app.component("scTableSelect", scTableSelect);
		app.component("scPageHeader", scPageHeader);
		app.component("scSelect", scSelect);
		app.component("scDialog", scDialog);
		app.component("scForm", scForm);
		app.component("scTitle", scTitle);
		app.component("scWaterMark", scWaterMark);
		app.component("scQrCode", scQrCode);
		app.component("scStatusIndicator", scStatusIndicator);
		app.component("scTrend", scTrend);
		app.component("sysAreaCascader", sysAreaCascader);
		app.component("sysOrgSelect", sysOrgSelect);
		app.component("sysMenuSelect", sysMenuSelect);
		app.component("sysOfficeSelect", sysOfficeSelect);
		app.component("sysRoleSelect", sysRoleSelect);
		app.component("ytzhImageUpload", ytzhImageUpload);
		app.component("ytzhVideoUpload", ytzhVideoUpload);
		app.component("ytzhEditor", ytzhEditor);
		app.component("diagnosisTreeTable", diagnosisTreeTable);
		app.component("massTreeTable", massTreeTable);
		app.component("Sizer", Sizer);
		app.component("SearchCom", SearchCom);
		app.component("compile", compile);
		app.component("auditForms", auditForms);
		app.component("logQuery", logQuery);
		app.component("viewImg", viewImg);
		app.component("TopTitle", TopTitle);
		app.component("DragTableColumn", DragTableColumn);
		app.component("RightToptipBarV2", rightToptipBarV2);
		
		
		//注册全局指令
		app.directive("auth", auth);
		app.directive("auths", auths);
		app.directive("auths-all", authsAll);
		app.directive("role", role);
		app.directive("time", time);
		app.directive("copy", copy);
		app.directive("clickOutside", clickOutside);

		//统一注册el-icon图标
		for (let icon in elIcons) {
			app.component(`ElIcon${icon}`, elIcons[icon]);
		}
		//统一注册sc-icon图标
		for (let icon in scIcons) {
			app.component(`ScIcon${icon}`, scIcons[icon]);
		}

		//关闭async-validator全局控制台警告
		window.ASYNC_VALIDATOR_NO_WARNING = 1;

		//全局代码错误捕捉
		app.config.errorHandler = errorHandler;
	},
};
