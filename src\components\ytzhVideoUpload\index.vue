<template>
  <el-upload
	  ref="upload"
	  :action="process.env.VUE_APP_API_UPLOAD"
	  :auto-upload="true"
	  :before-upload="beforeUploadVideo"
	  :data="{fileType:'video'}"
	  :file-list="fileList"
	  :headers="headers"
	  :on-change="handleChange"
	  :on-preview="handleOnPreview"
	  :on-progress="uploadVideoProcess"
	  :on-remove="handleRemove"
	  :on-success="handleVideoSuccess"
	  class="avatar-uploader"
  >
    <video
      v-if="videoForm.showVideoPath != '' && !videoFlag"
      :src="videoForm.showVideoPath"
      class="avatar video-avatar"
      controls="controls"
    >
      您的浏览器不支持视频播放
    </video>
    <i
      v-else-if="videoForm.showVideoPath == '' && !videoFlag"
      class="el-icon-plus avatar-uploader-icon"
    ></i>
    <el-progress
      v-if="videoFlag == true"
      type="circle"
      :percentage="videoUploadPercent"
      style="margin-top: 7px"
    ></el-progress>
  </el-upload>
</template>

<script>
export default {
  props: {
    fileLists: {
      type: Object,
      default: () => {
        [];
      },
    },
  },
  computed: {},
  data() {
    return {
      fileList: [],
      videoFlag: false,
      //是否显示进度条
      videoUploadPercent: "",
      //进度条的进度，
      isShowUploadVideo: false,
      //显示上传按钮
      videoForm: {
        showVideoPath: "", //回显的变量
      },
      headers: {
        Authorization: "",
		  clientType: 'PC'
      },
    };
  },
  watch: {
    fileLists() {
      this.fileList = this.fileLists;
      if (
        this.$ObjectUtils.isNotEmpty(this.fileList[this.fileList.length - 1].response)
      ) {
        this.videoForm.showVideoPath = this.fileList[this.fileList.length - 1].response.data.url;
      } else {
        this.videoForm.showVideoPath = this.fileList[this.fileList.length - 1].url;
      }
    },
  },
  mounted() {
    this.headers = {
      Authorization: "Bearer " + this.$TOOL.cookie.get("TOKEN"),
	  clientType: "PC"
    };
  },

  methods: {
    //上传前回调
    beforeUploadVideo(file) {
      var fileSize = file.size / 1024 / 1024 < 50; //控制大小  修改50的值即可
      if (
        [
          "video/mp4",
          "video/ogg",
          "video/flv",
          "video/avi",
          "video/wmv",
          "video/rmvb",
          "video/mov",
        ].indexOf(file.type) == -1 //控制格式
      ) {
        alert("请上传正确的视频格式");
        return false;
      }
      if (!fileSize) {
        alert("视频大小不能超过50MB");
        return false;
      }
      this.isShowUploadVideo = false;
    },
    //进度条
    uploadVideoProcess(event, file, fileList) {
      //注意在data中添加对应的变量名
      this.videoFlag = true;
      this.videoUploadPercent = file.percentage.toFixed(0) * 1;
    },
    /*
     * 上传文件处理并返回远程路径
     * @author: 路正宁
     * @date: 2023-04-18 14:33:04
     */
    async uploadVideo() {
      //远程文件路径
      var urls = [];
      //上传文件和远程路径处理
      for (var a = 0; a < this.fileList.length; a++) {
        if (this.fileList[a].status == "ready") {
          this.fileList.push(this.fileList[a].raw);
        }
        if (this.fileList[a].status == "success") {
          if (this.$ObjectUtils.isNotEmpty(this.fileList[a].response)) {
            urls.push(this.fileList[a].response.data.url);
          } else {
            urls.push(this.fileList[a].url);
          }
        }
      }
      return urls.join(",");
    },
    //上传成功回调
    handleVideoSuccess(res, file) {
      this.isShowUploadVideo = true;
      this.videoFlag = false;
      this.videoUploadPercent = 0;
      //后台上传数据
      if (res.code == 200) {
        this.videoForm.showVideoPath = res.data.url; //上传成功后端返回视频地址 回显
      } else {
        this.$Response.errorNotice(res, "[" + file.name + "]上传失败");
      }
    },
    /*
     * 同步文件上传列表
     * @author: 路正宁
     * @date: 2023-04-18 14:22:50
     */
    handleChange(file, fileLists) {
      this.fileList = fileLists;
      if (this.$ObjectUtils.isNotEmpty(file.response)) {
        this.videoForm.showVideoPath = file.response.data.url;
      } else {
        this.videoForm.showVideoPath = file.url;
      }
    },
    handleOnPreview(file){
      if (this.$ObjectUtils.isNotEmpty(file.response)) {
        this.videoForm.showVideoPath = file.response.data.url;
      } else {
        this.videoForm.showVideoPath = file.url;
      }
    },
    /*
     * 文件移除
     * @author: 路正宁
     * @date: 2023-04-18 09:30:20
     */
    async handleRemove(uploadFile, uploadFiles) {
      if (uploadFile.status != "success") {
        return;
      }
      var url = "";
      if (
        this.$ObjectUtils.isNotEmpty(uploadFile.response) &&
        this.$ObjectUtils.isNotEmpty(uploadFile.response.data.url)
      ) {
        url = uploadFile.response.data.url;
      } else {
        url = uploadFile.url;
      }
      var res = await this.$API.common.deleteFile.get(url);
      if (res.code == 200) {
        this.$message.success("操作成功");
      } else {
        this.$Response.errorNotice(res, "上传失败");
      }
    },
  },
};
</script>

<style scoped>
.ytzhTable {
}
.ytzhTable-table {
  height: calc(100% - 50px);
}
.ytzhTable-page {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.ytzhTable-do {
  white-space: nowrap;
}
.ytzhTable:deep(.el-table__footer) .cell {
  font-weight: bold;
}
.ytzhTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
  height: 12px;
  border-radius: 12px;
}
.ytzhTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
  width: 12px;
  border-radius: 12px;
}
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9 !important;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9 !important;
  border-radius: 6px !important;
  position: relative !important;
  overflow: hidden !important;
}
.avatar-uploader .el-upload:hover {
  border: 1px dashed #d9d9d9 !important;
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 300px;
  height: 178px;
  display: block;
}
</style>
