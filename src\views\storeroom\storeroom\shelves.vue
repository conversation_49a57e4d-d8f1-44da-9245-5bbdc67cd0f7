<!--
 * @Author: saya
 * @Date: 2023-07-20 09:18:51
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-07-20 09:20:05
 * @FilePath: \archive-manage-front\src\views\storeroom\storeroom\shelves.vue
 * @Description:
-->
<template>
	<div style="padding: 0 20px;">
		<el-tabs v-model="activeName" @tab-click="handleClick" style="width: 100%;">
			<el-tab-pane label="档案库" name="first">
				<img :src="houseInfoUrl" class="image">
			</el-tab-pane>
			<el-tab-pane label="档案室" name="second">
				<div style="display: grid;grid-template-columns: repeat(5, 1fr);grid-gap: 10px;">
					<el-card :body-style="{ padding: '0px' }" v-for="(item, index) in storeroomList" :key="index"
							 style="width: 255px;height: 340px;">
						<img :src="item.roomCoverUrl"
							 style="min-height: 152px;max-height: 152px;object-fit: contain;width: 100%;">
						<div style="padding: 2px 14px 0 14px;">
							<el-divider style="margin: 5px 0"></el-divider>
							<div class="information">
								<span>档案室名称&nbsp;:</span>
								<span>
									{{ item.roomName ? item.roomName : '暂无' }}
								</span>
							</div>
							<el-divider style="margin: 5px 0"></el-divider>
							<div class="information">
								<span>档案室编号&nbsp;:</span>
								<span>
									{{ item.roomNum ? item.roomNum : '暂无' }}
								</span>
							</div>
							<el-divider style="margin: 5px 0"></el-divider>
							<div class="information">
								<span>档案室所属仓库&nbsp;:</span>
								<span>
									{{ item.roomHouse ? item.roomHouse.houseName : '暂无' }}
								</span>
							</div>
							<el-divider style="margin: 5px 0"></el-divider>
							<div class="information">
								<span>档案室面积&nbsp;:</span>
								<span>
									{{ item.roomArea ? item.roomArea : '暂无' }} 平米
								</span>
							</div>
							<el-divider style="margin: 5px 0"></el-divider>
							<!-- <div class="information">档案室平面图路径:{{ item.roomPlaneUrl }}</div> -->
							<div class="information">
								<span>档案室管理员&nbsp;:</span>
								<span>
									{{ item.roomManager ? item.roomManager.name : '暂无' }}
								</span>
							</div>
							<el-divider style="margin: 5px 0"></el-divider>
							<div class="bottom clearfix">
								<el-button type="text" icon="View" class="button" @click="addShelves(item)">查看</el-button>
							</div>
						</div>
					</el-card>
				</div>
				<div style="position: fixed;bottom: 0;right: 0;">
					<pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
								@pagination="selectList"/>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import storeroomData from "@/api/archive/storeroom/storeroom";

export default {
	name: 'shelves',
	props: {
		parentDataInfo: {}
	},
	data() {
		return {
			activeName: 'first',
			total: 0,
			storeroomList: [],
			houseInfoUrl: '',
			queryParams: {
				current: 1,
				size: 10
			},
		}
	},
	mounted() {
		this.queryList()
		this.selectList();
	},
	methods: {
		addShelves(data) {
			this.$emit('shelvesView', data);
		},
		selectList() {
			console.log(this.parentDataInfo);
			this.storeroomList = [];
			this.houseInfoUrl = '';
			storeroomData.roomList({
				...this.queryParams,
				'roomHouse.id': this.parentDataInfo.recordHouseInfoTargetId ? this.parentDataInfo.recordHouseInfoTargetId : this.parentDataInfo.id
			}).then(result => {
				if (result.code === 200) {
					this.storeroomList = result.data.records;
					this.total = result.data.total;
				}
			}).catch(error => {
				console.log(error);
			});
		},
		queryList() {
			storeroomData.queryById({
				id: this.parentDataInfo.recordHouseInfoTargetId ? this.parentDataInfo.recordHouseInfoTargetId : this.parentDataInfo.id
			}).then(result => {
				if (result.code === 200) {
					this.houseInfoUrl = result.data.housePlaneUrl;
				}
			}).catch(error => {
				console.log(error);
			});
		}
	}
}
</script>

<style scoped>
.information {
	margin-top: 5px;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: space-between;
	align-items: center;
}

.bottom {
	margin-top: 5px;
	line-height: 12px;
}

.button {
	padding: 0;
	float: right;
}

.image {
	width: 100%;
	height: 100%;
	max-height: 82vh;
	object-fit: contain;
	display: block;
}

.pagination-container {
	padding: 15px 0.08333rem;
}
</style>
