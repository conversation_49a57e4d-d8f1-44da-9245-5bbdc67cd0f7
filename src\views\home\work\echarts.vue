<template>
    <div class="liquid-fill-chart">
        <div :id="chartId" style="width: 100%; height: 100%;"></div>
    </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import 'echarts-liquidfill';

const props = defineProps({
    value: {
        type: Number,
        required: true
    },
    hig: {
        type: Number,
        required: true
    },
    color: {
        type: String,
        default: '#20a5ff'
    },
    unit: {
        type: String,
        // default: '%'
    },
    // 渐变颜色配置
    gradientColors: {
        type: Array,
        default: () => []
    },
    tit: {
        type: String,
        default: '测试'
    },
    rate: Number,
});

const chartId = `liquid-fill-chart-${Math.random().toString(36).substr(2, 9)}`;
let myChart = null;

onMounted(() => {
    const container = document.getElementById(chartId);
    if (container) {
        myChart = echarts.init(container);
        initChart();
    }
});

onBeforeUnmount(() => {
    if (myChart) {
        myChart.dispose();
        myChart = null;
    }
});

const initChart = () => {
    const option = {
        title: [
            {    //这里可以写多个，然后每一个进行调整位置，就是上面效果图的样子，根据实际情况来定
                top: '40%',  // 这里可以调整值的位置
                left: 'center', //同上
                text: props.hig * 100 + props.unit,     //这里是显示的值
                textStyle: {
                    // color: '#FF4D4F',
                    fontStyle: 'normal',
                    fontWeight: 'bold',
                    fontSize: 16,
                },
            },{    //这里可以写多个，然后每一个进行调整位置，就是上面效果图的样子，根据实际情况来定
                top: '55%',  // 这里可以调整值的位置
                left: 'center', //同上
                text: props.tit,     //这里是显示的值
                textStyle: {
                    // color: '#FF4D4F',
                    fontStyle: 'normal',
                    fontWeight: '400',
                    fontSize: 14,
                },
            },
        ],
        series: [
            {
                color: [

                    {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: props.gradientColors[2]
                            },
                            {
                                offset: 1,
                                color: props.gradientColors[2]
                            }
                        ],
                    },
                    {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: props.gradientColors[0]
                            },
                            {
                                offset: 1,
                                color: props.gradientColors[1]
                            }
                        ],
                    }],
                // color: ['#0071F5', '#21C2FF'], //波浪的颜色
                type: 'liquidFill',
                radius: '90%',
                data: [
                    //波浪的高度占比 (第一个是浅色的 : 在传过来的数据上加上一点作为展示效果,第二个用传过来的数据)
                    {
                        value: props.hig + 0.01,
                    },
                    {
                        value: props.hig,
                    },
                ],
                center: ['50%', '50%'], //图在整个画布的位置
                backgroundStyle: {
                    color: 'white',
                    borderColor: props.gradientColors[0], //边框颜色
                    borderWidth: 2, //边框粗细
                    shadowColor: '#ffffff', //阴影颜色
                    shadowBlur: 20, //阴影范围
                },
                label: {
                    //水球图里面的文字喝字体等设置
                    normal: {
                        formatter:'',
                        // formatter: function (value) {
                        //     if (!value) {
                        //         return '加载中';
                        //     } else {
                        //         return props.hig * 100 + '%';
                        //     }
                        // },
                        textStyle: {
                            fontSize: 22,
                        },
                    },
                },
                outline: {
                    //水球图的外层边框 可设置 show:false  不显示
                    itemStyle: {
                        borderColor: '#DCDCDC',
                        borderWidth: 2,
                    },
                    borderDistance: 0,
                },
                itemStyle: {
                    opacity: 1,
                    shadowColor: 'rgba(255,255,255,1)',
                },
            },
        ],
    };

    if (myChart) {
        myChart.setOption(option);
    }
};
</script>

<style scoped>
.liquid-fill-chart {
    width: 100%;
    height: 160px;
}
</style>
