import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/factory/erpManufacturerGmpCertificate/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/factory/erpManufacturerGmpCertificate/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/factory/erpManufacturerGmpCertificate/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerGmpCertificate/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/factory/erpManufacturerGmpCertificate/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerGmpCertificate/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/factory/erpManufacturerGmpCertificate/import',
      data
    )
  }
}
