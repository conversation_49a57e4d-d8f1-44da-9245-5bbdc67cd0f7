import http from "@/utils/request";

// 系统配置  >  审核流程  api

export const flows = {
	role: function (params) {
		return http.get("/sys/role/list", params);
	},
	formData: function (params) {
		return http.get("/sys/dictValue/list", params);
	},
	flowData: function (params) {
		return http.get("/archive/audit/auditProcess/listZLC", params);
	},
	delFLow: function (params) {
		return http.delete("/archive/audit/auditProcess/delete", params);
	},
	saveFlow: function (data) {
		return http.post("/archive/audit/auditProcess/save", data);
	},
};

export const node = {
	nodeList: function (params) {
		return http.get("/archive/audit/auditNode/listNode", params);
	},
	tissue: function () {
		return http.get("/sys/org/list");
	},
	staff: function (params) {
		return http.get("/sys/user/list", params);
	},
	saveNode: function (data) {
		return http.post("/archive/audit/auditNode/save", data);
	},
	delNode: function (params) {
		return http.delete("/archive/audit/auditNode/delete", params);
	},
};
