import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/sales/erpSalesOrderForm/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/sales/erpSalesOrderForm/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/sales/erpSalesOrderForm/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrderForm/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/sales/erpSalesOrderForm/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrderForm/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/sales/erpSalesOrderForm/import',
      data
    )
  }
}
