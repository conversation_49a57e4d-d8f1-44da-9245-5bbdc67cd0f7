const timeData = 10 * 1000 // 检查间隔时间
let hidden = false // 页面是否隐藏
let setTimeoutId
let needTip = true // 默认开启提示

let oldScript = []
let newScript = []

const getHtml = async () => {
	//读取index html
	return await fetch('/', {
		headers: {'Pragma': 'no-cache', 'Cache-Control': 'no-cache, no-store, must-revalidate',},
	}).then(res => res.text())
}

// const scriptReg = /<script.*src=["'](?<src>[^"']+)/gm

const parserScript = (html) => {
	const reg = new RegExp(/<script(?:\s+[^>]*)?>(.*?)<\/script\s*>/ig) //script正则
	return html.match(reg) //匹配script标签
}

const init = async () => {
	const html = await getHtml()
	oldScript = parserScript(html)
}

const compareScript = async (oldArr, newArr) => {
	const base = oldArr.length
	const arr = Array.from(new Set(oldArr.concat(newArr)))
	let needRefresh = false
	if (arr.length !== base) {
		needRefresh = true
	}
	return needRefresh
}

// 自动更新
const autoUpdate = async () => {
	setTimeoutId = setTimeout(async () => {
		const newHtml = await getHtml()
		newScript = parserScript(newHtml)
		if (!hidden) {
			const willRefresh = await compareScript(oldScript, newScript)
			if (willRefresh && needTip) {
				// 延时更新，防止部署未完成用户就刷新空白
				setTimeout(() => {
					//*****右下角通知提示 */
					window.dispatchEvent(
						new CustomEvent("onmessageUpdate", {
							detail: {
								msg: "发现系统版本更新，是否现在刷新页面？",
							},
						})
					)
				}, 2000)
				needTip = false // 关闭更新提示，防止重复提醒
			}
		}
		if (needTip) {
			await autoUpdate()
		}
	}, timeData)
}

// 停止检测更新
const stop = () => {
	if (setTimeoutId) {
		clearTimeout(setTimeoutId)
		setTimeoutId = ''
	}
}

// 开始检查更新
const start = async () => {
	await init()
	await autoUpdate()
	// 监听页面是否隐藏
	document.addEventListener('visibilitychange', () => {
		hidden = document.hidden
		// 页面隐藏了就不检查更新。或者已经有一个提示框了，防止重复提示。
		if (!hidden && needTip) {
			autoUpdate()
		} else {
			stop()
		}
	})
}

export default {start}
