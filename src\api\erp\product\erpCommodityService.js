import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/product/erpCommodity/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/product/erpCommodity/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/product/erpCommodity/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/product/erpCommodity/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/product/erpCommodity/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/product/erpCommodity/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/product/erpCommodity/import',
      data
    )
  }
}
