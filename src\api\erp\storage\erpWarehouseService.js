import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/storage/erpWarehouse/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/storage/erpWarehouse/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/storage/erpWarehouse/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/storage/erpWarehouse/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/storage/erpWarehouse/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/storage/erpWarehouse/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/storage/erpWarehouse/import',
      data
    )
  }
}
