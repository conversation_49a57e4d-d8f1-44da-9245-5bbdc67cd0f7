<!--
 * @Author: saya
 * @Date: 2023-08-07 13:18:55
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-08-10 10:06:28
 * @FilePath: \archive-manage-front\src\views\managementFile\appraisalApproval\index.vue
 * @Description:
-->

<template>
    <el-container>
        <el-main ref="main" v-loading="loading" class="noPadding" style="">
			<el-card class="box-card" style="margin: 10px 10px 5px 10px ;">
				<div style="display: flex;">
					<el-form ref="formList" :inline="true" :model="form" label-position="right" label-width="auto">
						<el-form-item label="档案名称" prop="recordName" style="margin: 0;padding-right: 10px;">
							<el-input v-model="form.recordName" placeholder="请输入档案名称"/>
						</el-form-item>
						<el-form-item label="档案号" prop="recordNum" style="margin: 0;padding-right: 10px;">
							<el-input v-model="form.recordNum" placeholder="请输入档案号"/>
						</el-form-item>
						<el-form-item style="margin: 0;padding-left: 10px;">
							<el-button :icon="Search" type="primary" @click="() => getList()">查询</el-button>
							<el-button :icon="RefreshRight" plain @click="() => resetQuery()">重置</el-button>
						</el-form-item>
					</el-form>
                </div>
            </el-card>
			<el-card class="box-card" style="margin: 0 10px 10px 10px;">
				<el-table :data="receiveData">
					<el-table-column align="center" min-width="30" type="selection" width="80"/>
					<el-table-column align="left" label="档案名称" prop="record.name"/>
					<el-table-column align="center" label="档案号" prop="record.num"/>
					<el-table-column align="center" label="销毁鉴定人" prop="master.controlApply.name" width="150"/>
					<el-table-column align="center" label="销毁审批人" prop="master.controlAudit.name" width="150"/>
					<el-table-column align="center" label="销毁时间" prop="updateDate" width="180">
						<template #default="scope">
							{{
								(scope.row.createDate ?
									moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') :
									undefined) || '--'
							}}
						</template>
                    </el-table-column>
                </el-table>
                <div style="float: right;">
					<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
								style="padding: 22px" @pagination="getList"/>
                </div>
            </el-card>
        </el-main>
    </el-container>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import {RefreshRight, Search} from '@element-plus/icons-vue'
import approvalList from '@/api/archive/managementFile/appraisalApproval';
import moment from 'moment'

const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
})

const loading = ref(true)
const total = ref(0)
const {queryParams} = toRefs(data)
const {proxy} = getCurrentInstance()
// 数据List
const receiveData = ref([]);
// 头部查询
const form = ref({});

onMounted(() => {
	getList()
});

// 进入时查询全部
function getList() {
	loading.value = true;
	approvalList.list({
		master: {
			controlType: 2,
			controlStatus: 2
		},
		current: queryParams.value.current,
		size: queryParams.value.size,
		recordName: form.value.recordName,
		recordNum: form.value.recordNum
	}).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
			total.value = res.data.total;
			loading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 重置
function resetQuery() {
	form.value = [];
	getList()
}
</script>

<style scoped></style>
