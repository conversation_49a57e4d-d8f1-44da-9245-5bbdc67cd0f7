import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/audit/auditProcess/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/audit/auditProcess/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/audit/auditProcess/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/audit/auditProcess/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/audit/auditProcess/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/audit/auditProcess/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/audit/auditProcess/import',
      data
    )
  }
}
