import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/assist/erpBaseCommonItems/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/assist/erpBaseCommonItems/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/assist/erpBaseCommonItems/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/assist/erpBaseCommonItems/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/assist/erpBaseCommonItems/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/assist/erpBaseCommonItems/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/assist/erpBaseCommonItems/import',
      data
    )
  }
}
