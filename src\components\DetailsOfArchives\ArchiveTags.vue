<!--
 * @Author: AI Assistant
 * @Date: 2024-12-24
 * @Description: 档案标签公共组件
-->
<template>
  <div class="archive-tags">
    <!-- 标签模式 -->
    <div v-if="displayMode === 'tags'" class="tags-container">
      <template v-if="loading">
        <el-skeleton :rows="1" animated />
      </template>
      <template v-else-if="tags.length > 0">
        <el-tag
          v-for="tag in tags"
          :key="tag.id"
          :effect="judgeTheme(tag.tagTheme).type"
          :round="judgeShape(tag.tagRound).type"
          :type="judgeColor(tag.tagColor).type"
          class="tag-item"
          :class="{ 'clickable': clickable }"
          @click="handleTagClick(tag)"
        >
          {{ tag.tagName }}
        </el-tag>
      </template>
      <template v-else>
        <el-text type="info">{{ emptyText }}</el-text>
      </template>
    </div>

    <!-- 文本模式 -->
    <div v-else-if="displayMode === 'text'" class="text-container">
      <template v-if="loading">
        <el-skeleton :rows="1" animated />
      </template>
      <template v-else-if="tags.length > 0">
        <el-text>{{ tagNames.join(', ') }}</el-text>
      </template>
      <template v-else>
        <el-text type="info">{{ emptyText }}</el-text>
      </template>
    </div>

    <!-- 卡片模式 -->
    <div v-else-if="displayMode === 'cards'" class="cards-container">
      <template v-if="loading">
        <el-skeleton :rows="2" animated />
      </template>
      <template v-else-if="tags.length > 0">
        <div
          v-for="tag in tags"
          :key="tag.id"
          class="tag-card"
          :class="{ 'clickable': clickable }"
          @click="handleTagClick(tag)"
        >
          <div class="tag-card-header">
            <el-tag
              :effect="judgeTheme(tag.tagTheme).type"
              :round="judgeShape(tag.tagRound).type"
              :type="judgeColor(tag.tagColor).type"
              size="small"
            >
              {{ tag.tagName }}
            </el-tag>
          </div>
          <div v-if="tag.tagDescription" class="tag-card-content">
            <el-text size="small" type="info">{{ tag.tagDescription }}</el-text>
          </div>
        </div>
      </template>
      <template v-else>
        <el-text type="info">{{ emptyText }}</el-text>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import tagsManagement from '@/api/archive/tagsManagement'

const props = defineProps({
  // 标签ID字符串，逗号分隔
  tagIds: {
    type: String,
    default: ''
  },
  // 标签ID数组
  tagIdsArray: {
    type: Array,
    default: () => []
  },
  // 显示模式：tags(标签), text(文本), cards(卡片)
  displayMode: {
    type: String,
    default: 'tags',
    validator: (value) => ['tags', 'text', 'cards'].includes(value)
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },
  // 空数据时显示的文本
  emptyText: {
    type: String,
    default: '无标签'
  },
  // 是否自动加载标签详情
  autoLoad: {
    type: Boolean,
    default: true
  },
  // 外部传入的标签数据
  externalTags: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['tag-click', 'tags-loaded'])

// 响应式数据
const loading = ref(false)
const tags = ref([])

// 计算属性
const tagNames = computed(() => {
  return tags.value.map(tag => tag.tagName)
})

const finalTagIds = computed(() => {
  if (props.tagIdsArray && props.tagIdsArray.length > 0) {
    return props.tagIdsArray
  }
  if (props.tagIds && typeof props.tagIds === 'string') {
    return props.tagIds.split(',').filter(id => id.trim() !== '')
  }
  return []
})

// 监听标签ID变化
watch(() => [props.tagIds, props.tagIdsArray], () => {
  if (props.autoLoad && finalTagIds.value.length > 0) {
    loadTags()
  } else {
    tags.value = []
  }
}, { immediate: true })

// 监听外部标签数据
watch(() => props.externalTags, (newTags) => {
  if (newTags && newTags.length > 0) {
    tags.value = newTags
    loading.value = false
  }
}, { immediate: true })

// 加载标签详情
const loadTags = async () => {
  if (finalTagIds.value.length === 0) {
    tags.value = []
    return
  }

  loading.value = true
  try {
    const response = await tagsManagement.getList({
      current: 1,
      size: -1
    })

    if (response.code === 200) {
      const allTags = response.data?.records || []
      tags.value = allTags.filter(tag =>
        finalTagIds.value.includes(tag.id)
      )
      emit('tags-loaded', tags.value)
    }
  } catch (error) {
    console.error('加载标签失败:', error)
  } finally {
    loading.value = false
  }
}

// 标签点击事件
const handleTagClick = (tag) => {
  if (props.clickable) {
    emit('tag-click', tag)
  }
}

// 判断主题
const judgeTheme = (type) => {
  if (type === '1') {
    return { label: '深色', type: 'dark' }
  } else if (type === '2') {
    return { label: '浅色', type: 'light' }
  } else {
    return { label: '默认', type: 'plain' }
  }
}

// 判断形状
const judgeShape = (type) => {
  if (type === '1') {
    return { label: '圆角', type: false }
  } else if (type === '2') {
    return { label: '椭圆', type: true }
  } else {
    return { label: '圆角', type: false }
  }
}

// 判断颜色
const judgeColor = (type) => {
  if (type === '1') {
    return { label: '蓝色', type: '' }
  } else if (type === '2') {
    return { label: '绿色', type: 'success' }
  } else if (type === '3') {
    return { label: '灰色', type: 'info' }
  } else if (type === '4') {
    return { label: '红色', type: 'danger' }
  } else if (type === '5') {
    return { label: '橙色', type: 'warning' }
  } else {
    return { label: '蓝色', type: '' }
  }
}

// 组件挂载时加载标签
onMounted(() => {
  if (props.autoLoad && finalTagIds.value.length > 0) {
    loadTags()
  }
})
</script>

<style scoped>
.archive-tags {
  width: 100%;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 0;
  transition: all 0.3s ease;
}

.tag-item.clickable {
  cursor: pointer;
}

.tag-item.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.text-container {
  line-height: 1.5;
}

.cards-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 12px;
  background-color: var(--el-fill-color-blank);
  transition: all 0.3s ease;
}

.tag-card.clickable {
  cursor: pointer;
}

.tag-card.clickable:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tag-card-header {
  margin-bottom: 8px;
}

.tag-card-content {
  line-height: 1.4;
}
</style>
