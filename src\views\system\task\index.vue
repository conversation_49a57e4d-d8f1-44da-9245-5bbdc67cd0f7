<!--
 * @Descripttion: 系统计划任务配置
 * @version: 1.2
 * @Author: sakuya
 * @Date: 2021年7月7日09:28:32
 * @LastEditors: sakuya
 * @LastEditTime: 2021年7月10日20:56:47
-->

<template>
	<el-container>
		<el-header>
			<el-form ref="formSearch" v-model="searchData" inline>
				<el-form-item label="任务名称" style="margin-bottom: 0">
					<el-input v-model="searchData.name" clearable placeholder="请输入任务名称" style="width: 22vw"/>
				</el-form-item>
				<el-form-item style="margin-bottom: 0">
					<el-button icon="Search" type="primary" @click="getDataList">查询</el-button>
					<el-button icon="RefreshRight" type="danger" @click="resetQuery">重置</el-button>
				</el-form-item>
			</el-form>
			<el-button icon="Plus" type="primary" @click="add">
				添加计划任务
			</el-button>
		</el-header>
		<el-main v-loading="listLoading" style="padding: 10px;display: flex;flex-direction: row;flex-wrap: wrap;
    		align-content: flex-start;gap: 10px;">
			<el-card v-for="(item, index) in list" :body-style="{ padding: '15px 20px 15px 20px' }" class="task task-item"
					 :key="index" shadow="hover">
				<template #header>
					{{ item.name }}
				</template>
				<ul style="width: 100%;height: 100%;">
					<li>
						<h4>定时规则</h4>
						<p>{{ item.timingRules }}</p>
					</li>
					<li>
						<h4>最后一次执行时间</h4>
						<p>{{ item.lastTime ? item.lastTime : '------' }}</p>
					</li>
				</ul>
				<template #footer>
					<div class="bottom">
						<div class="state">
							<el-tag v-if="item.taskState === '0'" size="small" type="primary">准备就绪</el-tag>
							<el-tag v-if="item.taskState === '1'" size="small" type="success">运行中</el-tag>
							<el-tag v-if="item.taskState === '2'" size="small" type="warning">已暂停</el-tag>
						</div>
						<div class="handler">
							<el-popconfirm v-if="item.isEnable === true" title="确定暂停执行吗？"
										   width="220" @confirm="control(item,0)">
								<template #reference>
									<el-button circle icon="el-icon-video-pause" type="primary"></el-button>
								</template>
							</el-popconfirm>
							<el-popconfirm v-if="item.isEnable === false" title="确定恢复执行吗？"
										   width="220" @confirm="control(item,1)">
								<template #reference>
									<el-button circle icon="el-icon-video-play" type="primary"></el-button>
								</template>
							</el-popconfirm>
							<el-dropdown trigger="click">
								<el-button circle icon="el-icon-more" plain type="primary"></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item @click="edit(item)">编辑</el-dropdown-item>
										<el-dropdown-item @click="logs(item)">日志</el-dropdown-item>
										<el-dropdown-item @click="control(item,2)">立即执行一次</el-dropdown-item>
										<el-dropdown-item divided @click="del(item)">删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
					</div>
				</template>
			</el-card>
		</el-main>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @closed="dialog.save = false" @success="handleSuccess"/>
	<el-drawer v-model="dialog.logsVisible" :size="520" destroy-on-close direction="rtl" title="计划任务日志">
		<logs :taskInfo="taskInfo"/>
	</el-drawer>
</template>

<script>
import saveDialog from "./save";
import logs from "./logs";
import {Plus} from "@element-plus/icons-vue";

export default {
	name: "task",
	components: {
		Plus,
		saveDialog,
		logs,
	},
	provide() {
		return {
			list: this.list,
		};
	},
	data() {
		return {
			dialog: {
				save: false,
				logsVisible: false,
			},
			list: [],
			listLoading: false,
			taskInfo: [],
			searchData: {
				name: ''
			},
			pageParams: {
				current: 1,
				size: 10
			}
		};
	},
	mounted() {
		this.getDataList();
	},
	methods: {
		/*
		 * 获取计划任务列表
		 * @author: 路正宁
		 * @date: 2023-03-29 11:27:54
		 */
		async getDataList() {
			//页面加载
			this.listLoading = true;
			this.list = null;
			let res = await this.$API.sysTaskService.list({
				//当前页码
				current: this.pageParams.current,
				//每页条数
				size: this.pageParams.size,
				//任务名称
				name: this.searchData.name
			});
			if (res.code === 200) {
				//数据列表
				this.list = res.data.records;
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
			this.listLoading = false;
		},
		/*
		 * 重置查询
		 * @author: jmxx
		 * @date: 2023-03-29 11:28:08
		 */
		resetQuery() {
			this.pageParams.current = 1;
			this.searchData.name = "";
			this.getDataList();
		},
		/*
		 * 添加任务计划
		 * @author: 路正宁
		 * @date: 2023-03-29 11:29:56
		 */
		add() {
			this.dialog.save = true;
			this.$nextTick(() => {
				this.$refs.saveDialog.addView();
			});
		},
		/*
		 * 编辑任务计划
		 * @author: 路正宁
		 * @date: 2023-03-29 11:30:44
		 */
		edit(task) {
			this.dialog.save = true;
			this.$nextTick(() => {
				this.$refs.saveDialog.editView(task);
			});
		},
		/*立即执行*/
		async control(row, type) {
			let params = {
				id: row.id,
				cmd: type
			}
			let res = await this.$API.sysTaskService.control(params);
			if (res.code === 200) {
				if (type === 2) {
					this.$message.success('已成功执行计划任务');
				} else if (type === 1) {
					this.$message.success('计划任务恢复成功');
				} else if (type === 0) {
					this.$message.success('计划任务暂停成功');
				}
				await this.getDataList()
			}
		},
		/*
		 * 删除任务计划
		 * @author: 路正宁
		 * @date: 2023-03-29 11:34:04
		 */
		async del(task) {
			//确认删除弹框
			let confirmRes = await this.$confirm(
				`确定删除选中的  ${task.name}  计划任务吗？`,
				"提示",
				{
					type: "warning",
					confirmButtonText: "删除",
					confirmButtonClass: "el-button--danger",
				}
			).catch(() => {
			});
			//确认结果处理
			if (!confirmRes) {
				return false;
			}
			let res = await this.$API.sysTaskService.delete(task.id);
			if (res.code == 200) {
				this.list.splice(
					this.list.findIndex((item) => item.id === task.id),
					1
				);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
		},
		logs(row) {
			this.taskInfo = row;
			this.dialog.logsVisible = true;
		},
		run(task) {
		},
		//本地更新数据
		handleSuccess(data, mode) {
			if (mode == "add") {
				this.list.push(data);
			} else if (mode == "edit") {
				this.list
					.filter((item) => item.id === data.id)
					.forEach((item) => {
						Object.assign(item, data);
					});
			}
		},
	},
};
</script>

<style scoped>
.task {
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	align-items: stretch;
	width: calc(25% - 8px);
}

.task-item li {
	list-style-type: none;
}

.task-item li + li {
	margin-top: 10px;
}

.task-item li h4 {
	font-size: 12px;
	font-weight: normal;
	color: #999;
}

.task-item li p {
	margin-top: 5px;
}

.task-item .bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.task-add {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	cursor: pointer;
	color: #999;
}

.task-add:hover {
	color: #409eff;
}

.task-add i {
	font-size: 30px;
}

.task-add p {
	font-size: 12px;
	margin-top: 20px;
}

:deep(.el-card__footer) {
	padding: 10px 15px 10px 15px;
}
</style>
