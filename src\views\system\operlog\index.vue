<template>
	<el-container>
		<el-header style="height: 30%;padding: 0">
			<scEcharts :option="logsChartOption"></scEcharts>
		</el-header>
		<el-main class="noPadding" style="height: 70%;display: flex;flex-direction: column;flex-wrap: nowrap;
					align-items: flex-end;justify-content: space-between;">
			<el-table :data="dataList" fit highlight-current-row style="height: 100%">
				<el-table-column align="center" label="接口名称" min-width="120" prop="appName"/>
				<el-table-column align="center" label="操作" min-width="172" prop="operate">
					<template #default="scope">
						{{ scope.row.operate ? scope.row.operate : "无数据" }}
					</template>
				</el-table-column>
				<el-table-column align="center" label="操作人" min-width="142" prop="operator"/>
				<el-table-column align="center" label="日志类型" min-width="172" prop="logType"/>
				<el-table-column align="center" label="日志等级" prop="logGrade" width="72">
					<template #default="scope">
						<el-tooltip v-if="scope.row.logGrade === 'info'" content="默认" effect="light"
									placement="top">
							<el-tag effect="dark" type="success">info</el-tag>
						</el-tooltip>
						<el-tooltip v-if="scope.row.logGrade === 'error'" content="错误" effect="light"
									placement="top">
							<el-tag effect="dark" type="danger">error</el-tag>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column align="center" label="请求接口" min-width="172" prop="url"/>
				<el-table-column align="center" label="状态代码" prop="httpCode" width="72">
					<template #default="scope">
						<el-tooltip v-if="scope.row.httpCode === '200'" content="请求成功" effect="light"
									placement="top">
							<el-tag effect="dark" type="success">200</el-tag>
						</el-tooltip>
						<el-tooltip v-if="scope.row.httpCode === '401'" content="未授权" effect="light" placement="top">
							<el-tag effect="dark" type="warning">401</el-tag>
						</el-tooltip>
						<el-tooltip v-if="scope.row.httpCode === '404'" content="无法找到" effect="light"
									placement="top">
							<el-tag style="background-color: black;color: white">404</el-tag>
						</el-tooltip>
						<el-tooltip v-if="scope.row.httpCode === '500'" content="内部服务器错误" effect="light"
									placement="top">
							<el-tag effect="dark" type="danger">500</el-tag>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column align="center" label="客户端IP" prop="clientIp" width="142">
					<template #default="scope">
						{{ scope.row.clientIp ? scope.row.clientIp : "无数据" }}
					</template>
				</el-table-column>
				<el-table-column align="center" label="日志时间" prop="createDate" width="170">
					<template #default="scope">
						{{
							scope.row.createDate
								? moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss')
								: "暂无数据"
						}}
					</template>
				</el-table-column>
				<el-table-column align="center" fixed="right" label="操作" width="220">
					<template #default="scope">
						<el-button :disabled="scope.row.httpCode === '200'" size="small" type="warning"
								   @click="retryLogByMethod(scope.row)">
							重试
						</el-button>
						<el-button size="small" type="danger" @click="deleteRowData(scope.row)">
							删除
						</el-button>
						<el-button size="small" type="primary" @click="infoDrawer = true;logData = scope.row">
							查看详情
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="float: right;">
				<pagination v-model:limit="tablePage.pageSize" v-model:page="tablePage.currentPage"
							:total="tablePage.total" style="padding-top: 10px;padding-bottom: 10px;"
							@pagination="getDataList"/>
			</div>
		</el-main>

		<el-drawer v-model="infoDrawer" :size="720" destroy-on-close title="操作日志详情">
			<info ref="info" :log-data="logData"></info>
		</el-drawer>
	</el-container>
</template>
<script>
import info from "./info";
import scEcharts from "@/components/scEcharts";
import sysOperateLogService from "@/api/model/sys/sysOperateLogService.js";
import moment from "moment";

export default {
	name: "operateLog",
	computed: {
		moment() {
			return moment
		}
	},
	components: {
		info,
		scEcharts,
	},
	data() {
		return {
			infoDrawer: false,
			//柱状图数据
			barGraph: {
				dateList: [],
				valueList: {
					info: [],
					warn: [],
					debug: [],
					error: [],
					fatal: [],
					none: []
				}
			},
			logsChartOption: {},
			date: [],
			//数据列表
			dataList: [],
			//分页参数
			tablePage: {
				//数据总数
				total: 0,
				//当前页码
				currentPage: 1,
				//每页条数
				pageSize: 20,
			},
			//查询表单
			searchForm: {
				name: "",
				url: "",
			},
			//数据列选中行
			selection: [],
			//日志数据
			logData: {},
			//列表加载
			listLoading: false,
		};
	},
	mounted() {
		//刷新数据列表
		this.getDataList();
	},
	methods: {
		/*
		 * 行数据点击事件
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		rowClick(row) {
			this.infoDrawer = true;
			this.$nextTick(() => {
				this.$refs.info.setData(row);
			});
		},
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		getDataList() {
			this.dataList = null;
			//页面加载
			this.listLoading = true;
			sysOperateLogService.list({
				//当前页码
				current: this.tablePage.currentPage,
				//每页条数
				size: this.tablePage.pageSize,
				...this.searchForm,
			}).then(result => {
				if (result.code === 200) {
					//总数据条数
					this.tablePage.total = result.data.total;
					//数据列表
					this.dataList = result.data.records;
					//获取日期柱状图数据
					this.getBarGraphData();
				} else {
					//页面加载
					this.listLoading = false;
					this.$Response.errorNotice(result.msg, "操作日志列表查询失败");
				}
			}).catch(error => {
				//页面加载
				this.listLoading = false;
				this.$Response.errorNotice(null, "操作日志列表查询失败");
			});
		},
		/*
		 * 获取日期柱状图数据
		 */
		getBarGraphData() {
			this.barGraph.dateList = [];
			this.barGraph.valueList.info = [];
			this.barGraph.valueList.error = [];
			this.barGraph.valueList.warn = [];
			this.barGraph.valueList.fatal = [];
			this.barGraph.valueList.debug = [];
			this.barGraph.valueList.none = [];
			sysOperateLogService.selectCountByDate().then(result => {
				if (result.code === 200) {
					let dateSet = new Set();
					let dataList = result.data;
					if (dataList.length > 0) {
						dataList.forEach(item => {
							//添加日期
							dateSet.add(item.logDate);
							//处理日志等级信息
							let logGradeCountList = item.logGradeCounts.split(",");

							logGradeCountList.forEach(logGradeCount => {
								if (logGradeCount.includes("info")) {
									this.barGraph.valueList.info.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("error")) {
									this.barGraph.valueList.error.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("warn")) {
									this.barGraph.valueList.warn.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("fatal")) {
									this.barGraph.valueList.fatal.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("debug")) {
									this.barGraph.valueList.debug.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("none")) {
									this.barGraph.valueList.none.push(logGradeCount.split(":")[1]);
								}
							});

							let noneGrade = ["info", "warn", "error", "fatal", "debug", "none"]
							noneGrade.forEach(grade => {
								if (!item.logGradeCounts.includes(grade)) {
									this.barGraph.valueList[grade].push(0);
								}
							})
						});
						this.barGraph.dateList = Array.from(dateSet);
					}
					//赋值数据图数据
					this.logsChartOption = {
						color: ["#007BFF", "#FFC107", "#6C757D", "#DC3545", "#B00020", "#000000"],
						grid: {
							top: 6,
							left: 53,
							right: 82,
							bottom: 52,
						},
						dataZoom: [
							{
								type: 'inside'
							},
							{
								type: 'slider'
							}
						],
						tooltip: {
							trigger: "axis",
							confine: true
						},
						xAxis: {
							type: "category",
							data: this.barGraph.dateList,
						},
						yAxis: {
							show: false,
							type: "value",
						},
						series: [
							{
								name: "信息",
								data: this.barGraph.valueList.info,
								type: "bar",
								stack: "total",
								barWidth: "22%",
							},
							{
								name: "警告",
								data: this.barGraph.valueList.warn,
								type: "bar",
								stack: "total",
								barWidth: "22%",
							},
							{
								name: "调试",
								data: this.barGraph.valueList.debug,
								type: "bar",
								stack: "total",
								barWidth: "22%",
							},
							{
								name: "错误",
								data: this.barGraph.valueList.error,
								type: "bar",
								stack: "total",
								barWidth: "22%",
							},
							{
								name: "致命",
								data: this.barGraph.valueList.fatal,
								type: "bar",
								stack: "total",
								barWidth: "22%",
							},
							{
								name: "其他",
								data: this.barGraph.valueList.none,
								type: "bar",
								stack: "total",
								barWidth: "22%",
							},
						],
					}
				} else {
					this.$Response.errorNotice(null, result.msg);
				}
			}).catch(error => {
				this.$Response.errorNotice(error, "柱状图查询失败");
			});
			//页面状态恢复
			this.listLoading = false;
		},
		/*
		 * 失败方法重试
		 * @author: 路正宁
		 * @date: 2023-03-24 14:35:00
		 */
		retryLogByMethod(row) {
			this.listLoading = true;
			sysOperateLogService.retryLogByMethod(row.id).then(result => {
				if (result.code === 200) {
					this.getDataList();
					this.$message.success("执行成功");
				} else {
					this.$Response.errorNotice(result.msg, "操作日志列表查询失败");
				}
			}).catch(error => {
				this.$Response.errorNotice(null, "操作日志列表查询失败");
			});
			this.listLoading = false;
		},
		/*
		 * 删除数据，行内删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:35:00
		 */
		deleteRowData(row) {
			this.listLoading = true;
			this.$confirm('是否确认删除此日志信息?', '提示', {
				type: 'warning',
				confirmButtonText: "确定",
				cancelButtonText: "取消",
			}).then(() => {
				sysOperateLogService.delete(row.id).then((result) => {
					if (result.code === 200) {
						this.$message.success("删除成功");
					} else {
						this.$Response.errorNotice(result, "删除失败");
					}
				}).catch(error => {
					this.$Response.errorNotice(null, "删除失败");
				});
				this.getDataList();
			}).catch(() => {
				this.$message.warning("取消删除");
			});
			this.listLoading = false;
		},
	},
};
</script>

<style scoped>

</style>
