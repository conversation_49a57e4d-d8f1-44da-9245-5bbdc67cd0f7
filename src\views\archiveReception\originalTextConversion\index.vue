<template>
	<el-container>
		<el-main ref="main" style="height: 100%;width: 100%;padding: 10px;">
			<el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }" class="box-card"
					 style="height: 100%;width: 100%;">
				<el-container>
					<el-main style="padding: 0">
						<el-table v-loading="electronicLoading" :data="conversionData" border
								  style="height: 100%;width: 100%;">
							<el-table-column label="文件名称" prop="fileName"/>
							<el-table-column align="center" label="源文件格式" prop="oldFileType" width="100"/>
							<el-table-column align="center" label="转换文件格式" prop="newFileType" width="100"/>
							<el-table-column align="center" label="转换标识" prop="changeStatus" width="80">
								<template #default="scope">
									<el-tag v-if="scope.row.changeStatus === '1'">成功</el-tag>
									<el-tag v-else type="danger">失败</el-tag>
								</template>
							</el-table-column>
							<el-table-column align="center" label="转换开始时间" prop="createDate" width="180">
								<template #default="scope">
									{{
										(scope.row.createDate ?
											moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') :
											undefined) || '--'
									}}
								</template>
							</el-table-column>
							<el-table-column align="center" label="转换结束时间" prop="updateDate" width="180">
								<template #default="scope">
									{{
										(scope.row.updateDate ?
											moment(scope.row.updateDate).format('YYYY-MM-DD HH:mm:ss') :
											undefined) || '--'
									}}
								</template>
							</el-table-column>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
											 width="90">
								<template #default="scope">
									<el-button :disabled="scope.row.changeStatus !== '0'" icon="Refresh" link type="warning"
											   @click="retry(scope.row)">重试
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:page-sizes="[20,40,60,80]"
										:total="total" style="padding: 0" @pagination="getList()"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>
	</el-container>
</template>
<script setup>
import electronicFile from '@/api/archive/archiveReception/originalTextConversion'
import moment from 'moment'
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'

const {proxy} = getCurrentInstance();
const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	}
})
const {queryParams} = toRefs(data)
const total = ref(0);
const electronicLoading = ref(false)
// 接收库List
const conversionData = ref([])

onMounted(() => {
	getList();
})

// 查询档案电子原文装换列表数据
function getList() {
	electronicLoading.value = true;
	electronicFile.list(queryParams.value).then(res => {
		if (res.code === 200) {
			conversionData.value = res.data.records;
			total.value = res.data.total;
			electronicLoading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

function retry(data) {
	proxy.$confirm('是否重新开始转换?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		electronicFile.retryFileConvert({
			id: data.id
		}).then(res => {
			if (res.code === 200) {
				getList()
			}else if(res.code === 500){
                proxy.msgError(res.msg);
            }
		}).catch((err) => {
			proxy.msgError(err.msg);
		})
	}).catch(() => {
		proxy.msgError('转换失败');
	})
}
</script>
