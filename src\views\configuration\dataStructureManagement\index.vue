<template>
	<el-container>
		<el-aside v-if="dataViewType === 1" v-loading="menuLoading" width="21%">
			<el-tree ref="typeMenu" :data="typeMenuList" :expand-on-click-node="false" :props="menuProps" check-strictly
					 class="menu" default-expand-all draggable highlight-current node-key="id" show-checkbox
					 @node-click="typeMenuClick">
				<template #default="{ data }">
					<span class="custom-tree-node">
						<span class="label">
							{{ data.name }}
						</span>
					</span>
				</template>
			</el-tree>
		</el-aside>
		<el-main class="noPadding">
			<el-container>
				<el-header>
					<el-form ref="searchFormRef" :inline="true" :model="searchForm" label-width="auto">
						<el-form-item label="数据结构名称" prop="name" style="margin-bottom: 0">
							<el-input v-model="searchForm.name" placeholder="请输入需要查询的数据结构名称"
									  style="width: 12vw;"/>
						</el-form-item>
						<el-form-item style="margin-bottom: 0">
							<el-button icon="Search" type="primary" @click="handleQuery">
								查询
							</el-button>
							<el-button icon="RefreshRight" plain @click="resetQuery">
								重置
							</el-button>
						</el-form-item>
					</el-form>
					<el-button icon="Plus" type="success" @click="openDialog">
						新增
					</el-button>
				</el-header>
				<el-main v-loading="menuLoading">
					<div v-if="dataViewType === 1" style="display: grid;grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
						grid-template-rows: 1fr 1fr 1fr 1fr 1fr;grid-gap: 12px;height: 100%;width: 100%;">
						<el-card v-for="(item, index) in configDataList" :key="index" :body-style="{ width: '100%', height: '100%', padding: '10px 20px 10px 20px'}"
								 style="display: flex;
							flex-direction: column;flex-wrap: nowrap;justify-content: space-evenly;
							align-items: stretch;">
							<template #header>
								<el-text>
									{{ item.name }}
								</el-text>
							</template>
							<div style="display: flex;flex-direction: row;flex-wrap: wrap;align-items: center;row-gap: 5px;
							height: 100%;width: 100%">
								<el-text>
									{{ '当前版本号:' }}
									<el-tag plain>{{ item.versionName }}</el-tag>
								</el-text>
							</div>
							<template #footer>
								<div style="display: flex;justify-content: space-evenly;align-items: center;height: 100%;
								width: 100%;">
									<el-button icon="Search" type="primary" link @click="viewDetails(item)"/>
									<!--									<el-divider direction="vertical" style="height: 100%;margin: 0"/>-->
									<!--									<el-button icon="Delete" type="danger" link @click="deleteData(item.id)"/>-->
								</div>
							</template>
						</el-card>
					</div>
					<div v-else-if="dataViewType === 2" style="height: 100%;width: 100%;">
						<el-container>
							<el-header height="auto" style="padding: 0 0 15px 0">
								<el-button icon="Back" type="primary" @click="this.menuLoading = true;
									this.chooseData = {};this.dataViewType = 1;this.handleQuery();">
									返回
								</el-button>
							</el-header>
							<el-main>
								<el-timeline>
									<el-timeline-item v-for="(item, index) in configDetailsList" :timestamp="moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')" :type="index === 0 ? 'success' : 'info'"
													  center
													  placement="top">
										<el-card style="width: 22vw">
											<el-container>
												<el-main style="padding: 0">
													<h4>{{ item.name }}</h4>
													<p style="margin-top: 5px">版本号:
														<el-tag plain>{{ item.versionName }}</el-tag>
													</p>
												</el-main>
												<el-aside style="border-right: none;display: flex;align-items: center;justify-content: center"
														  width="12%">
													<el-button circle icon="View" plain type="success"
															   @click="this.infoData = item;this.infoOpen = true"/>
												</el-aside>
											</el-container>
										</el-card>
									</el-timeline-item>
								</el-timeline>
							</el-main>
						</el-container>
					</div>
				</el-main>
				<el-footer>
					<div style="display: flex;justify-content: flex-end;">
						<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
									:page-sizes="[25,50,75,100]" style="padding: 0" @pagination="handleQuery"/>
					</div>
				</el-footer>
			</el-container>
		</el-main>

		<!-- 添加或修改数据结构对话框 -->
		<dataConfigInfoDialog :data="infoData" :open="infoOpen" @close="closeInfoDialog"/>
		<!-- 添加或修改数据结构对话框 -->
		<el-dialog :title="configDialogTitle" v-model="configDialogOpen" v-show="configDialogOpen" width="22%"
				   append-to-body align-center>
			<el-form ref="configForm" :model="configData" :rules="configRules" label-width="auto">
				<el-form-item label="结构名称" prop="name">
					<el-input v-model="configData.name" :disabled="dataViewType === 2" placeholder="请输入结构名称"/>
				</el-form-item>
				<el-form-item label="所属分类" prop="platformId">
					<el-tree-select v-model="configData.platformId" :data="platformList"
									:props="{ value: 'id', label: 'name' }"
									check-strictly highlight-current placeholder="请选择所属分类"
									:disabled="dataViewType === 2" style="width: 100%;">
					</el-tree-select>
				</el-form-item>
				<el-form-item label="版本名称" prop="versionName">
					<el-input v-model="configData.versionName" placeholder="请输入版本名称"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button type="success" @click="addData">
					确定
				</el-button>
				<el-button @click="closeDialog">
					取消
				</el-button>
			</template>
		</el-dialog>
	</el-container>
</template>

<script>
import dataStructure from '@/api/archive/dataStructureManagement/dataStructure';
import dataConfigDialog from '@/views/configuration/dataStructureManagement/components/DataConfigDialog.vue'
import dataConfigInfoDialog from '@/views/configuration/dataStructureManagement/components/DataConfigInfoDialog.vue'
import classificationManagement from "@/api/archive/classificationManagement";
import {RefreshRight, Search} from "@element-plus/icons-vue";
import dataRelationalManager from "@/api/archive/dataRelationalManager";
import moment from "moment/moment";

export default {
	name: "dataStructureManagement",
	computed: {
		moment() {
			return moment
		},
		RefreshRight() {
			return RefreshRight
		},
		Search() {
			return Search
		}
	},
	components: {
		dataConfigDialog,
		dataConfigInfoDialog
	},
	data() {
		return {
			//菜单加载中
			menuLoading: false,
			searchForm: {
				name: ''
			},
			typeMenuList: [],
			platformList: [],
			configDataList: [],
			configDetailsList: [],
			menuProps: {
				label: (data) => {
					return data.name;
				},
			},
			queryParams: {
				current: 1,
				size: 25,
			},
			total: 0,
			dataViewType: 1,
			platformId: null,
			chooseData: {},
			infoData: {},
			infoOpen: false,
			configData: {
				id: '',
				name: '',
				platformId: '',
				versionId: '',
				versionName: ''
			},
			configRules: {
				name: [{required: true, message: '请输入结构名称', trigger: 'blur'}],
				platformId: [{required: true, message: '请选择结构分类', trigger: 'Change'}],
				versionName: [{required: true, message: '请输入版本名称', trigger: 'blur'}]
			},
			configDialogTitle: '',
			configDialogOpen: false
		};
	},
	mounted() {
		this.getTypeMenu();
		this.handleQuery();
		this.getPlatformList();
	},
	methods: {
		typeMenuClick(data) {
			this.platformId = data.id;
			this.handleQuery();
		},
		getPlatformList() {
			dataRelationalManager.classifyList().then((res) => {
				this.platformList = res.data;
			});
		},
		getTypeMenu() {
			classificationManagement.treeData().then(res => {
				if (res.code === 200) {
					this.typeMenuList = res.data;
				} else {
					this.$Response.errorNotice(res.msg, "数据查询失败");
				}
			}).catch((error) => {
				this.msgError(error, '查询失败');
			})
		},
		handleQuery() {
			this.menuLoading = true;
			if (this.dataViewType === 1) {
				dataStructure.versionList({
					name: this.searchForm.name,
					platformId: this.platformId,
					versionId: this.chooseData.versionId,
					current: this.queryParams.current,
					size: this.queryParams.size,
				}).then(res => {
					if (res.code === 200) {
						this.configDataList = res.data.records;
						this.total = res.data.total;
					} else {
						this.$Response.errorNotice(res.msg, "查询失败");
					}
					this.menuLoading = false;
				}).catch(error => {
					this.$Response.errorNotice(error, "查询失败");
				});
			} else {
				dataStructure.infoList({
					name: this.searchForm.name,
					platformId: this.platformId,
					versionId: this.chooseData.versionId,
					current: this.queryParams.current,
					size: this.queryParams.size,
				}).then(res => {
					if (res.code === 200) {
						this.configDetailsList = res.data.records;
						this.total = res.data.total;
					} else {
						this.$Response.errorNotice(res.msg, "查询失败");
					}
					this.menuLoading = false;
				}).catch(error => {
					this.$Response.errorNotice(error, "查询失败");
				});
			}
		},
		resetQuery() {
			this.platformId = null;
			this.$refs.searchFormRef.resetFields();
			this.handleQuery();
		},
		viewDetails(data) {
			this.dataViewType = 2;
			this.chooseData = data;
			this.handleQuery();
		},
		addData() {
			this.$refs.configForm.validate((valid) => {
				if (valid) {
					dataStructure.infoSave(this.configData).then((result) => {
						if (result.code === 200) {
							this.closeDialog();
							this.handleQuery();
							this.$Response.successNotice(result.msg, "保存成功");
						} else {
							this.$Response.errorNotice(result.msg, "保存失败");
						}
					}).catch((error) => {
						this.$Response.errorNotice(error, "保存失败");
					});
				}
			});
		},
		editData(data) {
			this.configDialogTitle = '编辑结构数据';
			this.configData = {
				...data
			};
			this.configDialogOpen = true;
		},
		deleteData(dataId) {
			this.$confirm(`确认删除数据吗, 这会删除所有相关的内容？`, '提示', {
				type: 'warning',
				confirmButtonText: '删除',
				confirmButtonClass: 'el-button--danger',
				cancelButtonText: '取消',
				cancelButtonClass: 'el-button--default',
				showCancelButton: true,
			}).then(() => {
				dataStructure.infoDelete({
					ids: dataId
				}).then((result) => {
					if (result.code === 200) {
						this.handleQuery();
						this.$Response.successNotice(result.msg, "删除成功");
					} else {
						this.$Response.errorNotice(result.msg, "删除失败");
					}
				}).catch((error) => {
					this.$Response.errorNotice(error, "删除失败");
				});
			}).catch(() => {
				//取消
			})

		},
		closeDialog() {
			this.configDialogTitle = '';
			this.configDialogOpen = false;
			this.$refs.configForm.resetFields();
		},
		closeInfoDialog() {
			this.infoOpen = false;
		},
		openDialog() {
			this.configData = {
				id: '',
				name: '',
				platformId: '',
				versionId: '',
				versionName: ''
			}
			if (this.dataViewType === 2) {
				this.configData.name = this.chooseData.name;
				this.configData.platformId = this.chooseData.platformId;
				this.configData.versionId = this.chooseData.versionId;
				this.configData.oldStructureId = this.chooseData.structureId;
			}
			this.configDialogTitle = '新增数据结构';
			this.configDialogOpen = true;
		}
	},
};
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
	display: flex;
	flex: 1;
	height: 100%;
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	height: 100%;
	padding-right: 24px;
}

.custom-tree-node .label {
	display: flex;
	align-items: center;
	height: 100%;
}

.custom-tree-node .label .el-tag {
	margin-left: 5px;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .do {
	display: inline-block;
}

:deep(.el-card__footer) {
	padding: 12px 0 12px 0;
}
</style>
