import http from "@/utils/request"
//档案印章管理

export default {
	getList: function (params) {
		return http.get(
			'/archive/sysConfig/signet/list',
			params
		)
	},
	queryById: function (params) {
		return http.get(
			'/archive/sysConfig/signet/queryById',
			params
		)
	},
	save: function (data) {
		return http.post(
			'/archive/sysConfig/signet/save',
			data
		)
	},
	delete: function (params) {
		return http.delete(
			'/archive/sysConfig/signet/delete',
			params
		)
	},
}
