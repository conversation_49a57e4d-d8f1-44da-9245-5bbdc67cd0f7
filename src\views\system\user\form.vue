<template>
	<el-dialog
		:title="titleName"
		v-model="visible"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form
			v-loading="dialogLoading"
			:model="form"
			:rules="[{ required: true, message: '请选择组织机构', trigger: 'blur' }]"
			:disabled="mode == 'view'"
			ref="dialogForm"
			label-width="100px"
		>
			<el-form-item label="组织机构" prop="sysOrg.name">
				<el-input
					v-model="form.sysOrg.name"
					placeholder="请选择组织机构"
					clearable
					disabled
				></el-input>
			</el-form-item>
			<el-form-item label="归属部门" prop="sysOffice.name">
				<el-input v-model="form.sysOffice.name" clearable disabled placeholder="请选择归属部门">
					<template #append>
						<el-button type="primary" @click="openSysOfficeDialog">选择</el-button>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item
				label="姓名"
				prop="name"
				:rules="[{ required: true, message: '姓名不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="form.name" placeholder="请输入姓名" clearable></el-input>
			</el-form-item>
			<el-form-item
				label="登录名"
				prop="loginName"
				:rules="[{ required: true, message: '登录名不能为空', trigger: 'blur' }]"
			>
				<el-input
					v-model="form.loginName"
					placeholder="请输入登录名"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item
				label="密码"
				prop="password"
				:rules="[{ required: true, message: '密码不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="form.password" placeholder="请输入密码" clearable></el-input>
			</el-form-item>
			<el-form-item
				label="邮箱"
				prop="email"
				:rules="[{ required: true, message: '邮箱不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="form.email" placeholder="请输入邮箱" clearable></el-input>
			</el-form-item>
			<el-form-item
				label="电话"
				prop="phone"
				:rules="[{ required: true, message: '电话不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="form.phone" placeholder="请输入电话" clearable></el-input>
			</el-form-item>
			<el-form-item
				label="手机"
				prop="mobile"
				:rules="[{ required: true, message: '手机不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="form.mobile" placeholder="请输入手机" clearable></el-input>
			</el-form-item>
			<el-form-item
				label="用户头像"
				prop="photo"
			>
				<el-upload
					ref="userInfoImg"
					:limit=1
					:headers='headers'
					class="upload-demo"
					:action="uploadUrl"
					:on-success="success"
					list-type="picture-card"
					:file-list="photoList"
					:on-exceed="uploadMsg"
					:data="{ fileType: 'image', fjType: '档案库', zhType: '111' }"
				>
					<slot>
						<el-icon>
							<el-icon-plus/>
						</el-icon>
					</slot>
					<template #file="{ file }">
						<div>
							<img class="el-upload-list__item-thumbnail" :src="file.url" alt=""/>
							<span class="el-upload-list__item-actions">
								<span class="el-upload-list__item-delete"
									  @click="handleRemove(file)">
									<el-icon><el-icon-delete/></el-icon>
								</span>
							</span>
						</div>
					</template>
				</el-upload>
			</el-form-item>
			<el-form-item label="是否可登录" prop="loginFlag">
				<el-switch v-model="form.loginFlag" active-value="true" inactive-value="false"/>
			</el-form-item>
			<el-form-item label="是否是管理员" prop="admin">
				<el-switch v-model="form.admin" active-value="true" inactive-value="false"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">
				取 消
			</el-button>
			<el-button v-if="mode != 'view'" :loading="isSubmiting" type="primary" @click="submit()">
				保 存
			</el-button>
		</template>
		<sysOfficeSelect
			ref="sysOfficeSelected"
			v-if="sysOfficeSelectDialog"
			:isMultiple="false"
			draggable
			@closed="sysOfficeSelectDialog = false"
			:selectChange="officeSelectChange"
		></sysOfficeSelect>
	</el-dialog>
</template>

<script>
import tool from "@/utils/tool";
import {getCurrentInstance} from "vue";
import {ElMessage} from "element-plus";
import sysUserService from "@/api/model/sys/sysUserService";

export default {
	props: {
		//回调函数
		callback: {type: Function},
	},
	data() {
		return {
			//表单类型
			mode: "add",
			//表单标题
			titleName: "",
			//是否显示或隐藏表单弹框
			visible: false,
			//提交中
			isSubmiting: false,
			//弹框加载中
			dialogLoading: false,
			//表单数据
			form: {
                sysOrg:{
                    id: undefined,
                    name: undefined,
                },
                sysOffice : {
                    id: undefined,
                    name: undefined,
                }
            },
			sysOfficeSelectDialog: false,
			headers: {
				Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
				ContentType: 'multipart/form-data',
				clientType: "PC"
			},
			photoList: [],
			proxy: getCurrentInstance(),
			uploadUrl: process.env.VUE_APP_API_UPLOAD
		};
	},
	mounted() {
	},
	methods: {
		/*
		 * 删除提示
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:15
		 */
		uploadMsg() {
			ElMessage({
				message: '最多只能上传一张.',
				type: 'warning',
			})
		},
		/*
		 * 添加视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:15
		 */
		addView(sysOrg) {
			//设置标题
			this.titleName = "添加";
			this.mode = "add";
			//显示表单
			this.visible = true;
			//释放提交按钮状态
			this.isSubmiting = false;
			//释放弹框加载
			this.dialogLoading = false;
			//清空头像列表
			this.photoList = [];
			//初始化表单
			this.setForm({sysOrg: sysOrg});
		},
		/*
		 * 编辑视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:35
		 */
		async editView(formData) {
			//设置标题
			this.titleName = "编辑";
			this.mode = "edit";
			//显示表单
			this.visible = true;
			//释放提交按钮
			this.isSubmiting = false;
			//清空头像列表
			this.photoList = [];
			//页面加载中
			this.dialogLoading = true;
			//重新查询
			sysUserService.queryById(formData.id).then((res) => {
				if (res.code === 200) {
					//设置表单数据
					this.setForm(res.data);
					// 只有当用户头像存在时才添加到photoList
					if (res.data.photo && res.data.photo.trim() !== '') {
						this.photoList.push({url: res.data.photo});
					}
				} else {
					this.$Response.errorNotice(res, "查询失败");
					//锁定提交按钮
					this.isSubmiting = true;
				}
			});
			//释放页面加载中
			this.dialogLoading = false;
		},
		/*
		 * 查看视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:21:14
		 */
		view(formData) {
			//设置标题
			this.titleName = "查看";
			this.mode = "view";
			//显示表单
			this.visible = true;
			//释放提交按钮状态
			this.isSubmiting = false;
			//释放弹框加载
			this.dialogLoading = false;
			//清空头像列表
			this.photoList = [];
			//设置表单数据
			this.setForm(formData);
			// 只有当用户头像存在时才添加到photoList
			if (formData.photo && formData.photo.trim() !== '') {
				this.photoList.push({url: formData.photo});
			}
		},
		/*
		 * 设置表单
		 * @author: 路正宁
		 * @date: 2023-04-04 16:19:17
		 */
		setForm(data) {
			// 创建数据的深拷贝，避免修改原始列表数据
			this.form = JSON.parse(JSON.stringify(data));
			if (this.$ObjectUtils.isEmpty(this.form.sysOrg)) {
				this.form.sysOrg = {
					id: "",
					name: "",
				};
			}
            if(!this.form.sysOrg?.name )this.form.sysOrg.name = ''
			if (this.$ObjectUtils.isEmpty(this.form.sysOffice)) {
				this.form.sysOffice = {
					id: "",
					name: "",
				};
			}
            if(!this.form.sysOffice?.name )this.form.sysOffice.name = ''

			// 处理loginFlag字段 - 确保正确的布尔值转换
			if (this.form.loginFlag === undefined || this.form.loginFlag === null) {
				this.form.loginFlag = "true"; // 默认值
			} else {
				// 将布尔值转换为字符串，以匹配el-switch的active-value和inactive-value
				this.form.loginFlag = this.form.loginFlag === true || this.form.loginFlag === "true" ? "true" : "false";
			}

			// 处理admin字段 - 确保正确的布尔值转换
			if (this.form.admin === undefined || this.form.admin === null) {
				this.form.admin = "false"; // 默认值
			} else {
				// 将布尔值转换为字符串，以匹配el-switch的active-value和inactive-value
				this.form.admin = this.form.admin === true || this.form.admin === "true" ? "true" : "false";
			}

			this.form.password = "";
		},
		/*
		 * 文件删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:11:20
		 */
		handleRemove(file) {
			this.$refs.userInfoImg.handleRemove(file);
			// 从photoList中移除对应的文件
			this.photoList = this.photoList.filter(item => item.url !== file.url);
		},
		/*
		 * 表单提交
		 * @author: 路正宁
		 * @date: 2023-03-24 14:11:20
		 */
		submit() {
			this.$refs.dialogForm.validate(valid => {
				if (valid) {
					this.isSubmiting = true;
					if (this.$ObjectUtils.isNotEmpty(this.form.password)) {
						this.form.password = this.$TOOL.crypto.MD5(this.form.password);
					}
					//锁定提交按钮
					this.form.photo = this.photoList[0] ? this.photoList[0].url : "";

					// 创建提交数据的副本，将字符串值转换为布尔值
					const submitData = { ...this.form };
					submitData.loginFlag = submitData.loginFlag === "true";
					submitData.admin = submitData.admin === "true";

					sysUserService.save(submitData).then(result => {
						if (result.code === 200) {
							//关闭页面
							this.visible = false;
							this.$message.success("操作成功");
							//回调函数
							this.callback(result.data, this.mode);
						} else {
							console.log(result);
							this.$Response.errorNotice(null, result.msg);
						}
					}).catch(() => {
						this.$message.error("操作失败");
					});
					//释放提交按钮
					this.isSubmiting = false;
				}
			});
		},
		/*
		 * 用户头像上传成功事件
		 * @author: 路正宁
		 * @date: 2023-04-03 10:27:55
		 */
		success(res, file, fileList) {
			if (res.code === 200) {
				fileList.forEach(file => {
					file.name = res?.data?.name;
					file.url = res?.data?.url;
				});
				this.photoList = fileList;
			}
		},
		/*
		 * 打开部门选择框
		 * @author: 路正宁
		 * @date: 2023-04-03 10:27:55
		 */
		openSysOfficeDialog() {
			let dataList = [];
			if (this.form.sysOffice.id !== '') {
				dataList.push(this.form.sysOffice);
			}
			this.sysOfficeSelectDialog = true;
			this.$nextTick(() => {
				this.$refs.sysOfficeSelected.init(this.form.sysOrg, dataList);
			});
		},

		/*
		 * 部门弹框回调选择事件
		 * @author: 路正宁
		 * @date: 2023-04-03 10:02:56
		 */
		officeSelectChange(officeList) {
			if (officeList.length > 0) {
				this.form.sysOffice = officeList[0];
			} else {
				this.form.sysOffice = {
					id: "",
					name: "",
				};
			}
		},
	},
};
</script>

<style></style>
