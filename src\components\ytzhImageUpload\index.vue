<template>
  <el-upload
	  ref="upload"
	  :action="process.env.VUE_APP_API_UPLOAD"
	  :auto-upload="false"
	  :file-list="fileList"
	  :on-change="handleChange"
	  :on-remove="handleRemove"
	  list-type="picture-card"
  >
    <el-icon><Plus /></el-icon>
  </el-upload>
</template>

<script>
export default {
  props: {
    fileLists: {
      type: Object,
      default: () => {
        [];
      },
    },
  },
  computed: {},
  data() {
    return {
      fileList: [],
    };
  },
  watch: {
    fileLists() {
      this.fileList = this.fileLists;
    },
  },
  mounted() {},

  methods: {
    /*
     * 上传文件并返回远程路径
     * @author: 路正宁
     * @date: 2023-04-18 14:33:04
    */
    async uploadImage() {
      //待上传文件
      var files = [];
      //远程文件路径
      var urls = [];
      //上传文件和远程路径处理
      await this.fileList.forEach((item) => {
        if (item.status == "ready") {
          files.push(item.raw);
        }
        if (item.status == "success") {
          urls.push(item.url);
        }
      });
      //上传文件
      for (var i = 0; i < files.length; i++) {
        var res = await this.$API.common.upload.post({ file: files[i] });
        if (res.code == 200) {
          urls.push(res.data.url);
        } else {
          this.$Response.errorNotice(res, "["+files[i].name+"]上传失败");
        }
      }
      return urls.join(",");
    },
    /*
     * 同步文件上传列表
     * @author: 路正宁
     * @date: 2023-04-18 14:22:50
    */
    handleChange(file, fileLists) {
      this.fileList = fileLists;
    },
    /*
     * 文件移除
     * @author: 路正宁
     * @date: 2023-04-18 09:30:20
     */
    async handleRemove(uploadFile, uploadFiles) {
      if (uploadFile.status != "success") {
        return;
      }
      var url = "";
      if (
        this.$ObjectUtils.isNotEmpty(uploadFile.response) &&
        this.$ObjectUtils.isNotEmpty(uploadFile.response.data.url)
      ) {
        url = uploadFile.response.data.url;
      } else {
        url = uploadFile.url;
      }
      var res = await this.$API.common.deleteFile.get(url);
      if (res.code == 200) {
        this.$message.success("操作成功");
      } else {
        this.$Response.errorNotice(res, "上传失败");
      }
    },
  },
};
</script>

<style scoped>
.ytzhTable {
}
.ytzhTable-table {
  height: calc(100% - 50px);
}
.ytzhTable-page {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.ytzhTable-do {
  white-space: nowrap;
}
.ytzhTable:deep(.el-table__footer) .cell {
  font-weight: bold;
}
.ytzhTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
  height: 12px;
  border-radius: 12px;
}
.ytzhTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
  width: 12px;
  border-radius: 12px;
}
</style>
