import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/procure/erpPurchaseOrderForm/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/procure/erpPurchaseOrderForm/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/procure/erpPurchaseOrderForm/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseOrderForm/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/procure/erpPurchaseOrderForm/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseOrderForm/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/procure/erpPurchaseOrderForm/import',
      data
    )
  }
}
