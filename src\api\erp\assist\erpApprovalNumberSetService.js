import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/assist/erpApprovalNumberSet/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/assist/erpApprovalNumberSet/delete',
      ids
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/assist/erpApprovalNumberSet/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/assist/erpApprovalNumberSet/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/assist/erpApprovalNumberSet/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/assist/erpApprovalNumberSet/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/assist/erpApprovalNumberSet/import',
      data
    )
  }
}
