<template>
    <el-descriptions :column="3" border style="margin-bottom: 20px">
        <el-descriptions-item label="借阅人" width="80">
            {{ descriptionsLabel.borrowApply.name }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅部门" width="80">
            {{ descriptionsLabel.borrowApply.sysOffice.name }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅起始时间" width="80">
            {{ moment(descriptionsLabel.borrowStartTime).format('YYYY-MM-DD HH:mm:ss') }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅结束时间" width="80">
            {{ moment(descriptionsLabel.borrowEndTime).format('YYYY-MM-DD HH:mm:ss') }}
        </el-descriptions-item>
        <el-descriptions-item label="是否包含水印" width="80">
            {{ descriptionsLabel.borrowIsWatermark == '1' ? '是' : '否' }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅理由" width="80">
            {{ descriptionsLabel.borrowRemark }}
        </el-descriptions-item>
    </el-descriptions>
    <el-table :data="controlList" border @selection-change="handleSelectionChange">
        <el-table-column align="center" label="序号" prop="sort" width="80">
            <template #default="scope">
                {{ scope.$index + 1 }}
            </template>
        </el-table-column>
        <el-table-column align="left" label="档案名称" prop="detailsInfo.name" />
        <el-table-column align="left" label="档案号" prop="detailsInfo.num" />
        <el-table-column align="left" label="档案门类" prop="detailsInfo.controlCategory.name" width="120" />
        <el-table-column align="left" label="借阅人" prop="detailsBorrow.borrowApply.name" width="120" />
        <el-table-column align="left" label="借阅方式" prop="detailsBorrowType" width="150">
            <template #default="scope">
                <span>{{ borrowingMethod(scope.row.detailsBorrowType) }}</span>
            </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="80px">
            <template #default="scope">
                <el-button link type="primary" @click="collectFile(scope.row)">查看
                </el-button>
            </template>
        </el-table-column>
    </el-table>
    <div v-if="!props.receiveType">
        <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">审批意见</h4>
        <auditForms ref="auditRef3" @refresh="refresh3" />
    </div>
    <div v-if="props.receiveType === '1'">
        <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">操作记录</h4>
        <LogQuery ref="logRef3" />
    </div>
    <el-main>
        <el-container>
            <el-main>
                <div style="float: right;">
                    <el-button plain @click="() => cancellation()">取消</el-button>
                    <el-button v-if="!props.receiveType" type="primary" @click="rightOrder()">确认</el-button>
                </div>
            </el-main>
        </el-container>
    </el-main>
	<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
		<viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
	</el-dialog>
</template>
<script setup>
import vitalizationArchiveList from '@/api/archive/vitalizationArchive'
import {defineProps, getCurrentInstance, onMounted, ref} from 'vue'
// 查看弹窗
import viewFiles from '@/views/archiveReception/view.vue';
// 操作流程
import LogQuery from "@/components/detailsForm/logQuery.vue";
import moment from 'moment'

const { proxy } = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const props = defineProps({
    receiveId: {
        type: String
    },
    receiveType: {
        type: String
    },
})

// 审批
const auditRef3 = ref(null)
const refresh3 = () => {
    emit("childMove");
}
const rightOrder = () => {
    auditRef3.value.formSub(descriptionsLabel.value.id);
}
// 操作流程
const logRef3 = ref(null)
async function auditList() {
    const auditList = await vitalizationArchiveList.auditList({ 'borrow.id': props.receiveId.id })
    if (auditList.code === 200) {
        console.log(auditList.data.records);
		let records = [];
		auditList.data.records.forEach(record => {
			records.push({
				name: record.borrowAuditPerson.name,
				updateDate: record.updateDate,
				remark: record.remark
			});
		});
        logRef3.value.timeFns(records);
    } else {
        proxy.msgError('加载失败');
    }
}

// 接收库List
const descriptionsLabel = ref([]);


// 回显鉴定类型
function borrowingMethod(val) {
    let arr = val.split(',');
    let method = arr.map((v) => {
        if (v == '1') {
            return '电子';
        } else if (v == '2') {
            return '纸质';
        } else if (v == '3') {
            return '下载';
        } else if (v == '4') {
            return '打印';
        }
    })
    return method.join(',');
}

// 取消
function cancellation() {
    emit("childMove");
}

const title = ref('')
// 查看receiveId
const receiveId = ref('')
const openView = ref(false)
function collectFile(val) {
    title.value = '查看';
    receiveId.value = val.detailsInfo;
    openView.value = true;
}
// 关闭查看
function parentView() {
    openView.value = false;
}

// 查询原数据
function getList() {
    descriptionsLabel.value = props.receiveId;
    if (props.receiveType == '1') {
        auditList();
    }
}
// 查询审批档案
const controlList = ref([]);
function getControl() {
    vitalizationArchiveList.list({
        'detailsBorrow.id': props.receiveId.id,
        size:'-1'
    }).then(res => {
        if (res.code === 200) {
            controlList.value = res.data.records
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

onMounted(() => {
    getControl();
    getList();
});
</script>
<style scoped>
.fileUrl {
    margin-bottom: 10px;
    cursor: pointer;
}

.fileUrl:hover {
    color: #2a76f8;
}

p {
    /* width: 250px; */
    /* white-space: nowrap; */
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
