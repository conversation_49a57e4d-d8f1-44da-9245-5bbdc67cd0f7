<template>
	<el-form ref="loginForm"
			 :model="form"
			 :rules="rules"
			 hide-required-asterisk
			 label-position="top"
			 label-width="0"
			 size="large"
			 @keyup.enter="login">
		<el-form-item label="账号" prop="user">
			<el-input v-model="form.user" clearable placeholder="请输入账号"/>
		</el-form-item>
		<el-form-item label="密码" prop="password">
			<el-input v-model="form.password" clearable placeholder="请输入密码" show-password/>
			<div class="form-actions">
				<a class="forget-link" @click="handlerChange">忘记密码？</a>
			</div>
		</el-form-item>
		<el-form-item>
			<el-button :loading="islogin" :disabled="!form.user || !form.password" class="submit-button" type="primary" @click="submitLogin">
				{{ $t("login.signIn") }}
			</el-button>
		</el-form-item>
		<div class="register-prompt">
			新用户?
			<router-link class="register-link" to="/user_register">马上注册</router-link>
		</div>
	</el-form>
</template>
<script>
import tool from '@/utils/tool';

export default {
	props: {
		handlerReset: Function,
	},
	data() {
		return {
			form: {
				user: "",
				password: "",
				autologin: false,
			},
			rules: {
				user: [
					{
						required: true,
						message: '请输入账号',
						trigger: "blur",
					},
					{
						min: 3,
						max: 30,
						message: '账号长度应在3到30个字符之间',
						trigger: "blur"
					}
				],
				password: [
					{
						required: true,
						message: this.$t("login.PWError") || '请输入密码',
						trigger: "blur",
					},
				],
			},
			//登录按钮锁
			islogin: false,
			orgList: [],
		};
	},
	watch: {},
	mounted() {
	},
	methods: {
		/*
		 * 提交登录事件
		 * @author: 路正宁
		 * @date: 2023-03-21 11:17:03
		 */
		async submitLogin() {
			//表单校验
			try {
				await this.$refs.loginForm.validate();
				
				//登录请求体
				let loginBody = {
					username: this.form.user,
					password: this.$TOOL.crypto.MD5(this.form.password)
					//password: this.form.password,
				};
				//锁定登录按钮
				this.islogin = true;
				
				try {
					//执行登录
					let loginUser = await this.login(loginBody);
					if (this.$ObjectUtils.isEmpty(loginUser)) {
						//释放登录按钮
						this.islogin = false;
						return false;
					}

					//获取系统菜单
					await this.getMenuTree();
					this.$message.success("登录成功");
					//释放登录按钮
					this.islogin = false;
					//获取菜单
					const paths = tool.data.get("MENU").map(item => item.path);
					this.$router.replace({
						path: paths[0] || "/home",
					});
				} catch (error) {
					console.error('登录过程发生错误:', error);
					this.$message.error("登录失败，请重试");
					this.islogin = false;
				}
			} catch (error) {
				console.error('表单验证失败:', error);
				return false;
			}
		},
		/*
		 *@functionName: 登录接口
		 *@params1: loginBody:登录参数
		 *@author: 路正宁
		 *@date: 2023-03-21 15:14:51
		 */
		async login(loginBody) {
			//执行登录
			let loginUser = await this.$API.auth.login(loginBody);
			//校验对象是否为空
			if (this.$ObjectUtils.isEmpty(loginUser)) {
				this.$message.error("登录失败，请重试");
				return null;
			}
			//校验状态码是否成功
			if (loginUser.code == 200) {
				//登录成功处理
				//保存token到cookie中
				this.$TOOL.cookie.set("TOKEN", loginUser.data.token, {
					expires: this.form.autologin ? 24 * 60 * 60 : 0,
				});
				//保存用户信息到cookie中
				this.$TOOL.data.set("USER_INFO", loginUser.data.userInfo);
				this.$TOOL.data.set("ROLE_LIST", loginUser.data.roleList);
				this.$TOOL.data.set("Organization", loginUser.data.orgList)
				// if (loginUser?.data?.orgList.length) {
				//   let res = await this.$API.system.menuTreeByOrg({ orgId: loginUser?.data?.orgList[0]?.id })
				//   this.$TOOL.data.set("USER_INFO", res);
				//   this.$TOOL.data.set("Organization", loginUser.data.orgList)
				// }

				return loginUser;
			} else {
				//登录失败处理
				this.$Response.errorNotice(loginUser, "登录失败，请重试");
				return null;
			}
		},
		/*
		 * 获取系统菜单
		 * @author: 路正宁
		 * @date: 2023-04-07 10:51:24
		 */
		async getMenuTree() {
			const orgList = this.$TOOL.data.get("Organization")
			if (orgList?.length) {
				let response = await this.$API.system.menuTreeByOrg({orgId: orgList[0]?.id})
				let menuTree = this.sysMenuToUiTree(response.data);
				let permissions = this.getPermissions(response.data);
				//菜单数据写入本地
				this.$TOOL.data.set("MENU", menuTree);
				//权限标识写入本地
				this.$TOOL.data.set("PERMISSIONS", permissions);
				this.$TOOL.data.set("orgKey", 0);
				return;
			}
			let res = await this.$API.system.menuTree();
			if (res.code != 200) {
				this.$Response.errorNotice(res, "当前用户无任何菜单权限，请联系系统管理员");
				return false;
			}
			let menuTrees = this.sysMenuToUiTree(res.data);
			let permissionss = this.getPermissions(res.data);
			//菜单数据写入本地
			this.$TOOL.data.set("MENU", menuTrees);
			//权限标识写入本地
			this.$TOOL.data.set("PERMISSIONS", permissionss);
			return true;
		},

		/*
		 * 菜单数据转树UI数据结构
		 * @author: 路正宁
		 * @date: 2023-04-07 11:20:31
		 */
		sysMenuToUiTree(sysMenu) {
			let menuTree = [];
			for (let i = 0; i < sysMenu.length; i++) {
				menuTree[i] = {
					name: sysMenu[i].alias,
					path: sysMenu[i].path,
					meta: {
						title: sysMenu[i].name,
						icon: sysMenu[i].logo,
						type: sysMenu[i].type,
						hidden: sysMenu[i].hide,
						color: sysMenu[i].affix,
						fullpage: sysMenu[i].wholePageRoute,
					},
					component: sysMenu[i].view,
				};
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					menuTree[i].children = this.sysMenuToUiTree(sysMenu[i].children);
				}
			}
			return menuTree;
		},
		/*
		 *提取权限标识
		 * @author: 路正宁
		 * @date: 2023-04-07 15:21:52
		 */
		getPermissions(sysMenu) {
			let permissions = [];
			for (let i = 0; i < sysMenu.length; i++) {
				if (this.$ObjectUtils.isNotEmpty(sysMenu[i].permission)) {
					permissions.push(sysMenu[i].permission);
				}
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					let pe = this.getPermissions(sysMenu[i].children);
					for (let j = 0; j < pe.length; j++) {
						if (this.$ObjectUtils.isNotEmpty(pe[j])) {
							permissions.push(pe[j]);
						}
					}
				}
			}
			return permissions;
		},
		handlerChange() {
			this.handlerReset()
		}
	},
};
</script>

<style scoped>
.el-form {
	margin-top: 8px;
}

.el-form-item {
	margin-bottom: 24px;
}

:deep(.el-form-item__label) {
	padding-bottom: 8px;
	font-size: 14px;
	color: #606A87;
	line-height: 1;
}

:deep(.el-input__wrapper) {
	padding: 0 12px;
	box-shadow: 0 0 0 1px #DCDFE6 inset;
	border-radius: 4px;
	transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
	box-shadow: 0 0 0 1px #C0C4CC inset;
}

:deep(.el-input__wrapper.is-focus) {
	box-shadow: 0 0 0 1px #1664FF inset;
}

.form-actions {
	display: flex;
	justify-content: flex-end;
	margin-top: 8px;
}

.forget-link {
	font-size: 14px;
	color: #909399;
	cursor: pointer;
	transition: color 0.2s;
}

.forget-link:hover {
	color: #1664FF;
}

.submit-button {
	width: 100%;
	height: 44px;
	font-size: 16px;
	border-radius: 4px;
	margin-top: 8px;
	background-color: #1664FF;
	border-color: #1664FF;
	transition: all 0.3s;
}

.submit-button:hover {
	background-color: #4285F4;
	border-color: #4285F4;
}

.register-prompt {
	text-align: center;
	margin-top: 16px;
	font-size: 14px;
	color: #606A87;
}

.register-link {
	color: #1664FF;
	text-decoration: none;
	margin-left: 4px;
	transition: color 0.2s;
}

.register-link:hover {
	color: #4285F4;
	text-decoration: underline;
}
</style>
