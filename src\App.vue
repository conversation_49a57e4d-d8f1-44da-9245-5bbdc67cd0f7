<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:11
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-10-26 09:55:25
 * @FilePath: \archive-manage-front\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<el-config-provider :button="config.button" :locale="locale" :size="config.size" :zIndex="config.zIndex">
		<router-view></router-view>
	</el-config-provider>
</template>

<script>
import colorTool from '@/utils/color'
// import DevicePixelRatio from './rem2'
export default {
	name: 'App',
	data() {
		return {
			config: {
				size: "default",
				zIndex: 2000,
				button: {
					autoInsertSpace: false
				}
			},
			s_length: 0,
			b_length: 0,
			timeoutId: null
		}
	},
	computed: {
		locale() {
			return this.$i18n.messages[this.$i18n.locale].el
		},
	},
	created() {
		// new DevicePixelRatio().init()
		//设置主题颜色
		const observer = new MutationObserver(this.click_d);

		// 配置观察器选项
		const config = {childList: true, subtree: true};

		// 开始观察 body 元素
		observer.observe(document.body, config);

		const app_color = this.$CONFIG.COLOR || this.$TOOL.data.get('APP_COLOR')
		if (app_color) {
			document.documentElement.style.setProperty('--el-color-primary', app_color);
			for (let i = 1; i <= 9; i++) {
				document.documentElement.style.setProperty(`--el-color-primary-light-${i}`, colorTool.lighten(app_color, i / 10));
			}
			for (let i = 1; i <= 9; i++) {
				document.documentElement.style.setProperty(`--el-color-primary-dark-${i}`, colorTool.darken(app_color, i / 10));
			}
		}
	},
	methods: {
		onStorageChange(event) {
			if (event.key === 'USER_INFO') {
				// 如果用户信息变动，刷新页面
				let fn = () => {
					setTimeout(() => {
						if (localStorage.getItem('MENU')) {
							location.reload();
						} else {
							fn()
						}
					}, 1000)
				}
				fn()
			}
		},
		click_d() {
			const el_button = document.getElementsByClassName('el-button');
			if (this.b_length !== el_button.length) {
				this.b_length = el_button.length
				for (let i = 0; i < el_button.length; i++) {
					this.$.directives.debounce.inserted(el_button[i]);
				}
			}
			const elInputs = document.getElementsByClassName('el-input');
			if (this.s_length !== elInputs.length) {
				this.s_length = elInputs.length
				for (let i = 0; i < elInputs.length; i++) {
					this.$.directives.bubble.mounted(elInputs[i]);
				}
			}
		},
		handleResize() {
			clearTimeout(this.timeoutId);
			this.timeoutId = setTimeout(() => {
				this.Event();
			}, 100);
		},
		Event() {
			// 表格自适应的事件处理逻辑
			let table = document.getElementsByClassName('app-container_flex')?.[0]?.getElementsByClassName('table_card')?.[0]?.getElementsByClassName('el-card__body')?.[0]?.getElementsByClassName('el-table')
			if (table?.length) {
				let num = document.getElementsByClassName('app-container_flex')?.[0].getElementsByClassName('table_card')?.[0].clientHeight - 44 - 44
				table[0].style.maxHeight = num + 'px'
				table[0].getElementsByClassName('el-table__inner-wrapper')[0].style.maxHeight = num + 'px'
				table[0].getElementsByClassName('el-scrollbar__wrap')[0].style.maxHeight = (num - table[0].getElementsByClassName('el-table__header-wrapper')[0].clientHeight) + 'px'
				table[0].getElementsByClassName('el-scrollbar__wrap')[0].style.overflow = 'auto'
			}
		},
		flag_table() {
			return !!document.getElementsByClassName('app-container_flex')?.[0]?.getElementsByClassName('table_card')?.[0]?.getElementsByClassName('el-card__body')?.[0]?.getElementsByClassName('el-table')?.length
		}
	},
	mounted() {
		window.addEventListener('storage', this.onStorageChange);
		// let update_local = '20241008' //TODO:如需要用户清理本地存储（localStorage）里的数据把数据改成对应修改日期即可
		// if (localStorage.getItem('update_local') !== update_local) {  //如用户本地存储的更新日期和最新修改日期不一致则清理本地存储，同时判断保留用户信息
		// 	for (let localStorageKey in localStorage) {
		// 		let no_remove = ['USER_INFO', 'ROLE_LIST', 'Organization', 'MENU', 'noticeOpen', 'PERMISSIONS', 'orgKey', 'orgValue', 'AUTO_EXIT']
		// 		if (!no_remove?.find(item => item === localStorageKey)) {
		// 			localStorage.removeItem(localStorageKey)
		// 		}
		// 	}
		// 	localStorage.setItem('update_local', update_local)
		// }
		window.addEventListener('resize', this.handleResize);
		this.$router.afterEach((to, from) => {
			// 执行你的操作，例如记录页面浏览、执行特定的动画等
			let num = 0
			let timer = setInterval(() => {
				num++
				if (num === 50) {
					clearInterval(timer)
				}
				if (this.flag_table()) {
					clearInterval(timer)
					this.Event()
				}
			}, 100)
		});
	},
	beforeUnmount() {
		window.removeEventListener('resize', this.handleResize);
	},
	directives: {
		'bubble': {
			mounted(el) {
				// 判断是否为日期选择器 处理输入时自动加入‘-’
				if (new RegExp('el-date-editor--date').test(el.getAttribute('class'))) {
					el.addEventListener('input', () => {
						if (el.getElementsByClassName('el-input__inner')[0].value.length === 4) {
							el.getElementsByClassName('el-input__inner')[0].value += '-'
						} else if (el.getElementsByClassName('el-input__inner')[0].value.length === 7) {
							el.getElementsByClassName('el-input__inner')[0].value += '-'
						}
					})
				}
				let bubble = null
				// 创建气泡
				if (document.getElementsByClassName('bubble').length) {
					bubble = document.getElementsByClassName('bubble')[0]
				} else {
					bubble = document.createElement('div')
					bubble.className = 'bubble'
					document.body.appendChild(bubble)
				}
				bubble.style.paddingTop = '0'
				bubble.style.paddingBottom = '0'
				bubble.style.height = '0'
				bubble.style.opacity = '0'
				let blo = () => {
					const rect = el.getBoundingClientRect()
					bubble.innerText = el.getElementsByClassName('el-input__inner')[0].value
					bubble.style.width = rect.width + 'px'
					bubble.style.top = rect.bottom + 'px'
					bubble.style.maxHeight = (window.innerHeight - rect.bottom) + 'px'
					bubble.style.overflow = 'auto'
					bubble.style.left = rect.left + 'px'
					let old_dom = document.getElementsByClassName('bubble')
					for (let i = 0; i < old_dom.length; i++) {
						if (old_dom[i] !== bubble) {
							old_dom[i].style.paddingTop = '0'
							old_dom[i].style.paddingBottom = '0'
							old_dom[i].style.opacity = '0'
							old_dom[i].style.height = '0'
						}
					}
					if (el.getElementsByClassName('el-input__inner')[0].scrollWidth > el.getElementsByClassName('el-input__inner')[0].offsetWidth) {
						bubble.style.paddingTop = '5px'
						bubble.style.paddingBottom = '5px'
						bubble.style.height = 'auto'
						bubble.style.opacity = '1'
					} else {
						bubble.style.paddingTop = '0'
						bubble.style.paddingBottom = '0'
						bubble.style.height = '0'
						bubble.style.opacity = '0'
					}
				}
				// 监听输入框的输入事件
				el.addEventListener('input', () => {
					blo()
				})
				el.addEventListener('mouseenter', () => {
					blo()
				})
				// 监听鼠标移出输入框事件
				el.addEventListener('mouseleave', async () => {
					bubble.style.paddingTop = '0'
					bubble.style.paddingBottom = '0'
					bubble.style.height = '0'
					bubble.style.opacity = '0'
					await bubble.addEventListener("mouseover", function () {
						blo()
					});
					bubble.addEventListener("mouseout", function () {
						bubble.style.paddingTop = '0'
						bubble.style.paddingBottom = '0'
						bubble.style.height = '0'
						bubble.style.opacity = '0'
					});
				})
			}
		},
		'debounce': {
			inserted(el) {
				el.addEventListener('click', e => {
					el.classList.add('is-disabled')
					el.disabled = true
					setTimeout(() => {
						el.disabled = false
						el.classList.remove('is-disabled')
					}, 1000)
				})
			}
		}
	}
}
</script>

<style lang="scss">
@import '@/style/style.scss';

body {
	font-family: "PingFang SC", "Microsoft Yahei", monospace;
}

.form_225 {
	width: 225px !important;
}

.form_130 {
	.el-form-item__label {
		width: 130px !important
	}
}

.messTable {
	tr {
		td, th {
			word-wrap: break-word;
			word-break: break-all;
		}
	}
}

.form_95 {
	.el-form-item__label {
		width: 95px !important;
		text-align: right !important;
	}
}

.form_130 {
	.el-input__inner {
		height: auto !important;
	}
}

.col_title {
	color: #333;
	font-size: 14px;
	font-weight: bold;
	position: relative;
	padding-left: 8px;

	&::after {
		content: "";
		display: inline-block;
		width: 3px;
		height: 17px;
		background-color: #2878ff;
		border-radius: 2px;
		position: absolute;
		top: 15px;
		left: 0;
	}
}

.textThere {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}

#toolPanel[data-v-6fb77b2a] {
	display: flex;
	justify-content: space-evenly;
	height: auto !important;
}

#mx-menu-default-container {
	z-index: 999999 !important;
}

.mx-context-menu {
	min-width: 100px !important;

	.mx-context-menu-item {
		justify-content: center !important;
	}

	.mx-item-row {
		.mx-icon-placeholder {
			width: 0 !important;
		}

		.label {
			padding-right: 0 !important;
		}
	}
}

.bubble {
	position: fixed;
	z-index: 99999;
	background-color: #fff;
	border: 1px solid #ccc;
	padding: 5px;
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
	word-wrap: break-word;
	transition: 0.3s;
	border-radius: 5px;
	overflow: hidden
}

#toolPanel .brush, #toolPanel .square, #toolPanel .text, #toolPanel .mosaicPen, #toolPanel .right-top, #toolPanel .round, #toolPanel .separateLine, #toolPanel .undo-disabled {
	display: none;
}

.app-container_flex {
	height: 100% !important;
	display: flex !important;
	flex-direction: column !important;

	.search_card {
		margin: 5px !important;

		.el-card__body {
			padding-top: 10px !important;
			padding-bottom: 0 !important;

			.el-form-item {
				margin-bottom: 9px !important;
			}
		}
	}

	.table_card {
		margin: 0 5px !important;
		flex: 1 !important;
		overflow: hidden !important;

		.el-card__header {
			padding: 10px !important;
			padding-bottom: 0 !important;
		}

		.el-card__body {
			padding: 10px !important;
			height: 100% !important;
		}
	}
}

.watermark-box::before {
	content: "被   驳   回"; /* 水印文字 */
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(-45deg);
	font-size: 100px;
	font-weight: bolder;
	color: rgba(255, 0, 0, 0.1);
	white-space: nowrap;
	pointer-events: none;
	z-index: 1;
}

.el-table .el-scrollbar__bar.is-horizontal {
	display: block !important; /* 始终显示 */
	opacity: 1 !important; /* 确保不透明 */
	height: 8px;

	.el-scrollbar__thumb {
		--el-scrollbar-bg-color: #2a75f6;
		--el-scrollbar-hover-bg-color: #78bafd;
		--el-scrollbar-opacity: 0.7;
		--el-scrollbar-hover-opacity: 0.7
	}
}
</style>
