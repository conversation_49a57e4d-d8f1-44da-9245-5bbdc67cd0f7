<template>
	<el-container>
		<el-main style="padding: 0 20px">
			<ytzhTable
				:hideDo="true"
				highlight-current-row
				ref="dataTable"
				:data="dataList"
				paginationLayout="total, prev, pager, next"
				:tablePage="tablePage"
				:pageChangeHandle="getDataList"
				:refreshDataListHandle="getDataList"
				row-key="id"
				stripe
				@selection-change="selectionChange"
			>
				<el-table-column align="center" label="执行时间" prop="taskExecuteTime" width="200">
					<template #default="scope">
						{{
							scope.row.taskExecuteTime
								? moment(scope.row.taskExecuteTime).format("YYYY-MM-DD HH:mm:ss")
								: "暂无"
						}}
					</template>
				</el-table-column>
				<el-table-column align="center" label="执行结果" prop="taskExecuteState" width="120">
					<template #default="scope">
						<el-tag v-if="scope.row.taskExecuteState === true" effect="dark" type="primary">成功</el-tag>
						<el-tag v-else effect="dark" type="danger">失败</el-tag>
					</template>
				</el-table-column>
				<el-table-column align="center" fixed="right" label="操作" width="152">
					<template #default="scope">
						<el-button icon="View" size="small" type="text" @click="show(scope.row)">查看详情</el-button>
					</template>
				</el-table-column>
			</ytzhTable>
		</el-main>
	</el-container>

	<el-drawer v-model="logsVisible" :size="620" destroy-on-close direction="rtl" title="日志">
		<el-container>
			<el-main style="padding: 0 20px 20px 20px">
				<el-collapse v-model="activeNames">
					<el-collapse-item name="1" title="日志信息">
						<div class="code">
							<el-text size="small" style="color: white">
								{{ logResult ? logResult : "暂无数据" }}
							</el-text>
						</div>
					</el-collapse-item>
					<el-collapse-item name="2" title="日志异常信息">
						<div class="code">
							<el-text size="small" style="color: white">
								{{ logException ? logException : "暂无数据" }}
							</el-text>
						</div>
					</el-collapse-item>
				</el-collapse>
			</el-main>
		</el-container>
	</el-drawer>
</template>

<script>
import moment from "moment/moment";
import {CircleCloseFilled, SuccessFilled} from "@element-plus/icons-vue";

export default {
	components: {CircleCloseFilled, SuccessFilled},
	computed: {
		moment() {
			return moment
		},
	},
	props: {
		taskInfo: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			logsVisible: false,
			activeNames: ['1', '2'],
			//数据列表
			dataList: [],
			logResult: '',
			logException: '',
			//分页参数
			tablePage: {
				//数据总数
				total: 0,
				//当前页码
				currentPage: 1,
				//每页条数
				pageSize: 20,
			},
			//查询表单
			searchForm: {
				name: "",
				url: "",
			},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
		};
	},
	mounted() {
		//刷新数据列表
		this.getDataList();
	},
	methods: {
		show(log) {
			this.logResult = log.taskExecuteResult
			this.logException = log.taskException
			this.logsVisible = true;
		},
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getDataList() {
			//页面加载
			this.listLoading = true;
			this.dataList = null;
			let res = await this.$API.sysTaskLogService.list({
				//当前页码
				current: this.tablePage.currentPage,
				//每页条数
				size: this.tablePage.pageSize,
				taskId: this.taskInfo.id,
				...this.searchForm,
			});
			if (res.code === 200) {
				//总数据条数
				this.tablePage.total = res.data.total;
				//数据列表
				this.dataList = res.data.records;
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
			this.listLoading = false;
		},
		/*
		 * 编辑数据
		 * @author: 路正宁
		 * @date: 2023-03-24 14:32:41
		 */
		editForm(row) {
			this.dialog.form = true;
			this.$nextTick(() => {
				this.$refs.formDialog.editView(row);
			});
		},
		/*
		 * 查看数据
		 * @author: 路正宁
		 * @date: 2023-03-24 14:32:55
		 */
		viewForm(row) {
			this.dialog.form = true;
			this.$nextTick(() => {
				this.$refs.formDialog.view(row);
			});
		},
		/*
		 * 删除数据，行内删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:35:00
		 */
		async deleteData(row, index) {
			this.listLoading = true;
			let res = await this.$API.sysTaskLogService.delete(row.id);
			if (res.code === 200) {
				this.$refs.dataTable.removeIndex(index);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
			this.listLoading = false;
		},
		/*
		 * 批量删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:36:11
		 */
		async deleteDataList() {
			//确认删除弹框
			let confirmRes = await this.$confirm(
				`确定删除选中的 ${this.selection.length} 项吗？`,
				"提示",
				{
					type: "warning",
					confirmButtonText: "删除",
					confirmButtonClass: "el-button--danger",
				}
			).catch(() => {
			});
			//确认结果处理
			if (!confirmRes) {
				return false;
			}
			//要删除的id数组
			let ids = this.selection.map((v) => v.id);
			//拼接的数组字符串，接口传参
			let idStr = this.selection.map((v) => v.id).join(",");
			//页面加载中
			this.listLoading = true;
			let res = await this.$API.sysTaskLogService.delete(idStr);
			if (res.code === 200) {
				//从列表中移除已删除的数据
				this.$refs.dataTable.removeKeys(ids);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
			//释放页面加载中
			this.listLoading = false;
		},
		/*
		 * 表格选择后回调事件
		 * @author: 路正宁
		 * @date: 2023-03-24 14:51:09
		 */
		selectionChange(selection) {
			this.selection = selection;
		},
	},
};
</script>

<style scoped>
.code {
	background: #848484;
	padding: 15px;
	color: #fff;
	font-size: 12px;
	border-radius: 4px;
}
</style>
