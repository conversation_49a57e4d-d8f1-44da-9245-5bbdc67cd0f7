<template>
	<el-dialog
		v-if="visible"
		v-model="visible"
		title="角色选择"
		top="12vh"
		width="72%"
		@closed="$emit('closed')"
	>
		<el-container v-loading="listLoading" style="cursor: pointer;height: 57vh">
			<el-main style="padding: 0;border: 1px solid var(--el-border-color-light);display: flex;
    			justify-content: space-between;align-items: stretch;flex-direction: column;flex-wrap: nowrap;">
				<el-header>
					<div class="right-panel-search">
						<el-input v-model="searchForm.name" clearable placeholder="输入角色名称"></el-input>
						<el-button icon="el-icon-search" type="primary" @click="search"></el-button>
					</div>
				</el-header>
				<el-main class="noPadding">
					<ytzhTable
						ref="dataTable"
						:data="dataList"
						:pageChangeHandle="getDataList"
						:refreshDataListHandle="getDataList"
						:tablePage="tablePage"
						highlight-current-row
						row-key="id"
						stripe
						@current-change="handleCurrentChange"
					>
						<el-table-column label="角色名称" prop="name"></el-table-column>
						<el-table-column label="所属机构" prop="sysOrg.name"></el-table-column>
						<el-table-column align="center" label="系统数据" prop="sys" width="92">
							<template #default="scope">
								<el-tag v-if="scope.row.sys === true" type="success">是</el-tag>
								<el-tag v-if="scope.row.sys === false">否</el-tag>
							</template>
						</el-table-column>
						<el-table-column align="center" label="共享角色" prop="common" width="92">
							<template #default="scope">
								<el-tag v-if="scope.row.common === true" type="success">是</el-tag>
								<el-tag v-if="scope.row.common === false">否</el-tag>
							</template>
						</el-table-column>
						<el-table-column align="center" label="是否可用" prop="useable" width="92">
							<template #default="scope">
								<el-tag v-if="scope.row.useable === true" type="success">可用</el-tag>
								<el-tag v-if="scope.row.useable === false">不可用</el-tag>
							</template>
						</el-table-column>
					</ytzhTable>
				</el-main>
			</el-main>
			<el-aside style="border: 1px solid var(--el-border-color-light);display: flex;flex-direction: column;
    			flex-wrap: nowrap;align-items: stretch;justify-content: space-between;width: 32%">
				<el-header>
					<h4>已选择的角色</h4>
				</el-header>
				<el-main style="padding: 0">
					<el-table ref="selectTableList" :data="selectData" height="100%" highlight-current-row>
						<el-table-column label="角色名称" prop="name"></el-table-column>
						<el-table-column label="角色所属机构" prop="sysOrg.name"></el-table-column>
						<el-table-column align="center" fixed="right" label="操作" width="72">
							<template #default="scope">
								<el-button plain size="small" type="text"
										   @click="deleteSelect(scope.row, scope.$index)">
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
			</el-aside>
		</el-container>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" @click="submitComplet()">确 定</el-button>
		</template>
	</el-dialog>
</template>

<script>
import sysRoleService from "@/api/model/sys/sysRoleService";

export default {
	components: {},
	props: {
		//是否多选
		isMultiple: {type: Boolean, default: false},
		//回调函数
		selectChange: {type: Function},
	},
	data() {
		return {
			//数据列表
			dataList: [],
			//分页参数
			tablePage: {
				//数据总数
				total: 0,
				//当前页码
				currentPage: 1,
				//每页条数
				pageSize: 5,
			},
			//查询表单
			searchForm: {},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
			//已选择的数据
			selectData: [],
			//对象回传
			callData: {},
			visible: false,
		};
	},
	mounted() {
		setTimeout(() => {
			//刷新数据列表
			this.getDataList();
		}, 0);
	},
	methods: {
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getDataList() {
			//初始化数据列表
			this.dataList = [];
			//请求接口
			var res = await this.reqeustList();
			if (res.code == 200) {
				//总数据条数
				this.tablePage.total = res.data.total;
				//数据列表
				this.dataList = res.data.records;
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},
		/*
		 * 列表请求接口
		 * @author: 路正宁
		 * @date: 2023-03-30 11:20:01
		 */
		async reqeustList(pageSize = this.tablePage.pageSize) {
			//debugger;
			//页面加载
			this.listLoading = true;
			var res = await sysRoleService.list({
				//当前页码
				current: this.tablePage.currentPage,
				//每页条数
				size: pageSize,
				//排序查询
				orders: this.tablePage.orders,
				//查询参数
				...this.searchForm,
			});
			this.listLoading = false;
			return res;
		},
		/*
		 * 表格选择后回调事件，单选
		 * @author: 路正宁
		 * @date: 2023-03-31 17:38:42
		 */
		handleCurrentChange(val) {
			if (this.isMultiple == true) {
				//多选
				//选中数据去重
				for (var i = 0; i < this.selectData.length; i++) {
					if (this.selectData[i].id == val.id) {
						this.selectData[i] = val;
						return;
					}
				}
				this.selectData.push(val);
			} else {
				//单选
				this.selectData = [];
				this.selectData.push(val);
			}
			this.$message.success("选择了[" + val.name + "]");
		},
		/*
		 * 头部搜索框
		 * @author: 路正宁
		 * @date: 2023-03-24 14:58:47
		 */
		search() {
			this.getDataList();
		},
		/*
		 * 选中的数据
		 * @author: 路正宁
		 * @date: 2023-03-31 17:26:29
		 */
		init(callData, selectData) {
			this.selectData = selectData;
			this.callData = callData;
			this.visible = true;
		},
		/*
		 * 删除已选择列表中的数据
		 * @author: 路正宁
		 * @date: 2023-04-03 09:47:24
		 */
		deleteSelect(row, index) {
			this.selectData.splice(index, 1);
		},
		/*
		 * 提交选择结果
		 * @author: 路正宁
		 * @date: 2023-04-03 09:55:23
		 */
		submitComplet() {
			this.selectChange(this.callData, this.selectData);
			this.visible = false;
		},
	},
};
</script>

<style scoped>

</style>
