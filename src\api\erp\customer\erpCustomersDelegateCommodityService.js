import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/customer/erpCustomersDelegateCommodity/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/customer/erpCustomersDelegateCommodity/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/customer/erpCustomersDelegateCommodity/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/customer/erpCustomersDelegateCommodity/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/customer/erpCustomersDelegateCommodity/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/customer/erpCustomersDelegateCommodity/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/customer/erpCustomersDelegateCommodity/import',
      data
    )
  }
}
