<template>
    <el-container>
        <el-main class="noPadding">
			<el-card body-style="padding-top: 0;padding-bottom: 0;" class="box-card" style="margin:10px;">
				<el-tabs v-model="activeName" v-loading="loading" class="demo-tabs" @tab-change="handleClick">
					<el-tab-pane label="待审批" name="1">
						<!-- <div style="margin-bottom: 10px;">
							<el-form :model="form" ref="formList" label-position="left" :inline="true" label-width="70px">
								<el-form-item label="借阅人" prop="name" style="margin: 0;padding-right: 30px;">
									<el-input v-model="form.name" placeholder="请输入档案名称" />
								</el-form-item>
								<el-form-item label="档案号" prop="num" style="margin: 0;padding-right: 30px;">
									<el-input v-model="form.num" placeholder="请输入档案号" />
								</el-form-item>
								<el-form-item style="margin: 0;padding-left: 50px;">
									<el-button type="primary" :icon="Search" @click="() => handleQuery()">查询</el-button>
									<el-button :icon="RefreshRight" plain @click="() => resetQuery()">重置</el-button>
								</el-form-item>
							</el-form>
						</div> -->
                        <div class="card-header"
                            style="display: flex;justify-content:space-between;align-items:center;margin-bottom: 10px;">
                            <div>
								<el-select v-model="batch" placeholder="请选择审核操作" style="width: 200px">
									<el-option label="批量同意" value="1" />
									<el-option label="批量驳回" value="2" />
								</el-select>
								<el-button :disabled="!handList.length || !batch" icon="Check"
										   plain style="margin-left: 10px" type="primary" @click="() => handleAllSub()">确定
								</el-button>
							</div>
                            <div>
                                <span style="margin-right: 15px;" @click="getList">
                                    <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                            <Refresh />
                                        </el-icon>
                                    </el-tooltip>
                                </span>
                                <span @click="screen">
                                    <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC"
                                            style="cursor:pointer;"><el-icon-full-screen /></el-icon>
                                    </el-tooltip>
                                </span>
                            </div>
                        </div>
                        <el-table :data="receiveData" border @selection-change="handleSelectionChange">
                            <el-table-column align="center" fixed="left" min-width="30" type="selection" width="50" />
                            <el-table-column align="center" label="借阅人" prop="borrowApply.name" width="280" />
                            <el-table-column align="center" label="借阅部门" prop="borrowApply.sysOffice.name" width="250" />
                            <el-table-column align="center" label="借阅起始日期" prop="borrowStartTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowStartTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
                            <el-table-column align="center" label="借阅归还日期" prop="borrowEndTime" width="180">
                                <template #default="scope">
                                    <span>{{
											moment(scope.row.borrowEndTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="是否包含水印" min-width="100" prop="borrowIsWatermark">
                                <template #default="scope">
                                    <span>{{ scope.row.borrowIsWatermark == '1' ? '是' : '否' }}</span>
                                </template>
                            </el-table-column>
							<!--                            <el-table-column label="审核状态" align="center" prop="borrowAuditStatus">-->
							<!--                                <template #default="scope">-->
							<!--                                    <span>{{ scope.row.borrowAuditStatus === '2' ? '未审核' : '已审核' }}</span>-->
							<!--                                </template>-->
							<!--                            </el-table-column>-->
							<el-table-column align="center" label="借阅理由" min-width="180" prop="borrowRemark" show-overflow-tooltip />
                            <el-table-column align="center" fixed="right" label="操作" min-width="180px">
                                <template #default="scope">
                                    <el-button icon="RefreshLeft" link size="small" type="warning" @click="revokeBorrow(scope.row)">撤销</el-button>
                                    <el-button icon="Finished" link size="small" type="primary" @click="collectFile(scope.row)">审批</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 22px" @pagination="getList()"/>
						</div>
                    </el-tab-pane>

                    <el-tab-pane label="已审批" name="2">
                        <!-- <div style="margin-bottom: 10px;">
                            <el-form :model="form" ref="formList" label-position="left" :inline="true" label-width="70px">
                                <el-form-item label="档案名称" prop="name" style="margin: 0;padding-right: 30px;">
                                    <el-input v-model="form.name" placeholder="请输入档案名称" />
                                </el-form-item>
                                <el-form-item label="档案号" prop="num" style="margin: 0;padding-right: 30px;">
                                    <el-input v-model="form.num" placeholder="请输入档案号" />
                                </el-form-item>
                                <el-form-item style="margin: 0;padding-left: 50px;">
                                    <el-button type="primary" :icon="Search" @click="() => handleQuery()">查询</el-button>
                                    <el-button :icon="RefreshRight" plain @click="() => resetQuery()">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </div> -->
                        <el-table :data="receiveStop" border @selection-change="handleSelectionChange">
                            <el-table-column align="center" fixed="left" min-width="30" type="selection" width="50" />
                            <el-table-column align="center" label="借阅人" prop="borrowApply.name" width="280" />
                            <el-table-column align="center" label="借阅部门" prop="borrowApply.sysOffice.name" width="250" />
							<el-table-column align="center" label="借阅起始日期" prop="borrowStartTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowStartTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
                            <el-table-column align="center" label="借阅归还日期" prop="borrowEndTime" width="180">
                                <template #default="scope">
                                    <span>{{
											moment(scope.row.borrowEndTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="是否包含水印" min-width="100" prop="borrowIsWatermark">
                                <template #default="scope">
                                    <span>{{ scope.row.borrowIsWatermark == '1' ? '是' : '否' }}</span>
                                </template>
                            </el-table-column>
							<!--                            <el-table-column label="审核状态" align="center" prop="borrowAuditStatus">-->
							<!--                                <template #default="scope">-->
							<!--                                    <span>{{ scope.row.borrowAuditStatus == '1' ? '未审核' : '已审核' }}</span>-->
							<!--                                </template>-->
							<!--                            </el-table-column>-->
							<el-table-column align="center" label="借阅理由" min-width="180" prop="borrowRemark" show-overflow-tooltip />
                            <el-table-column align="center" fixed="right" label="操作" min-width="120px">
                                <template #default="scope">
                                    <el-button icon="View" link size="small" type="primary" @click="collectFile(scope.row, '1')">查看审批</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 22px" @pagination="getList()"/>
						</div>
                    </el-tab-pane>
                </el-tabs>
            </el-card>
        </el-main>
        <!-- 审批 -->
        <el-dialog v-if="open" v-model="open" :title="title" append-to-body width="1300px">
            <examine :receiveId="receiveId" :receiveType="receiveType" @childMove="parentView"></examine>
        </el-dialog>
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog v-model="openList" :title="title" append-to-body width="600px">
            <span style="color:#333;line-height:30px;font-size:14px">
                将对以下{{
					handList.length
				}}个审核任务进行批量{{ batch == '2' ? '驳回' : '同意' }}操作，请确定
            </span>
            <div v-for="(item, index) in handList" :key="index" style="display: flex;line-height:30px">
                <p style="width:40%">
                    <span style="color:#333;font-size:14px;">ID：</span>
                    <span style="color:#666">{{ item.id }}</span>
                </p>
                <p>
                    <span style="color:#333;font-size:14px;">借阅人：</span>
                    <span style="color:#666">{{ item.borrowApply.name }}</span>
                </p>
            </div>
            <div v-show="batch == '2' ? true : false">
                <span>驳回原因</span>
                <el-input v-model="idea" clearable placeholder="请输入驳回原因" style="width: 240px" />
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定
                    </el-button>
                    <el-button @click="() => (openList = false)">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </el-container>
</template>
<script setup>
import {Refresh} from '@element-plus/icons-vue'
import vitalizationArchiveList from '@/api/archive/vitalizationArchive'
import sysNoticeService from "@/api/model/approve/sysNoticeService";
import examine from './examine.vue';
import tool from '@/utils/tool';
import moment from "moment";
import {getCurrentInstance, reactive, ref, toRefs, onMounted} from 'vue'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance()
const route = useRoute()
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    }
})
const total = ref(0)
const { queryParams } = toRefs(data)
const activeName = ref('1')

// 是否显示审批
const open = ref(false)
const title = ref('')
const receiveId = ref('')
const receiveType = ref('')
// 审批
function collectFile(val, type) {
    console.log(val, type);
    receiveId.value = val;
    if (type == '1') {
        receiveType.value = type;
        title.value = '查看审批';
    } else {
        title.value = '审批';
    }
    open.value = true;
}
// 关闭审批
function parentView() {
    getList();
    receiveId.value = '';
    receiveType.value = '';
    open.value = false;
}
// 切换tab时
const handleClick = (tabName) => {
    activeName.value = tabName;
    getList();
}

const batch = ref("");
const openList = ref(false);
const handleAllSub = () => {
    if (batch.value == "2") {
        title.value = "批量驳回";
    } else {
        title.value = "批量同意";
    }
    idea.value = '';
    openList.value = true;
};
const newFilArr = ref([]);
const idea = ref("");

function submitForm() {
    let idss = "";
    let status = "";
    if (batch.value == "2") {
        status = "5";
    } else {
        status = "6";
    }
    newFilArr.value = [];
    handList.value.filter((item) => {
        newFilArr.value.push(item.id);
    });
    idss = newFilArr.value.join(",");
    sysNoticeService.handbatch({ ids: idss, status: status, idea: idea.value })
        .then((res) => {
            if (res.code == 200) {
                proxy.msgSuccess(`批量${batch.value === '2'?'驳回':'同意'}操作成功`);
                openList.value = false;
                getList();
            } else {
                proxy.msgError(res.msg);
            }
        });
}

// 头部查询
// const form = ref([])
// function handleQuery() {
//     loading.value = true;
//     vitalizationArchiveList.borrowList({
//         current: queryParams.value.current,
//         size: queryParams.value.size,
//         borrowAuditStatus: activeName.value == 1 ? '2' : '7',
//     }).then(res => {
//         if (res.code === 200) {
//             if (activeName.value == '1') {
//                 receiveData.value = res.data.records;
//             } else {
//                 receiveStop.value = res.data.records;
//             }
//             total.value = res.data.total;
//             loading.value = false;
//         }
//     }).catch(() => {
//         proxy.msgError('查询失败');
//     })
// }
// 重置
// function resetQuery() {
//     form.value = [];
//     getList();
// }

//全屏
function screen() {
    var element = document.documentElement;
    tool.screen(element);
}

// 列表
const receiveData = ref([]) //待审批
const receiveStop = ref([]) //已审批

const loading = ref(true)
const handList = ref([])
// 选中表格
function handleSelectionChange(val) {
    handList.value = val;
}

// 进入时查询全部
function getList() {
    vitalizationArchiveList.borrowList({
        current: queryParams.value.current,
        size: queryParams.value.size,
        borrowAuditType: activeName.value == 1 ? '1' : '2',
    }).then(res => {
        if (res.code === 200) {
            if (activeName.value == '1') {
                receiveData.value = res.data.records;
            } else {
                receiveStop.value = res.data.records;
            }
            total.value = res.data.total;
            loading.value = false;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

// 撤销借阅申请
function revokeBorrow(row) {
    proxy.$confirm('确定要撤销该借阅申请吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        vitalizationArchiveList.revokeBorrow({
            id: row.id
        }).then(res => {
            if (res.code === 200) {
                proxy.msgSuccess('撤销成功');
                getList();
            } else {
                proxy.msgError(res.message || '撤销失败');
            }
        }).catch(() => {
            proxy.msgError('撤销失败');
        });
    }).catch(() => {
        // 用户取消操作
    });
}

// 初始化页面，处理路由参数
onMounted(() => {
    // 检查路由参数中是否有tab参数
    if (route.query.tab) {
        activeName.value = route.query.tab;
    }
    getList();
});
</script>
<style scoped>
.fileUrl {
    margin-bottom: 10px;
    cursor: pointer;
}

.fileUrl:hover {
    color: #2a76f8;
}

p {
    width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
