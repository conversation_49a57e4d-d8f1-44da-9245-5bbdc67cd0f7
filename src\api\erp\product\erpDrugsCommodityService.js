import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/product/erpDrugsCommodity/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/product/erpDrugsCommodity/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/product/erpDrugsCommodity/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/product/erpDrugsCommodity/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/product/erpDrugsCommodity/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/product/erpDrugsCommodity/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/product/erpDrugsCommodity/import',
      data
    )
  }
}
