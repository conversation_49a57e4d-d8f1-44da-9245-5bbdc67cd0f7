import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/storage/erpStorehouseArea/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/storage/erpStorehouseArea/delete',ids)
  },
  save: function (params) {
    return http.post('/erp/storage/erpStorehouseArea/save',params)
  },
  detail: function (id) {
    return http.get('/erp/storage/erpStorehouseArea/queryById',id)
  },
  logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
}