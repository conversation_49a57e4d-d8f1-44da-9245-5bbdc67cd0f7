import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/sales/erpSalesOrderApproval/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/sales/erpSalesOrderApproval/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/sales/erpSalesOrderApproval/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrderApproval/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/sales/erpSalesOrderApproval/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrderApproval/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/sales/erpSalesOrderApproval/import',
      data
    )
  }
}
