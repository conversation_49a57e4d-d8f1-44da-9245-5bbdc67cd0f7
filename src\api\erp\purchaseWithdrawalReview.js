/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-11 10:08:03
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-16 17:31:38
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\manufacturerManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
  getList: function (inputForm) {
    return http.get('/erp/procure/retreat/erpPurchaseRetreat/list',inputForm)
  },

  getIdOrder: function (inputForm) {
    return http.get('/erp/procure/retreat/erpPurchaseRetreat/purchaseRetreatDetailById',inputForm)
  },
  
  save: function (data) {
    return http.post('/erp/procure/retreat/erpPurchaseRetreat/createPurchaseRetreat',data)
  },
  getReview: function (inputForm) {
    return http.get('/erp/procure/retreat/erpPurchaseRetreatApproval/list',inputForm)
  },
  getOut: function (inputForm) {
    return http.get('/erp/procure/retreat/erpPurchaseRetreatOutbound/list',inputForm)
  },
  
}
