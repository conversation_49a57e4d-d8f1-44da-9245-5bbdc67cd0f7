<template>
    <div v-loading="loading">
        <el-descriptions :column="3">
            <el-descriptions-item label="报告编号:" label-align="right" align="left" label-class-name="my-label">{{
                props.receiveId.id }}</el-descriptions-item>
            <el-descriptions-item label="检测总数:" label-align="right" align="left" label-class-name="my-label">{{
                props.receiveId.recordDetectionItemCount }}</el-descriptions-item>
            <el-descriptions-item label="案卷/文件:" label-align="right" align="left" label-class-name="my-label">{{
                props.receiveId.recordDetectionItemCount }}</el-descriptions-item>
            <el-descriptions-item label="数据评价:" label-align="right" align="left" label-class-name="my-label">{{
                props.receiveId.recordDetectionResult }}</el-descriptions-item>
        </el-descriptions>
        <div v-for="(item, index) in recordDetectionList" :key="index">
            <div style="margin-bottom: 10px;">
                <span style="font-size: 14px;font-weight: bold;">{{ formDict(data.fourConfig.recordFourConfigLevelTwo,
                    item.recordDetectionItem.recordFourConfigLevelTwo) }}</span>
            </div>
            <el-table :data="recordDetectionList">
                <el-table-column align="center" min-width="30" label="序号" type="index" width="50"/>
                <el-table-column label="一级分类" align="left" prop="recordFourConfigLevelOne" width="100">
                    <template #default="scope">
                        {{ formDict(data.fourConfig.recordFourConfigLevelOne, scope.row.recordDetectionItem.recordFourConfigLevelOne) }}
                    </template>
                </el-table-column>
                <el-table-column label="二级分类" align="left" prop="recordFourConfigLevelTwo" width="150">
                    <template #default="scope">
                        {{ formDict(data.fourConfig.recordFourConfigLevelTwo, scope.row.recordDetectionItem.recordFourConfigLevelTwo) }}
                    </template>
                </el-table-column>
                <el-table-column label="编号" align="left" prop="recordFourConfigNum" width="80">
                    <template #default="scope">
                        {{ scope.row.recordDetectionItem.recordFourConfigNum}}
                    </template>
                </el-table-column>
                <el-table-column label="检测目的" align="left" prop="recordFourConfigItem" width="200">
                    <template #default="scope">
                        {{ scope.row.recordDetectionItem.recordFourConfigItem}}
                    </template>
                </el-table-column>
                <el-table-column label="检测对象" align="left" prop="recordFourConfigObject" width="120">
                    <template #default="scope">
                        {{ scope.row.recordDetectionItem.recordFourConfigObject}}
                    </template>
                </el-table-column>
                <el-table-column label="检测依据和方法" align="left" prop="recordFourConfigMethod">
                    <template #default="scope">
                        {{ scope.row.recordDetectionItem.recordFourConfigMethod}}
                    </template>
                </el-table-column>
                <el-table-column label="检测结果" align="left" prop="recordDetectionItemReturn" width="80">
                    <template #default="scope">
                        {{ scope.row.recordDetectionItemReturn == 0 ? '成功' : '失败' }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, defineProps, toRefs } from 'vue'
import detection from "@/api/archive/archiveReception/fourTesting"
const { proxy } = getCurrentInstance()
const props = defineProps({
    receiveId: {
        type: Array
    }
})
const data = reactive({
    fourConfig: {},
});
const loading = ref(true)
const { fourConfig } = toRefs(data);
// 获取到的检测信息
const recordDetectionList = ref([])
// 获取检测信息
function getList() {
    detection.detailsList({
        recordDetectionId: props.receiveId.id,
    }).then(res => {
        if (res.code === 200) {
            recordDetectionList.value = res.data.records;
            loading.value = false;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

//字典值表格转换
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val);
}

// 字典值
async function dict() {
    fourConfig.value.recordFourConfigLevelOne = await proxy.getDictList("four_config_link");
    fourConfig.value.recordFourConfigLevelTwo = await proxy.getDictList("four_config_check_type");
}

getList();
dict();
</script>