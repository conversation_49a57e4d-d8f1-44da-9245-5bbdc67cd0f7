<template>
	<el-main style="padding:0 20px;">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="日志类型">
				<el-text v-if="data.logType === '0'" size="small">登录日志</el-text>
				<el-text v-else-if="data.logType === '1'" size="small">提示日志</el-text>
				<el-text v-else-if="data.logType === '2'" size="small">警告日志</el-text>
				<el-text v-else-if="data.logType === '3'" size="small">错误日志</el-text>
				<el-text v-else-if="data.logType === '4'" size="small">严重日志</el-text>
				<el-text v-else-if="data.logType === '5'" size="small">新增日志</el-text>
				<el-text v-else-if="data.logType === '6'" size="small">修改日志</el-text>
				<el-text v-else-if="data.logType === '7'" size="small">删除日志</el-text>
				<el-text v-else size="small">暂无数据</el-text>
			</el-descriptions-item>
			<el-descriptions-item label="日志名称">
				{{ data.name ? data.name : "暂无数据" }}
			</el-descriptions-item>
			<el-descriptions-item label="请求地址">
				{{ data.url ? data.url : "暂无数据" }}
			</el-descriptions-item>
			<el-descriptions-item label="操作用户">
				{{ data.controlUser ? data.controlUser.name : "暂无数据" }}
			</el-descriptions-item>
			<el-descriptions-item label="日志时间">
				{{ formatData(data.createDate) }}
			</el-descriptions-item>
		</el-descriptions>
		<el-collapse v-model="activeNames" style="margin-top: 20px;">
			<el-collapse-item name="1" title="日志信息">
				<div class="code">
					<el-text size="small" style="color: white">
						{{ data.pointMessage ? data.pointMessage : "暂无数据" }}
					</el-text>
				</div>
			</el-collapse-item>
			<el-collapse-item name="2" title="日志详细信息">
				<div class="code">
					<el-text size="small" style="color: white">
						{{ data.pointDetailed ? data.pointDetailed : "暂无数据" }}
					</el-text>
				</div>
			</el-collapse-item>
		</el-collapse>
	</el-main>
</template>

<script>
import moment from "moment";

export default {
	data() {
		return {
			data: {},
			activeNames: ['1', '2'],
			typeMap: {
				'info': "info",
				'warn': "warning",
				'error': "error"
			}
		}
	},
	methods: {
		setData(data) {
			this.data = data
		},
		formatData(time) {
			return moment(time).format("YYYY-MM-DD HH:mm:ss");
		},
	}
}
</script>

<style scoped>
.code {
	background: #848484;
	padding: 15px;
	color: #fff;
	font-size: 12px;
	border-radius: 4px;
}
</style>
