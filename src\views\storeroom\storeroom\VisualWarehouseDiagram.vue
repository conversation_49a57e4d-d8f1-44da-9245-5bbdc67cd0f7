<template>
	<div :id="chartId" v-loading="loadingData"></div>
</template>

<script setup>
import {onMounted, onBeforeUnmount, ref, getCurrentInstance} from 'vue';
import warehouse from "@/api/archive/storeroom/storeroom";
import * as echarts from 'echarts/core';
const {proxy} = getCurrentInstance()

const props = defineProps({
	warehouseId: {
		type: String,
		// required: true
	},
});

const chartId = `warehouse-${Math.random().toString(16).substring(2, 9)}`;
const diskData = ref([]);
const loadingData = ref(false);
let myChart = null;

onMounted(() => {
	const container = document.getElementById(chartId);
	loadingData.value = true;
	if (container) {
		warehouse.queryVisual({
			warehouseId: props.warehouseId
		}).then(result => {
			if (result.code === 200) {
				diskData.value = result.data;
				myChart = echarts.init(container);
				initChart();
				loadingData.value = false;
			}
		}).catch(error => {
			console.log(error);
			proxy.msgError('查询失败');
		});
	}
});

onBeforeUnmount(() => {
	if (myChart) {
		myChart.dispose();
		myChart = null;
	}
});

function getLevelOption() {
	return [
		{
			itemStyle: {
				borderColor: '#777',
				borderWidth: 0,
				gapWidth: 1
			},
			upperLabel: {
				show: false
			}
		},
		{
			itemStyle: {
				borderColor: '#555',
				borderWidth: 5,
				gapWidth: 1
			},
			emphasis: {
				itemStyle: {
					borderColor: '#ddd'
				}
			}
		},
		{
			colorSaturation: [0.35, 0.5],
			itemStyle: {
				borderWidth: 5,
				gapWidth: 1,
				borderColorSaturation: 0.6
			}
		}
	];
}

const initChart = () => {
	const option = {
		// title: {
		// 	text: 'Disk Usage',
		// 	left: 'center'
		// },
		tooltip: {
			formatter: function (info) {
				let value = info.value;
				let treePathInfo = info.treePathInfo;
				let treePath = [];
				for (let i = 1; i < treePathInfo.length; i++) {
					treePath.push(treePathInfo[i].name);
				}
				return [
					'<div class="tooltip-title">路径: ' + echarts.format.encodeHTML(treePath.join('/')) + '</div>',
					'档案数量: ' + echarts.format.addCommas(value) + ' 个'
				].join('');
			}
		},
		series: [
			{
				name: 'Disk Usage',
				type: 'treemap',
				visibleMin: 300,
				label: {
					show: true,
					formatter: '{b}'
				},
				upperLabel: {
					show: true,
					height: 30
				},
				itemStyle: {
					borderColor: '#fff'
				},
				levels: getLevelOption(),
				data: diskData.value
			}
		]
	}

	if (myChart) {
		myChart.setOption(option);
	}
};
</script>

<style scoped>
.bodyStyle {
	width: 100%;
	height: 100%;
}

::v-deep .el-dialog__body {
	width: 100%;
	height: 100%;
}
</style>
