<template>
	<el-dialog v-if="props.open" v-model="props.open" :title="props.title" align-center width="42%"
			   @close="closeDialog">
		<el-form ref="formRef" v-loading="data.loading" :model="data.form" :rules="data.rules" label-width="auto"
				 style="margin-top: 0;padding-right: 20px;">
			<el-form-item label="名称" prop="name">
				<el-input v-model="data.form.name" placeholder="请输入名称"/>
			</el-form-item>
			<el-form-item label="目标名称" prop="targetName">
				<el-input v-model="data.form.targetName" placeholder="请输入目标名称"/>
			</el-form-item>
			<el-form-item label="描述" prop="remark">
				<el-input v-model="data.form.remark" placeholder="请输入描述"/>
			</el-form-item>
			<el-form-item label="排序" prop="sort">
				<el-input v-model="data.form.sort" placeholder="请输入排序"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="saveData(data.form)">确 定</el-button>
				<el-button type="danger" @click="closeDialog">取 消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import dataStructure from "@/api/archive/dataStructureManagement/dataStructure";
import {getCurrentInstance, reactive, ref, watch} from "vue";

const {proxy} = getCurrentInstance();
const props = defineProps({
	title: {
		type: String,
		default: '新增'
	},
	open: {
		type: Boolean,
		default: false
	},
	data: {
		type: Object,
		default: () => {
			return {}
		}
	}
});
const emit = defineEmits(['close']);
const data = reactive({
	//校验
	rules: {
		name: [{required: true, message: "名称不能为空", trigger: "blur"}],
		targetName: [{required: true, message: "目标名称不能为空", trigger: "blur"}],
		remark: [{required: true, message: "描述不能为空", trigger: "blur"}],
		sort: [{required: true, message: "排序不能为空", trigger: "blur"}],
	},
	// 新增表单
	form: {},
	// 加载状态
	loading: false,
});
const formRef = ref(null);

watch(() => props.data, (newData) => {
	data.form = newData;
}, {deep: true});

/*
 * 关闭弹窗
 */
function closeDialog() {
	emit('close', '');
}

/*
 * 更新门类配置
 */
function saveData(data) {
	let newMenuData = {
		//父级ID
		parent: {
			id: data.parentId,
		},
		// id
		id: data.id,
		// 配置名称
		name: data.name,
		//  数据类型
		targetName: data.targetName,
		//  配置描述
		remark: data.remark,
		//  排序
		sort: data.sort,
	};
	formRef.value.validate((valid) => {
		if (valid) {
			dataStructure.save(newMenuData).then(res => {
				if (res.code === 200) {
					formRef.value.resetFields();
					proxy.msgSuccess("修改成功");
					emit('close', '');
				}
			}).catch(error => {
				proxy.msgError(error, "修改失败");
			});
		}
	});
}
</script>

<style scoped>

</style>
