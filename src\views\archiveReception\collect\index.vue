<template>
	<el-container>
		<el-aside style="border-right: 0;padding: 10px 0 10px 10px;background: none" width="21%">
			<el-card :body-style="{ padding: '8px 0 0 0', height: '100%', width: '100%' }"
					 style="height: 100%;width: 100%;">
				<comDoorTable @clickChild="clickEven"/>
			</el-card>
		</el-aside>
		<el-main style="padding: 10px;row-gap: 10px;display: flex;flex-direction: column;flex-wrap: nowrap;">
			<el-card style="height: auto;width: 100%">
				<div style="height: 100%;width: 100%">
					<el-form ref="formList" :inline="true" :model="form" label-position="left"
							 label-width="auto">
						<el-form-item label="档案名称" prop="name" style="margin: 0;width:32%">
							<el-input v-model="form.name" placeholder="请输入档案名称" clearable @keydown.enter="handleQuery"/>
						</el-form-item>
						<el-form-item style="margin: 0;padding-left: 10px;">
							<el-button icon="Search" type="primary" @click="handleQuery">
								查询
							</el-button>
							<el-button icon="RefreshRight" plain @click="resetQuery">
								重置
							</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-card>
			<el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }"
					 style="height: 91%;width: 100%">
				<el-container>
					<el-header height="auto" style="padding: 0 0 10px 0">
						<div style="width:100%;display: flex;justify-content:space-between;align-items:center;">
							<div>
								<el-button :loading="collectFileStatus" icon="MessageBox" plain type="warning"
										   @click="() => collectFile()">
									归档
								</el-button>
								<el-button icon="Upload" plain type="primary" @click="() => collectUpload()">
									原文上传
								</el-button>
								<el-button icon="Plus" plain type="primary" @click="() => collectAdd()">
									新建
								</el-button>
								<el-button icon="Switch" plain type="primary" @click="() => collectMove()">
									移动
								</el-button>
								<el-button icon="Delete" plain type="danger" @click="() => collectDelete()">
									删除
								</el-button>
							</div>
							<div style="display: flex;align-items: center">
								<div style="margin-right: 15px;" @click="getList">
									<el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
										<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
											<Refresh/>
										</el-icon>
									</el-tooltip>
								</div>
								<div @click="screen">
									<el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
										<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
											<FullScreen/>
										</el-icon>
									</el-tooltip>
								</div>
							</div>
						</div>
					</el-header>
					<el-main style="padding: 0">
						<el-table v-loading="dataLoading" :data="receiveData" border style="height: 100%;width: 100%"
								  @selection-change="handleSelectionChange">
							<el-table-column align="center" min-width="30" type="selection" width="50"/>
							<el-table-column align="left" label="档案名称" min-width="180" prop="name"/>
							<el-table-column align="center" label="档案年份" prop="year" width="83"/>
							<el-table-column align="center" label="档案月份" prop="month" width="83"/>
							<el-table-column align="center" label="保留年限" prop="retentionPeriod" width="83">
								<template #default="scope">
									{{ reserve(scope.row.retentionPeriod) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="控制等级" prop="controlStatus" width="120">
								<template #default="scope">
									{{ control(scope.row.controlStatus) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="所属部门" prop="office.name" width="auto">
								<template #default="scope">
									{{
										scope.row.office ? scope.row?.office?.name ? scope.row.office.name : '暂无' : '暂无'
									}}
								</template>
							</el-table-column>
							<el-table-column align="center" label="档案创建时间" min-width="180" prop="createDate">
								<template #default="scope">
									{{
										(scope.row.createDate
												? moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss')
												: undefined
										) || '--'
									}}
								</template>
							</el-table-column>
							<el-table-column align="center" fixed="right" label="操作" width="210">
								<template #default="scope">
									<div style="display: flex;flex-direction: row;flex-wrap: nowrap;
											align-items: center;width: 100%;height: 100%">
										<el-button icon="View" link type="primary" @click="collectView(scope.row)">
											查看
										</el-button>
										<el-button icon="Edit" link type="warning" @click="collectEdit(scope.row)">
											编辑
										</el-button>
										<el-dropdown>
											<el-button icon="ArrowDown" link type="primary">
												更多
											</el-button>
											<template #dropdown>
												<el-dropdown-menu>
													<el-dropdown-item>
														<el-button icon="MessageBox" link type="success"
																   @click="collectFile(scope.row)">
															归档
														</el-button>
													</el-dropdown-item>
<!--													<el-dropdown-item>-->
<!--														<el-button icon="Clock" link type="success"-->
<!--																   @click="openHistory()">-->
<!--															操作记录-->
<!--														</el-button>-->
<!--													</el-dropdown-item>-->
												</el-dropdown-menu>
											</template>
										</el-dropdown>
									</div>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:page-sizes="[20,40,60,80]" :total="total" style="padding: 0"
										@pagination="paginationing()"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 原文上传 -->
		<el-dialog v-if="openUpload" v-model="openUpload" :title="title" align-center append-to-body width="800px">
			<fileUpload :handList="handList" @childEvent="parentMethod"/>
		</el-dialog>
		<!-- 新建 -->
		<el-dialog v-if="openSelect" v-model="openSelect" :title="title" align-center append-to-body width="32%">
			<el-divider style="margin: 0;"/>
			<fileSelect style="height: 72vh" @changeTitle="changeTitle" @clickEvenListAdd="clickEvenListAdd"
						@menuPropsList="menuPropsList"/>
		</el-dialog>
		<!-- 添加窗口 -->
		<fileAdd :addOpen="addOpen" :chooseMenu="chooseMenu" :infoAddId="infoAddId" :title="title"
				 @childEvent="closeAdd"/>
		<!-- 编辑窗口 -->
		<archivesEdit :isOpen="openEdit" :receiveInfo="receiveInfo" :title="title" @childMove="parentEdit"/>
		<!-- 移动 -->
		<el-dialog v-if="openMove" v-model="openMove" :title="title" append-to-body width="600px">
			<el-divider style="margin: 0;"/>
			<fileMove :handList="handList" style="height: 72vh" @childMove="parentMove"/>
		</el-dialog>
		<!-- 查看 -->
		<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="8vh" width="92%">
			<viewFiles :receiveId="receiveInfo" @childMove="parentView"/>
		</el-dialog>
		<el-dialog v-model="openControlHistory" align-center append-to-body title="操作记录" width="42%">
			<LogQuery ref="logRef3"/>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import {FullScreen, Refresh} from '@element-plus/icons-vue'
import collectList from '@/api/archive/archiveReception/collect';
import LogQuery from "@/components/detailsForm/logQuery.vue";
import fileUpload from "./fileUpload.vue"
import fileAdd from "./fileAdd.vue"
import fileMove from "./fileMove.vue"
import fileSelect from "./fileSelect.vue"
import archivesEdit from "./archivesEdit.vue"
import moment from 'moment'
// 查看弹窗
import viewFiles from '@/views/archiveReception/view.vue';
import tool from "@/utils/tool";

const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	}
})
const total = ref(0);
const {queryParams} = toRefs(data);
const {proxy} = getCurrentInstance();
// 接收库List
const receiveData = ref([]);
// 点击查询列表
const openTree = ref(true);
const openControlHistory = ref(false);
// 点击树结构的id
const clickEvenId = ref([]);
// 归档状态
const collectFileStatus = ref(false);
const dataLoading = ref(false);
// 查看receiveId
const receiveInfo = ref('')
const openView = ref(false)
// 操作流程
const logRef3 = ref(null)
// 头部查询
const form = ref([])
const handList = ref([])
// 移动
const openMove = ref(false);
const treeChoose = ref(null);
// 编辑
const openEdit = ref(false);
// 原文上传状态
const openUpload = ref(false);
// 新建
const openSelect = ref(false);
const addOpen = ref(false);
const chooseMenu = ref([]);
const infoAddId = ref([]);
// 标题
const title = ref('');

onMounted(() => {
	getList()
});

function clickEven(val) {
	clickEvenId.value = val;
	clickEvenList(val);
}

function collectView(val) {
	title.value = '查看';
	receiveInfo.value = val;
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 保留年限
function reserve(val) {
	if (val == 'Y') {
		return '永久'
	} else if (val == 'D5') {
		return '5年'
	} else if (val == 'D10') {
		return '10年 '
	} else if (val == 'D20') {
		return '20年'
	} else if (val == 'D30') {
		return '30年'
	} else {
		return '暂无'
	}
}

// 控制等级
function control(val) {
	if (val === '1') {
		return '公开'
	} else if (val === '2') {
		return '公司内部开放'
	} else if (val === '3') {
		return '部门内部开放 '
	} else if (val === '4') {
		return '控制'
	} else {
		return '暂无'
	}
}

function handleQuery() {
	dataLoading.value = true;
    getList();
}

// 重置
function resetQuery() {
	form.value = [];
	clickEvenList(clickEvenId.value);
}

// 分页
function paginationing() {
	if (clickEvenId.value.length > 0) {
		clickEvenList(clickEvenId.value);
	} else {
		handleQuery()
	}
}

// 点击树结构查询表格
function clickEvenList(val) {
	dataLoading.value = true;
	treeChoose.value = val;
	if (val.recordGroupName) {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlGroup.id': val.id,
            name:form.value.name,
			archiveStatus: 0,
			delFlag: 0
		}).then(res => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
				total.value = res.data.total;
				openTree.value = true;
				dataLoading.value = false;
			}
		}).catch(() => {
			dataLoading.value = false;
			proxy.msgError('查询失败');
		})
	} else {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlCategory.id': val.id,
            name:form.value.name,
			archiveStatus: 0,
			delFlag: 0
		}).then(res => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
				total.value = res.data.total;
				openTree.value = true;
				dataLoading.value = false;
			}
		}).catch(() => {
			dataLoading.value = false;
			proxy.msgError('查询失败');
		})
	}
}

//查看操作记录
// function openHistory() {
// 	openControlHistory.value = true;
	// basicInfoList.value.forEach(label => {
	// 	if (label.name === '操作记录' || label.name === '供应商审核记录' || label.name.includes('审核记录')) {
	// 		let recordData = label.dataValue ? JSON.parse(label.dataValue) : [];
	// 		recordData.forEach(record => {
	// 			records.push({
	// 				name: record.reviewer,
	// 				updateDate: record.review_time,
	// 				remark: record.remark,
	// 				reviewIdea: record.review_idea
	// 			});
	// 		});
	// 	}
	// });
	// logRef3.value.timeFns(null);
// }

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 归档
function collectFile(val) {
	if (val) {
		collectFileStatus.value = true;
		proxy.$confirm('是否要进行归档?', '提示', {
			type: 'warning',
			confirmButtonText: "确定",
			cancelButtonText: "取消",
		}).then(() => {
			let data = {};
			data.archiveTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
			data.archiveStatus = 1;
			data.infoIds = val.id;
			collectList.infoOperate(data).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess('归档成功');
					clickEvenList(clickEvenId.value);
					collectFileStatus.value = false;
				} else {
					proxy.msgError(res.msg);
					collectFileStatus.value = false;
				}
			}).catch(() => {
				proxy.msgError('归档失败');
				collectFileStatus.value = false;
			})
		}).catch(() => {
			proxy.msgInfo('取消归档');
			collectFileStatus.value = false;
		})
	} else if (handList.value.length > 0) {
		collectFileStatus.value = true;
		proxy.$confirm('是否要进行归档?', '提示', {
			type: 'warning',
			confirmButtonText: "确定",
			cancelButtonText: "取消",
		}).then(() => {
			let idStr = handList.value.map((v) => v.id);
			//拼接的数组字符串，接口传参
			let ids = idStr.join(",");
			let data = {};
			data.archiveTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
			data.archiveStatus = 1;
			data.infoIds = ids;
			collectList.infoOperate(data).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess('归档成功');
					clickEvenList(clickEvenId.value);
				} else {
					proxy.msgError(res.msg);
				}
				collectFileStatus.value = false;
			}).catch(() => {
				proxy.msgError('归档失败');
				collectFileStatus.value = false;
			})
		}).catch(() => {
			proxy.msgInfo('取消归档');
			collectFileStatus.value = false;
		})
	} else {
		proxy.msgError('请先选择归档的数据！');
		collectFileStatus.value = false;
	}
}

function collectEdit(val) {
	openEdit.value = true;
	receiveInfo.value = val;
	title.value = '编辑档案';
}

// 关闭编辑
function parentEdit() {
	openEdit.value = false;
	if (clickEvenId.value.length === 0) {
		getList();
	} else {
		clickEvenList(clickEvenId.value);
	}
}

//关闭新增
function closeAdd() {
	addOpen.value = false;
	getList();
}

// 原文上传
function collectUpload() {
	if (handList.value.length > 0) {
		openUpload.value = true;
		title.value = '原文上传';
	} else {
		proxy.msgError('请先选择上传的数据！');
	}
}

// 上传完成
function parentMethod() {
	openUpload.value = false;
	clickEvenList(clickEvenId.value);
}

function collectAdd() {
	openSelect.value = true;
	title.value = '选择表单';
}

function menuPropsList(value) {
	chooseMenu.value = value;
}

function clickEvenListAdd(value) {
	infoAddId.value = value;
	openSelect.value = false;
	title.value = '新增';
	addOpen.value = true;
}

//切换标题
function changeTitle(newTitle) {
	title.value = newTitle;
}

function collectMove() {
	if (handList.value.length > 0) {
		openMove.value = true;
		title.value = '移动';
	} else {
		proxy.msgError('请先选择移动的数据！');
	}
}

function parentMove() {
	openMove.value = false;
	clickEvenList(clickEvenId.value);
}

// 删除
function collectDelete() {
	if (handList.value.length > 0) {
		var idStr = handList.value.map((v) => v.id);
		//拼接的数组字符串，接口传参
		var ids = idStr.join(",");
		proxy.$confirm('所选档案及相关信息会进入回收站，请确认是否进行删除操作?', '提示', {
			type: 'warning',
			confirmButtonText: "确认",
			cancelButtonText: "取消",
		}).then(() => {
			collectList.delete({ids}).then(res => {
				if (res.code === 200) {
					clickEvenList(clickEvenId.value);
					proxy.msgSuccess('删除成功');
				} else {
					proxy.msgError('删除失败')
				}
			})
		}).catch(() => {
            proxy.msgInfo('取消删除')
		})
	} else {
		proxy.msgError('请先选择删除的数据！');
	}

}

// 进入时查询全部
function getList() {
	dataLoading.value = true;

	let requestData = {
		current: queryParams.value.current,
		size: queryParams.value.size,
		archiveStatus: 0,
		delFlag: 0,
        name:form.value.name
	}

	if (treeChoose.value) {
		if (treeChoose.value.recordGroupName) {
			requestData = {
				'controlGroup.id': treeChoose.value.id,
				...requestData
			}
		} else {
			requestData = {
				'controlCategory.id': treeChoose.value.id,
				...requestData
			}
		}
	}
	collectList.list(requestData).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
			total.value = res.data.total;
			openTree.value = true;
			dataLoading.value = false;
		}
	}).catch(() => {
		dataLoading.value = false;
		proxy.msgError('查询失败');
	})
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}
</script>

<style scoped>

</style>
