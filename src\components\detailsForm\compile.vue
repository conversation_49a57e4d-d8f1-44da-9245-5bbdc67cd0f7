<template>
	<div>
		<el-dialog v-model="dialogVisible" title="查看详情" width="80%">
			<h4
				v-if="data.editStr"
				slot="title"
				class="stateTitle"
			>
				{{ data.editStr == null ? "" : data.editStr.state }}
			</h4>
			<div class="demo-collapse">
				<el-collapse v-model="activeNames" @change="handleChange">
					<el-collapse-item name="1">
						<template #title>
							<h3>基本信息</h3>
						</template>
						<el-form
							:disabled="true"
							:inline="true"
							:model="formInline"
							class="demo-form-inline"
							label-position="right"
							label-width="125px"
							style="overflow-x:auto"
						>
							<div class="formBox">
								<el-form-item label="通用名" prop="n1">
									<el-input
										v-model="formInline.n1"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="商品名" prop="n2">
									<el-input
										v-model="formInline.n2"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="拼音码">
									<el-input
										v-model="formInline.n3"
										:disabled="true"
										placeholder="系统生成字段"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="自编码">
									<el-input
										v-model="formInline.n4"
										:disabled="true"
										placeholder="系统生成字段"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="商品编码" prop="n17">
									<el-input
										v-model="formInline.n17"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="基本单位" prop="n5">
									<el-select
										v-model="formInline.n5"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.basicType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select
									>
								</el-form-item>
								<el-form-item label="整件单位" prop="n6">
									<el-select
										v-model="formInline.n6"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.wholeType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="比率" prop="n7">
									<el-input
										v-model="formInline.n7"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="有效期(月)" prop="n8">
									<el-input
										v-model="formInline.n8"
										min="1"
										style="width: 100%;min-width:100px"
										type="number"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types != '6'"
									label="药品剂型"
									prop="n9"
								>
									<el-select
										v-model="formInline.n9"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.drugType"
											:key="index"
											:label="item.valueName"
											:value="item.valueName"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="包装规格" prop="n10">
									<el-input
										v-model="formInline.n10"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="上市许可持有人">
									<el-input
										v-model="formInline.n11"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="生产厂家" prop="n12">
									<el-select
										v-model="formInline.n12"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>

									</el-select>
								</el-form-item>
								<el-form-item label="生产地址" prop="n13">
									<el-select
										v-model="formInline.n13"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.siteType"
											:key="index"
											:label="item.licenseAddress"
											:value="item.licenseAddress"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="经营范围" prop="n14">
									<el-tree-select
										v-model="formInline.n14"
										:data="data.treesType"
										:props="{ value: 'id', label: 'massName' }"
										:render-after-expand="false"
										node-key="id"
										placeholder=" "
										show-checkbox
										style="width: 100%;min-width:100px"
										value-key="id"
									/>
								</el-form-item>
								<el-form-item label="产地">
									<el-input
										v-model="formInline.n15"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="备注">
									<el-input
										v-model="formInline.n16"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
							</div>
						</el-form>
					</el-collapse-item>
					<el-collapse-item name="2">
						<template #title>
							<h3>质量信息</h3>
						</template>
						<el-form
							:disabled="true"
							:inline="true"
							:model="formInline2"
							class="demo-form-inline"
							label-position="right"
							label-width="125px"
							style="overflow-x:auto"
						>
							<div class="formBox">
								<el-form-item label="GSP属性" prop="n1">
									<el-select
										v-model="formInline2.n1"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.GSPType"
											:key="index"
											:label="item.valueName"
											:value="item.valueName"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="data.types == '7'"
									label="生产许可证号"
									prop="n24"
								>
									<el-input
										v-model="formInline2.n24"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == '7'"
									label="生产许可证号"
									prop="n25"
								>
									<el-date-picker
										v-model="formInline2.n25"
										placeholder=" "
										size="default"
										style="width: 100%;min-width:100px"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="管理类别"
									prop="n15"
								>
									<el-select
										v-model="formInline2.n15"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.manageType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="商品分类"
									prop="n16"
								>
									<el-select
										v-model="formInline2.n16"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.goodsType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="阳光采购码"
								>
									<el-input
										v-model="formInline2.n18"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="质管分类"
									prop="n17"
								>
									<el-select
										v-model="formInline2.n17"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.conType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="data.types == 5"
									label="注册/备案号"
									prop="n24"
								>
									<el-select
										v-model="formInline2.n24"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.ratifyType"
											:key="index"
											:label="item.valueName"
											:value="item.valueName"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="data.types == 5"
									label="注册/备案有限期"
									prop="n25"
								>
									<el-date-picker
										v-model="formInline2.n25"
										placeholder=" "
										size="default"
										style="width: 100%;min-width:100px"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == 4"
									label="批准文号"
									prop="n2"
								>
									<el-select
										v-model="formInline2.n2"
										placeholder=" "
										style="width: 55%;min-width:50px"
									>
										<el-option
											v-for="(item, index) in data.ratifyType"
											:key="index"
											:label="item.valueName"
											:value="item.valueName"
										/>
									</el-select>
									<el-input
										v-model="formInline.n27"
										style="width: 40%;min-width:40px;margin-left: 5%"
									/>
								</el-form-item>

								<el-form-item
									v-if="data.types == 4"
									label="批准文号有限期"
									prop="n3"
								>
									<el-date-picker
										v-model="formInline2.n3"
										placeholder=" "
										size="default"
										style="width: 100%;min-width:100px"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == 4"
									label="处方类型"
									prop="n4"
								>
									<el-select
										v-model="formInline2.n4"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.recipeType"
											:key="index"
											:label="item.valueName"
											:value="item.valueName"
										/>
									</el-select>
								</el-form-item
								>
								<el-form-item
									v-if="data.types == 4"
									label="注册批件号"
									prop="n5"
								>
									<el-input
										v-model="formInline2.n5"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="质量标准" prop="n6">
									<el-input
										v-model="formInline2.n6"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>

								<el-form-item
									v-if="data.types == 4 || data.types == 5"
									label="养护类型"
									prop="n7"
								>
									<el-select
										v-model="formInline2.n7"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(
											item, index
										) in data.maintainType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="注册证号"
									prop="n19"
								>
									<el-input
										v-model="formInline2.n19"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="注册证发证日期"
									prop="n20"
								>
									<el-date-picker
										v-model="formInline2.n20"
										placeholder=" "
										size="default"
										style="width: 100%;min-width:100px"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</el-form-item>
								<el-form-item
									v-if="data.types == '6'"
									label="注册证有限期"
									prop="n21"
								>
									<el-date-picker
										v-model="formInline2.n21"
										placeholder=" "
										size="default"
										style="width: 100%;min-width:100px"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</el-form-item>
								<el-form-item label="贮藏温区" prop="n8">
									<el-select
										v-model="formInline2.n8"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.storeType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="贮藏温区范围">
									<el-select
										v-model="formInline2.n9"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.warmType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="运输温区">
									<el-select
										v-model="formInline2.n10"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.storeType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="运输温度范围">
									<el-select
										v-model="formInline2.n11"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.warmType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>

							</div>
							<el-form-item label=" " label-width="50px">
								<el-checkbox
									v-model="formInline2.n12"
									label="首营品种"
								/>
								<el-checkbox
									v-model="formInline2.n13"
									label="特药控制"
								/>
								<el-checkbox
									v-model="formInline2.n14"
									label="特殊温区"
								/>
								<el-checkbox
									v-model="formInline2.n23"
									label="电子监控"
								/>
							</el-form-item>
						</el-form>
					</el-collapse-item>
					<el-collapse-item name="3">
						<template #title>
							<h3>经营信息</h3>
						</template>
						<el-form
							:default-expand-all="true"
							:disabled="true"
							:inline="true"
							:model="formInline3"
							class="demo-form-inline"
							label-position="right"
							label-width="125px"
							style="overflow-x:auto"
						>
							<div class="formBox">
								<el-form-item label="税率" prop="n1">
									<el-input
										v-model="formInline3.n1"
										style="width: 100%;min-width:100px"
										type="number"
									/>
								</el-form-item>
								<el-form-item label="税务分类编码" prop="n2">
									<el-input
										v-model="formInline3.n2"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="疗程数量">
									<el-input
										v-model="formInline3.n3"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="用药分类">
									<el-input
										v-model="formInline3.n4"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item
								>
								<el-form-item label="使用天数">
									<el-input
										v-model="formInline3.n5"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="医保分类">
									<el-select
										v-model="formInline3.n6"
										placeholder=" "
										style="width: 100%;min-width:100px"
									>
										<el-option
											v-for="(item, index) in data.healthType"
											:key="index"
											:label="item.name"
											:value="item.name"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="用法用量">
									<el-input
										v-model="formInline3.n7"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="禁忌">
									<el-input
										v-model="formInline3.n8"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="主要成分">
									<el-input
										v-model="formInline3.n9"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="特殊存储要求">
									<el-input
										v-model="formInline3.n10"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="功能主治">
									<el-input
										v-model="formInline3.n11"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>
								<el-form-item label="适应征状">
									<el-input
										v-model="formInline3.n12"
										style="width: 100%;min-width:100px"
									/>
								</el-form-item>

							</div>
							<el-form-item label=" " label-width="50px">
								<el-checkbox
									v-model="formInline3.n13"
									label="基本药物"
								/>
								<el-checkbox
									v-model="formInline3.n14"
									label="注册商标"
								/>
								<el-checkbox
									v-model="formInline3.n15"
									label="会员积分"
								/>
								<el-checkbox
									v-model="formInline3.n16"
									label="价格保护"
								/>
								<el-checkbox
									v-model="formInline3.n17"
									label="贵细药品"
								/>
								<el-checkbox
									v-model="formInline3.n18"
									label="网络销售"
								/>
								<el-checkbox
									v-model="formInline3.n19"
									label="独占货位"
								/>
								<el-checkbox
									v-model="formInline3.n20"
									label="是否易碎"
								/>
							</el-form-item>
						</el-form>
					</el-collapse-item>
					<el-collapse-item name="4">
						<template #title>
							<h3>附件上传</h3>
						</template>
						<el-table
							:cell-style="{ textAlign: 'center' }"
							:data="formInline4.table"
							:header-cell-style="{ 'text-align': 'center' }"
							border
							style="width: 100%"
						>
							<el-table-column label="序号" prop="n1">
								<template #default="scope">
									{{ scope.row.n1 + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="编号" prop="n2">
								<template #default="scope">
									<p
										v-show="!scope.row.n2.flag"
										:disabled="data.title == '查看详情'"
										style="width: 100%"
										@click="
											formInline4.table[
												scope.$index
											].n2.flag = true
										"
									>
										{{ scope.row.n2.str }}&emsp;
									</p>

									<el-input
										v-show="scope.row.n2.flag"
										v-model="
											formInline4.table[scope.$index].n2
												.str
										"
										:disabled="data.title == '查看详情'"
										@blur="
											formInline4.table[
												scope.$index
											].n2.flag = false
										"
									/>
								</template>
							</el-table-column>
							<el-table-column label="文件分类" prop="n3">
								<template #default="scope">
									<p style="width: 100%">
										{{ scope.row.n3.str }}
									</p>
								</template>
							</el-table-column>
							<el-table-column label="文件上传名称" prop="n4">
								<template #default="scope">
									<p
										style="
											width: 100%;
											cursor: pointer;
											color: #2a76f8;
										"
										@click="
											() => {
												data.imgUrl = scope.row.n4.url;
												data.checkFlag = true;
											}
										"
									>
										{{ scope.row.n4.str }}
									</p>
								</template>
							</el-table-column>
							<el-table-column
								label="有限期"
								prop="n5"
								width="180px"
							>
								<template #default="scope">
									<el-date-picker
										v-model="
											formInline4.table[scope.$index].n5
										"
										:disabled="true"
										placeholder=" "
										size="small"
										style="width: 140px"
										type="date"
										value-format="YYYY-MM-DD"
									/>
								</template>
							</el-table-column>
							<el-table-column label="备注" prop="n6">
								<template #default="scope">
									<p style="width: 100%">
										{{ scope.row.n6.str }}&emsp;
									</p>
								</template>
							</el-table-column>
						</el-table>
					</el-collapse-item>
					<el-collapse-item name="5">
						<template #title>
							<h3>操作日志</h3>
						</template>
						<LogQuery ref="logQueryRef"/>
					</el-collapse-item>
					<el-collapse-item v-if="!data.formFlag" name="6">
						<template #title>
							<h3>审批意见</h3>
						</template>
						<auditForms ref="auditRef" @refresh="refresh"/>
					</el-collapse-item>
				</el-collapse>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible = false"> 取消 </el-button>
					<el-button v-if="!data.formFlag" type="primary" @click="allRight()">
						确定
					</el-button>
				</span>
			</template>
		</el-dialog>
		<div v-show="data.checkFlag" class="zhe">
			<div class="imgDiv">
				<img :src="data.imgUrl" alt="图片加载失败"/>
				<span @click="data.checkFlag = false"
				><el-icon><Close/></el-icon
				></span>
			</div>
		</div>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import {drugApi} from "@/api/model/commodity/drug";
import {ElLoading} from "element-plus";
import {foodApi} from "@/api/model/commodity/food";
import {applianceApi} from "@/api/model/commodity/appliance";
import {disappApi} from "@/api/model/commodity/disappear";
import LogQuery from "@/components/detailsForm/logQuery.vue";
import {Close} from "@element-plus/icons-vue";
// import {typeList} from "@/views/commodity/indexApi";

const dialogVisible = ref(false);
const activeNames = ref(["1"]);

const auditRef = ref(null);
const logQueryRef = ref(null);
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({
	formFlag: false,
	pageNum: 1,
	treesType: null,
	ids: "",
	title: "",
	types: "",
	checkFlag: false,
	imgUrl: "",
	status: "",
	pageSize: 10,
	state: false,
	editStr: null,
	total: 0,
	basicType: null,
	wholeType: null,
	drugType: null,
	yieldType: null,
	GSPType: null,
	ratifyType: null,
	recipeType: null,
	maintainType: null,
	storeType: null,
	warmType: null,
	healthType: null,
	fileType: null,
	siteType: null,
	manageType: null,
	goodsType: null,
	conType: null,
	venderDrug: {
		pageNum: 1,
		pageSize: 10,
		total: 0,
		records: null,
		value: "",
	},
});
const emit = defineEmits(["refresh"]);
const formInline = reactive({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	n6: "",
	n7: "",
	n8: "",
	n9: "",
	n10: "",
	n11: "",
	n12: "",
	n13: "",
	n14: [],
	n15: "",
	n16: "",
	n17: "",
	n18: "",
	n19: "",
	n20: "",
	n21: "",
	n22: "",
	n23: "",
	n24: "",
	n25: "",
	n26: "",
	n27: "",
});
const formInline2 = reactive({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	n6: "",
	n7: "",
	n8: "",
	n9: "",
	n10: "",
	n11: "",
	n12: "",
	n13: "",
	n14: "",
	n15: "",
	n16: "",
	n17: "",
	n18: "",
	n19: "",
	n20: "",
	n21: "",
	n22: "",
	n23: "",
	n24: "",
	n25: "",
});
const formInline3 = reactive({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	n6: "",
	n7: "",
	n8: "",
	n9: "",
	n10: "",
	n11: "",
	n12: "",
	n13: "",
	n14: "",
	n15: "",
	n16: "",
	n17: "",
	n18: "",
	n19: "",
	n20: "",
	n21: "",
	n22: "",
	n23: "",
	n24: "",
	n25: "",
});
const formInline4 = reactive({
	table: [],
	delAll: [],
});
const allRight = () => {
	auditRef.value.formSub(data.ids);
};
const refresh = () => {
	dialogVisible.value = false;
	emit("refresh");
};
const details = (type, id, rowId, mess) => {
	data.types = type;
	data.ids = rowId;
	if (mess) {
		data.formFlag = true
	} else {
		data.formFlag = false
	}
	ElLoading.service();
	const loadingInstance = ElLoading.service();
	drugApi
		.journalList({
			"commodity.id": id,
		})
		.then((res) => {
			drugApi
				.drugLog({
					masterId: id,
				})
				.then((resg) => {
					setTimeout(() => {
						logQueryRef.value.timeFns(
							res.data.records,
							resg.data.records
						);
					}, 500);
				});
		});
	if (type == 4) {
		drugApi
			.searchDrug({
				id: id,
			})
			.then((res) => {
				if (res.code == 200) {
					let fileArr = [];
					let row = res.data.erpDrugsCommodityDTO.commodity;
					data.editStr = res.data;
					if (row.status == "0") {
						data.editStr.state = `状态: 无效`;
					} else if (row.status == "1") {
						data.editStr.state = `状态: 有效`;
					} else if (row.status == "2") {
						data.editStr.state = `状态: 待审核`;
					} else if (row.status == "3") {
						data.editStr.state = `状态: 审核中`;
					} else if (row.status == "4") {
						data.editStr.state = `状态: 驳回`;
					} else if (row.status == "5") {
						data.editStr.state = `状态: 撤销`;
					} else if (row.status == "6") {
						data.editStr.state = `状态: 草稿`;
					}
					formInline2.n2 =
						res.data.erpDrugsCommodityDTO.approvalNumber;
					formInline2.n4 =
						res.data.erpDrugsCommodityDTO.proscriptionType;
					formInline2.n3 =
						res.data.erpDrugsCommodityDTO.approvalValidity;
					formInline2.n5 =
						res.data.erpDrugsCommodityDTO.registrationNo;

					formInline.n12 = row.manufacture.enterpriseName;
					formInline.n17 = row.commodityCode;
					formInline.n3 = row.pinyinCode;
					formInline.n2 = row.tradeName;
					formInline.n1 = row.commonName;
					formInline2.n16 = row.grugsType;
					formInline.n9 = row.dosageForm;
					formInline.n10 = row.packageSpecification;
					formInline.n8 = row.validityTime;
					formInline.n13 = row.originPlace;
					formInline.n15 = row.producingArea;
					formInline.n11 = row.listPermitHolder;
					formInline.n5 = row.basicUnit;
					formInline2.n1 = row.gspAttribute;
					formInline.n14 = row.businessScope;
					formInline2.n6 = row.qualityStandard;
					formInline2.n13 =
						row.specialMedicineControl == "1" ? true : false;
					formInline2.n12 = row.premiereVariety == "1" ? true : false;
					formInline2.n7 = row.curingType;
					formInline2.n8 = row.storageTemperature;
					formInline2.n9 = row.storageRange;
					formInline2.n10 = row.transportTemperature;
					formInline2.n11 = row.transportTemperatureRange;
					formInline2.n14 =
						row.specialTemperature == "1" ? true : false;
					formInline2.n23 =
						row.electronicSupervision == "1" ? true : false;
					formInline.n16 = row.remark;
					formInline.n4 = row.commoditySelfCode;
					formInline.n6 = row.completeUnit;
					formInline.n7 = row.ratio;
					formInline3.n1 = row.taxRate;
					formInline3.n2 = row.taxClassifyCode;
					formInline3.n3 = row.treatmentNumber;
					formInline3.n4 = row.medicationClassify;
					formInline3.n5 = row.usageDays;
					formInline3.n6 = row.medicalInsuranceClassify;
					formInline3.n7 = row.usageDosage;
					formInline3.n8 = row.taboo;
					formInline3.n9 = row.mainComponents;
					formInline3.n10 = row.specialStorage;
					formInline3.n11 = row.functionalIndications;
					formInline3.n12 = row.adaptiveSymptoms;
					formInline3.n13 =
						row.essentialMedicines == "1" ? true : false;
					formInline3.n14 =
						row.registeredTrademark == "1" ? true : false;
					formInline3.n15 = row.memberPoint == "1" ? true : false;
					formInline3.n17 =
						row.preciousMedicines == "1" ? true : false;
					formInline3.n18 = row.onlineSales == "1" ? true : false;
					formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
					formInline3.n20 = row.isFragile == "1" ? true : false;
					formInline3.n16 = row.priceProtection == "1" ? true : false;
					formInline2.n22 = row.onlyCode == "1" ? true : false; //
					dialogVisible.value = true;

					res.data.fileDTOS.forEach((item, index) => {
						fileArr.push({
							id: item.id,
							n1: index,
							n2: {
								str: item.fileCoder,
								flag: false,
							},
							n3: {
								str: item.commodityType,
								flag: false,
							},
							n4: {
								str: item.fileName,
								flag: true,
								type: item.fileType,
								name: "",
								url: item.fileUrl,
							},
							n5: item.fileEndDate,
							n6: {
								str: item.remark,
								flag: false,
							},
						});
					});
					formInline4.table = fileArr;
				}
				loadingInstance.close();
			});
	} else if (type == 5) {
		foodApi
			.searchDrug({
				id: id,
			})
			.then((res) => {
				if (res.code == 200) {
					let row = res.data.erpFoodCommodityDTO.commodity;
					let fileArr = [];
					data.editStr = res.data;
					if (row.status == "0") {
						data.editStr.state = `状态: 无效`;
					} else if (row.status == "1") {
						data.editStr.state = `状态: 有效`;
					} else if (row.status == "2") {
						data.editStr.state = `状态: 待审核`;
					} else if (row.status == "3") {
						data.editStr.state = `状态: 审核中`;
					} else if (row.status == "4") {
						data.editStr.state = `状态: 驳回`;
					} else if (row.status == "5") {
						data.editStr.state = `状态: 撤销`;
					} else if (row.status == "6") {
						data.editStr.state = `状态: 草稿`;
					}
					formInline2.n24 =
						res.data.erpFoodCommodityDTO.registerLicense;
					formInline2.n25 =
						res.data.erpFoodCommodityDTO.registerLicenseDate;
					formInline.n12 = row.manufacture.enterpriseName;

					formInline2.n26 = row.importMark == "1" ? true : false;
					formInline.n17 = row.commodityCode;
					formInline.n3 = row.pinyinCode;
					formInline.n2 = row.tradeName;
					formInline.n1 = row.commonName;
					formInline2.n16 = row.grugsType;
					formInline.n9 = row.dosageForm;
					formInline.n10 = row.packageSpecification;
					formInline.n8 = row.validityTime;
					formInline.n13 = row.originPlace;
					formInline.n15 = row.producingArea;
					formInline.n11 = row.listPermitHolder;
					formInline.n5 = row.basicUnit;
					formInline2.n1 = row.gspAttribute;
					formInline.n14 = row.businessScope;
					formInline2.n6 = row.qualityStandard;
					formInline2.n13 =
						row.specialMedicineControl == "1" ? true : false;
					formInline2.n12 = row.premiereVariety == "1" ? true : false;
					formInline2.n7 = row.curingType;
					formInline2.n8 = row.storageTemperature;
					formInline2.n9 = row.storageRange;
					formInline2.n10 = row.transportTemperature;
					formInline2.n11 = row.transportTemperatureRange;
					formInline2.n14 =
						row.specialTemperature == "1" ? true : false;
					formInline2.n23 =
						row.electronicSupervision == "1" ? true : false;
					formInline.n16 = row.remark;
					formInline.n4 = row.commoditySelfCode;
					formInline.n6 = row.completeUnit;
					formInline.n7 = row.ratio;
					formInline3.n1 = row.taxRate;
					formInline3.n2 = row.taxClassifyCode;
					formInline3.n3 = row.treatmentNumber;
					formInline3.n4 = row.medicationClassify;
					formInline3.n5 = row.usageDays;
					formInline3.n6 = row.medicalInsuranceClassify;
					formInline3.n7 = row.usageDosage;
					formInline3.n8 = row.taboo;
					formInline3.n9 = row.mainComponents;
					formInline3.n10 = row.specialStorage;
					formInline3.n11 = row.functionalIndications;
					formInline3.n12 = row.adaptiveSymptoms;
					formInline3.n13 =
						row.essentialMedicines == "1" ? true : false;
					formInline3.n14 =
						row.registeredTrademark == "1" ? true : false;
					formInline3.n15 = row.memberPoint == "1" ? true : false;
					formInline3.n17 =
						row.preciousMedicines == "1" ? true : false;
					formInline3.n18 = row.onlineSales == "1" ? true : false;
					formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
					formInline3.n20 = row.isFragile == "1" ? true : false;
					formInline3.n16 = row.priceProtection == "1" ? true : false;
					formInline2.n22 = row.onlyCode == "1" ? true : false; //
					formInline2.n5 = row.registrationNo;
					formInline2.n2 = row.approvalNumber;
					formInline2.n3 = row.approvalValidity;
					formInline2.n4 = row.proscriptionType;
					dialogVisible.value = true;

					res.data.fileDTOS.forEach((item, index) => {
						fileArr.push({
							id: item.id,
							n1: index,
							n2: {
								str: item.fileCoder,
								flag: false,
							},
							n3: {
								str: item.commodityType,
								flag: false,
							},
							n4: {
								str: item.fileName,
								flag: true,
								type: item.fileType,
								name: "",
								url: item.fileUrl,
							},
							n5: item.fileEndDate,
							n6: {
								str: item.remark,
								flag: false,
							},
						});
					});
					formInline4.table = fileArr;
				}
				loadingInstance.close();
			});
	} else if (type == 6) {
		applianceApi
			.searchDrug({
				id: id,
			})
			.then((res) => {
				if (res.code == 200) {
					let row = res.data.erpApparatusCommodityDTO.commodity;
					let fileArr = [];
					data.editStr = res.data;
					if (row.status == "0") {
						data.editStr.state = `状态: 无效`;
					} else if (row.status == "1") {
						data.editStr.state = `状态: 有效`;
					} else if (row.status == "2") {
						data.editStr.state = `状态: 待审核`;
					} else if (row.status == "3") {
						data.editStr.state = `状态: 审核中`;
					} else if (row.status == "4") {
						data.editStr.state = `状态: 驳回`;
					} else if (row.status == "5") {
						data.editStr.state = `状态: 撤销`;
					} else if (row.status == "6") {
						data.editStr.state = `状态: 草稿`;
					}
					formInline2.n19 =
						res.data.erpApparatusCommodityDTO.registrationCode; //
					formInline2.n20 =
						res.data.erpApparatusCommodityDTO.registrationStart; //
					formInline2.n21 =
						res.data.erpApparatusCommodityDTO.registrationOverdue; //
					formInline2.n15 =
						res.data.erpApparatusCommodityDTO.manageFamily; //
					formInline2.n18 =
						res.data.erpApparatusCommodityDTO.sunshinePurchaseCode; //
					formInline2.n17 =
						res.data.erpApparatusCommodityDTO.qualityType;
					formInline.n12 = row.manufacture.enterpriseName;

					formInline.n17 = row.commodityCode;
					formInline.n3 = row.pinyinCode;
					formInline.n2 = row.tradeName;
					formInline.n1 = row.commonName;
					formInline2.n16 = row.grugsType;
					formInline.n9 = row.dosageForm;
					formInline.n10 = row.packageSpecification;
					formInline.n8 = row.validityTime;
					formInline.n13 = row.originPlace;
					formInline.n15 = row.producingArea;
					formInline.n11 = row.listPermitHolder;
					formInline.n5 = row.basicUnit;
					formInline2.n1 = row.gspAttribute;
					formInline.n14 = row.businessScope;
					formInline2.n6 = row.qualityStandard;
					formInline2.n13 =
						row.specialMedicineControl == "1" ? true : false;
					formInline2.n12 = row.premiereVariety == "1" ? true : false;
					formInline2.n7 = row.curingType;
					formInline2.n8 = row.storageTemperature;
					formInline2.n9 = row.storageRange;
					formInline2.n10 = row.transportTemperature;
					formInline2.n11 = row.transportTemperatureRange;
					formInline2.n14 =
						row.specialTemperature == "1" ? true : false;
					formInline2.n23 =
						row.electronicSupervision == "1" ? true : false;
					formInline.n16 = row.remark;
					formInline.n4 = row.commoditySelfCode;
					formInline.n6 = row.completeUnit;
					formInline.n7 = row.ratio;
					formInline3.n1 = row.taxRate;
					formInline3.n2 = row.taxClassifyCode;
					formInline3.n3 = row.treatmentNumber;
					formInline3.n4 = row.medicationClassify;
					formInline3.n5 = row.usageDays;
					formInline3.n6 = row.medicalInsuranceClassify;
					formInline3.n7 = row.usageDosage;
					formInline3.n8 = row.taboo;
					formInline3.n9 = row.mainComponents;
					formInline3.n10 = row.specialStorage;
					formInline3.n11 = row.functionalIndications;
					formInline3.n12 = row.adaptiveSymptoms;
					formInline3.n13 =
						row.essentialMedicines == "1" ? true : false;
					formInline3.n14 =
						row.registeredTrademark == "1" ? true : false;
					formInline3.n15 = row.memberPoint == "1" ? true : false;
					formInline3.n17 =
						row.preciousMedicines == "1" ? true : false;
					formInline3.n18 = row.onlineSales == "1" ? true : false;
					formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
					formInline3.n20 = row.isFragile == "1" ? true : false;
					formInline3.n16 = row.priceProtection == "1" ? true : false;
					formInline2.n22 = row.onlyCode == "1" ? true : false; //
					formInline2.n5 = row.registrationNo;
					formInline2.n2 = row.approvalNumber;
					formInline2.n3 = row.approvalValidity;
					formInline2.n4 = row.proscriptionType;
					dialogVisible.value = true;

					res.data.fileDTOS.forEach((item, index) => {
						fileArr.push({
							id: item.id,
							n1: index,
							n2: {
								str: item.fileCoder,
								flag: false,
							},
							n3: {
								str: item.commodityType,
								flag: false,
							},
							n4: {
								str: item.fileName,
								flag: true,
								type: item.fileType,
								name: "",
								url: item.fileUrl,
							},
							n5: item.fileEndDate,
							n6: {
								str: item.remark,
								flag: false,
							},
						});
					});
					formInline4.table = fileArr;
				}
				loadingInstance.close();
			});
	} else if (type == 7) {
		disappApi
			.searchDrug({
				id: id,
			})
			.then((res) => {
				if (res.code == 200) {
					let row = res.data.erpSterilizedCommodityDTO.commodity;
					let fileArr = [];
					data.editStr = res.data;
					if (row.status == "0") {
						data.editStr.state = `状态: 无效`;
					} else if (row.status == "1") {
						data.editStr.state = `状态: 有效`;
					} else if (row.status == "2") {
						data.editStr.state = `状态: 待审核`;
					} else if (row.status == "3") {
						data.editStr.state = `状态: 审核中`;
					} else if (row.status == "4") {
						data.editStr.state = `状态: 驳回`;
					} else if (row.status == "5") {
						data.editStr.state = `状态: 撤销`;
					} else if (row.status == "6") {
						data.editStr.state = `状态: 草稿`;
					}
					data.status = row.status;

					formInline2.n24 =
						res.data.erpSterilizedCommodityDTO.productionLicense;
					formInline2.n25 =
						res.data.erpSterilizedCommodityDTO.productionLicenseDate;
					formInline.n12 = row.manufacture.enterpriseName;
					dialogVisible.value = true;
					formInline.n17 = row.commodityCode;
					formInline.n3 = row.pinyinCode;
					formInline.n2 = row.tradeName;
					formInline.n1 = row.commonName;
					formInline2.n16 = row.grugsType;
					formInline.n9 = row.dosageForm;
					formInline.n10 = row.packageSpecification;
					formInline.n8 = row.validityTime;
					formInline.n13 = row.originPlace;
					formInline.n15 = row.producingArea;
					formInline.n11 = row.listPermitHolder;
					formInline.n5 = row.basicUnit;
					formInline2.n1 = row.gspAttribute;
					formInline.n14 = row.businessScope;
					formInline2.n6 = row.qualityStandard;
					formInline2.n13 =
						row.specialMedicineControl == "1" ? true : false;
					formInline2.n12 = row.premiereVariety == "1" ? true : false;
					formInline2.n7 = row.curingType;
					formInline2.n8 = row.storageTemperature;
					formInline2.n9 = row.storageRange;
					formInline2.n10 = row.transportTemperature;
					formInline2.n11 = row.transportTemperatureRange;
					formInline2.n14 =
						row.specialTemperature == "1" ? true : false;
					formInline2.n23 =
						row.electronicSupervision == "1" ? true : false;
					formInline.n16 = row.remark;
					formInline.n4 = row.commoditySelfCode;
					formInline.n6 = row.completeUnit;
					formInline.n7 = row.ratio;
					formInline3.n1 = row.taxRate;
					formInline3.n2 = row.taxClassifyCode;
					formInline3.n3 = row.treatmentNumber;
					formInline3.n4 = row.medicationClassify;
					formInline3.n5 = row.usageDays;
					formInline3.n6 = row.medicalInsuranceClassify;
					formInline3.n7 = row.usageDosage;
					formInline3.n8 = row.taboo;
					formInline3.n9 = row.mainComponents;
					formInline3.n10 = row.specialStorage;
					formInline3.n11 = row.functionalIndications;
					formInline3.n12 = row.adaptiveSymptoms;
					formInline3.n13 =
						row.essentialMedicines == "1" ? true : false;
					formInline3.n14 =
						row.registeredTrademark == "1" ? true : false;
					formInline3.n15 = row.memberPoint == "1" ? true : false;
					formInline3.n17 =
						row.preciousMedicines == "1" ? true : false;
					formInline3.n18 = row.onlineSales == "1" ? true : false;
					formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
					formInline3.n20 = row.isFragile == "1" ? true : false;
					formInline3.n16 = row.priceProtection == "1" ? true : false;
					formInline2.n22 = row.onlyCode == "1" ? true : false; //
					formInline2.n5 = row.registrationNo;
					formInline2.n2 = row.approvalNumber;
					formInline2.n3 = row.approvalValidity;
					formInline2.n4 = row.proscriptionType;
					res.data.fileDTOS.forEach((item, index) => {
						fileArr.push({
							id: item.id,
							n1: index,
							n2: {
								str: item.fileCoder,
								flag: false,
							},
							n3: {
								str: item.commodityType,
								flag: false,
							},
							n4: {
								str: item.fileName,
								flag: true,
								type: item.fileType,
								name: "",
								url: item.fileUrl,
							},
							n5: item.fileEndDate,
							n6: {
								str: item.remark,
								flag: false,
							},
						});
					});
					formInline4.table = fileArr;
				}
				loadingInstance.close();
			});
	}
};
const handleChange = (val) => {
	console.log(val);
};
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount'
	// )
	let arr = await typeList()
	for (let key in arr) {
		data[key] = arr[key];
	}
	drugApi.TreesDrug({}).then((res) => {
		data.treesType = res.data
	});
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	details,
});
</script>
<style lang="scss" scoped>
::v-deep .el-input.is-disabled .el-input__wrapper {
	background: none !important;
	box-shadow: none !important;
	color: #000 !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
	-webkit-text-fill-color: #000 !important; //修改输入框文字颜色
}

::v-deep .el-input__suffix-inner {
	display: none !important;
}

.zhe {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.6);

	.imgDiv {
		max-width: 70%;
		max-height: 70%;

		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);

		img {
			width: 100%;
			height: 100%;
			min-width: 200px;
			color: #fff;
		}

		span {
			position: absolute;
			font-size: 25px;
			border-radius: 50%;
			height: 30px;
			width: 30px;
			line-height: 34px;
			text-align: center;
			color: #fff;
			right: -30px;
			top: -5px;
		}
	}
}

.formBox {
	width: 100%;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
}

.stateTitle {
	position: absolute;
	font-size: 15px;
	top: 21px;
	right: 53px;
}
</style>
