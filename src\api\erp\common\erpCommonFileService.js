import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/common/erpCommonFile/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/common/erpCommonFile/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/common/erpCommonFile/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/common/erpCommonFile/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/common/erpCommonFile/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/common/erpCommonFile/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/common/erpCommonFile/import',
      data
    )
  }
}
