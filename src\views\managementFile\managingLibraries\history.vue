<template>
	<el-container v-loading="loading">
		<el-scrollbar height="72vh" style="width: 100%;padding-right: 10px">
			<el-timeline style="margin-right: 22px;">
				<el-timeline-item v-for="(item, index) in receiveData" :index="index" :timestamp="moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')"
								  placement="top">
					<el-card>
						<template #header>
							<div class="headerBox">
								<div>
									{{ item.name }}
								</div>
								<div v-if="item.version === props.recordVersion">
									<el-tag effect="dark" round type="success">
										{{ '此信息' }}
									</el-tag>
								</div>
							</div>
						</template>
						<div class="cardBodyStyle">
							<div>
								<h5>档案号: {{ item.num }}</h5>
								<h5>档案版本: {{ item.version }}</h5>
							</div>
							<div>
								<el-button round style="margin-right: 12px" type="primary"
										   @click="viewArchives(item.id)">
									详情
								</el-button>
							</div>
						</div>
					</el-card>
				</el-timeline-item>
			</el-timeline>
		</el-scrollbar>
	</el-container>

	<!-- 查看 -->
	<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
		<viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
	</el-dialog>
</template>

<script setup>
import {defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import viewFiles from '../view.vue';
import collectApi from '@/api/archive/archiveReception/collect';
import moment from "moment/moment";

const props = defineProps({
	recordId: {
		type: String,
	},
	recordVersion: {
		type: String,
	}
})
const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	},
})
const {proxy} = getCurrentInstance()
const {queryParams} = toRefs(data)
const loading = ref(true)
const total = ref(0)
const receiveData = ref([]);
const receiveId = ref('')
const openView = ref(false)
const title = ref('')

onMounted(() => {
	getList();
});

//查询历史数据
function getList() {
	collectApi.getHistoryByRecordId({
		recordId: props.recordId
	}).then(result => {
		receiveData.value = result.data;
		loading.value = false;
	}).catch(error => {
		loading.value = false;
		proxy.msgError('查询失败');
	});
}

//查看档案信息
function viewArchives(id) {
	title.value = '查看';
	receiveId.value = {
		id: id
	};
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

</script>

<style scoped>
.cardBodyStyle {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: space-between;
	align-items: center;
}

.headerBox {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: flex-start;
	align-items: center;
	gap: 10px;
}
</style>
