import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/supplier/erpSupplierProductionDelegate/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/supplier/erpSupplierProductionDelegate/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegate/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegate/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegate/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegate/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/supplier/erpSupplierProductionDelegate/import',
      data
    )
  }
}
