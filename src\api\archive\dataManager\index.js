import http from "@/utils/request"
//档案数据管理
export default {
	getFieldsByPlatform: function (params) {
		return http.get(
			'/archive/config/dataConfig/details/fieldList',
			params
		)
	},
	getFieldsById: function (params) {
		return http.get(
			'/archive/config/dataConfig/details/queryById',
			params
		)
	},

	getList: function (params) {
		return http.get(
			'/archive/info/dataManager/list',
			params
		)
	},
	versionList: function (params) {
		return http.get(
			'/archive/config/info/versionList',
			params
		)
	},
	getDetailsList: function (inputForm) {
		return http.get(
			'/archive/config/dataConfig/details/list',
			inputForm
		)
	},
	getAllTableList: function (params) {
		return http.get(
			'/archive/info/dataManager/getAllTableList',
			params
		)
	},
	dataConfigList: function (params) {
		return http.get(
			'/archive/config/dataConfig/treeData',
			params
		)
	},
	classifyList: function (params) {
		return http.get(
			'/archive/config/classifyConfig/treeData',
			params
		)
	},
	queryById: function (params) {
		return http.get(
			'/archive/info/dataManager/queryById',
			params
		)
	},
	dataConfigQueryById: function (params) {
		return http.get(
			'/archive/config/dataConfig/queryById',
			params
		)
	},
	save: function (data) {
		return http.post(
			'/archive/info/dataManager/save',
			data
		)
	},
	delete: function (params) {
		return http.delete(
			'/archive/info/dataManager/delete',
			params
		)
	},
}
