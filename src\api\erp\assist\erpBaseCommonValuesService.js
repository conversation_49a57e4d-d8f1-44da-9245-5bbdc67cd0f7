import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/assist/erpBaseCommonValues/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/assist/erpBaseCommonValues/delete',
      ids
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/assist/erpBaseCommonValues/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/assist/erpBaseCommonValues/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/assist/erpBaseCommonValues/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/assist/erpBaseCommonValues/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/assist/erpBaseCommonValues/import',
      data
    )
  }
}
