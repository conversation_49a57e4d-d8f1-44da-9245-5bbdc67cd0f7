import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/procure/erpPurchaseOrder/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/procure/erpPurchaseOrder/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/procure/erpPurchaseOrder/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseOrder/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/procure/erpPurchaseOrder/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseOrder/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/procure/erpPurchaseOrder/import',
      data
    )
  }
}
