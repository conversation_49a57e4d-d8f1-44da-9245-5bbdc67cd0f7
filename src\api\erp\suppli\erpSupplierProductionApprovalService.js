import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/supplier/erpSupplierProductionApproval/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/supplier/erpSupplierProductionApproval/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/supplier/erpSupplierProductionApproval/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionApproval/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/supplier/erpSupplierProductionApproval/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionApproval/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/supplier/erpSupplierProductionApproval/import',
      data
    )
  }
}
