import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/factory/erpManufacturerInformation/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/factory/erpManufacturerInformation/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/factory/erpManufacturerInformation/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerInformation/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/factory/erpManufacturerInformation/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerInformation/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/factory/erpManufacturerInformation/import',
      data
    )
  }
}
