<template>
	<el-collapse v-model="props.chooseCollapse">
		<el-collapse-item
			v-for="(item, index) in descriptionsLabel"
			:key="index"
			:name="index"
		>
			<template #title>
				<el-text tag="b" truncated>
					<el-icon>
						<Expand />
					</el-icon>
					{{ item.config?.remark || "暂无" }}
				</el-text>
			</template>

			<!-- 描述列表模式 -->
			<template v-for="(childItem, childIndex) in item.children">
				<el-descriptions
					v-if="item.config?.showMethod === '1'"
					:key="childIndex"
					:column="4"
					border
					style="margin-bottom: 20px; width: 100%"
				>
					<template v-if="item.children.length > 1" #title>
						<el-divider
							direction="vertical"
							style="border-width: 4px; border-color: #e6e9f0"
						/>
						<el-text>{{ "第" + (childIndex + 1) + "个" }}</el-text>
					</template>

					<el-descriptions-item
						v-for="(deepChildItem, deepIndex) in childItem"
						:key="deepIndex"
						:label="deepChildItem.config?.remark || '暂无'"
						:span="getSpan(deepChildItem.config?.displayMode)"
						align="left"
						label-align="left"
						class-name="labelCustomClass"
						label-class-name="labelCustomClass"
						min-width="12.5%"
					>
						<div
							v-if="deepChildItem.value"
							style="width: 100%; height: 100%"
						>
							<div
								v-if="deepChildItem.config?.type === '1'"
								style="width: 100%; height: 100%"
							>
								<el-popover
									v-if="
										deepChildItem.value &&
										deepChildItem.value.length >= 320
									"
									:content="
										deepChildItem.value
											? deepChildItem.value
											: '无'
									"
									placement="bottom"
									trigger="hover"
									width="60vw"
								>
									<template #reference>
										<el-text line-clamp="6">
											{{
												deepChildItem.value
													? deepChildItem.value
													: "无"
											}}
										</el-text>
									</template>
									<el-scrollbar height="312px">
										{{
											deepChildItem.value
												? deepChildItem.value
												: "无"
										}}
									</el-scrollbar>
								</el-popover>
								<el-text v-else>
									{{
										deepChildItem.value
											? deepChildItem.value
											: "无"
									}}
								</el-text>
							</div>

							<div
								v-if="deepChildItem.config?.type === '2'"
								class="image-container"
							>
								<el-image
									v-for="(imgItem, imgIndex) in disposeImg(
										deepChildItem.value
									)"
									v-if="disposeImg(deepChildItem.value)"
									:key="imgIndex"
									:preview-src-list="srcList"
									:src="imgItem"
									fit="fill"
									class="info-image"
									@click="$emit('open-img', imgItem)"
								>
									<template #error>
										<div class="image-slot">
											<el-text>{{ "无" }}</el-text>
										</div>
									</template>
								</el-image>
								<el-text v-else>{{ "无" }}</el-text>
							</div>

							<div v-if="deepChildItem.config?.type === '3'">
								{{ deepChildItem.value }}
							</div>

							<div v-if="deepChildItem.config?.type === '4'">
								{{
									deepChildItem.value
										? moment(deepChildItem.value).format(
												deepChildItem.config
													?.dataConfigValue ||
													"YYYY-MM-DD"
										  )
										: "无"
								}}
							</div>

							<div v-if="deepChildItem.config?.type === '5'">
								{{ deepChildItem.value }}
							</div>
						</div>
						<el-text v-else>{{ "无" }}</el-text>
					</el-descriptions-item>
				</el-descriptions>
			</template>

			<!-- 表格模式 -->
			<el-table
				v-if="item.config?.showMethod === '2'"
				:data="disposeTableData(item.children, 1)"
				fit
				highlight-current-row
				show-overflow-tooltip
				stripe
			>
				<el-table-column
					align="center"
					label="序号"
					prop="sort"
					width="52"
				>
					<template #default="scope">
						{{ scope.$index + 1 }}
					</template>
				</el-table-column>

				<el-table-column
					v-for="(column, columnIndex) in disposeTableData(
						item.children,
						2
					)"
					:key="columnIndex"
					:label="column"
					:min-width="
						disposeTableData(item.children, 1)[0][column].config
							?.length || 100
					"
					:prop="column"
					align="center"
				>
					<template #default="scope">
						<div v-if="scope.row[column].config?.type === '1'">
							{{
								scope.row[column].value
									? scope.row[column].value
									: "无"
							}}
						</div>

						<div
							v-if="scope.row[column].config?.type === '2'"
							class="image-container"
						>
							<el-image
								v-for="(imgItem, imgIndex) in disposeImg(
									scope.row[column].value
								)"
								v-if="
									checkFileType(scope.row[column].value) ===
										'image' &&
									disposeImg(scope.row[column].value)
								"
								:key="imgIndex"
								:preview-src-list="srcList"
								:src="imgItem"
								fit="fill"
								class="info-image"
								@click="$emit('open-img', imgItem)"
							>
								<template #error>
									<div class="image-slot">
										<el-text>{{ "无" }}</el-text>
									</div>
								</template>
							</el-image>

							<el-button
								v-else-if="
									checkFileType(scope.row[column].value) ===
									'pdf'
								"
								link
								type="primary"
								@click="
									$emit(
										'open-file-box',
										scope.row[column].value
									)
								"
							>
								查看
							</el-button>

							<el-image
								v-else
								style="width: 100px; height: 100px"
							>
								<template #error>
									<div class="image-slot">
										<el-text>{{ "无" }}</el-text>
									</div>
								</template>
							</el-image>
						</div>

						<div v-if="scope.row[column].config?.type === '3'">
							{{ scope.row[column].value || "无" }}
						</div>

						<div v-if="scope.row[column].config?.type === '4'">
							{{
								scope.row[column].value
									? moment(scope.row[column].value).format(
											scope.row[column].config
												?.dataConfigValue ||
												"YYYY-MM-DD"
									  )
									: "无"
							}}
						</div>

						<div v-if="scope.row[column].config?.type === '5'">
							{{ scope.row[column].value || "无" }}
						</div>
					</template>
				</el-table-column>
			</el-table>
		</el-collapse-item>
	</el-collapse>
</template>

<script setup>
import moment from "moment/moment";

const props = defineProps({
	descriptionsLabel: {
		type: Array,
		default: () => [],
	},
	chooseCollapse: {
		type: Array,
		default: () => [],
	},
	srcList: {
		type: Array,
		default: () => [],
	},
});

defineEmits(["open-img", "open-file-box"]);

// 获取列跨度
const getSpan = (displayMode) => {
	switch (displayMode) {
		case "1":
			return 1;
		case "2":
			return 2;
		case "3":
			return 4;
		default:
			return null;
	}
};

// 处理表格数据
const disposeTableData = (list, type) => {
	let newList = type === 1 ? [] : new Set();

	if (!Array.isArray(list)) {
		return newList;
	}

	list.forEach((data) => {
		if (type === 1) {
			let newObj = [];
			data.forEach((item) => {
				newObj[item.name] = {
					value: item.value,
					config: item.config,
				};
			});
			newList.push(newObj);
		} else if (type === 2) {
			data.forEach((item) => {
				newList.add(item.name);
			});
		}
	});
	return newList;
};

// 处理图片数据
const disposeImg = (imgObject) => {
	let imgList = [];

	if (typeof imgObject === "string") {
		if (imgObject.includes("[")) {
			let parseArray = JSON.parse(imgObject);
			if (Array.isArray(parseArray)) {
				parseArray.forEach((item) => {
					for (const key in item) {
						if (key.toLowerCase().includes("url")) {
							imgList.push(item[key]);
						}
					}
				});
			}
		} else if (imgObject.includes("{")) {
			let parseObj = JSON.parse(imgObject);
			for (const key in parseObj) {
				if (key.toLowerCase().includes("url")) {
					imgList.push(parseObj[key]);
				}
			}
		} else {
			imgList.push(imgObject);
		}
	} else {
		return null;
	}

	return imgList;
};

// 检测文件类型
const checkFileType = (fileStrUrl) => {
	const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
	const pdfExtensions = ["pdf"];

	if (fileStrUrl) {
		let returnData = "";
		imageExtensions.forEach((extension) => {
			if (fileStrUrl.toLowerCase().includes(extension)) {
				returnData = "image";
			}
		});
		pdfExtensions.forEach((extension) => {
			if (fileStrUrl.toLowerCase().includes(extension)) {
				returnData = "pdf";
			}
		});
		return returnData;
	} else {
		return "other";
	}
};
</script>

<style scoped>
.image-container {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	column-gap: 10px;
}

.info-image {
	width: 100px;
	height: 100px;
	border: 1px solid var(--el-border-color);
	border-radius: var(--el-border-radius-base);
	box-shadow: var(--el-box-shadow-lighter);
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
}
</style>
