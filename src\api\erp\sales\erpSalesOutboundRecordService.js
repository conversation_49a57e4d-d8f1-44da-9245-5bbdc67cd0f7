import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/sales/erpSalesOutboundRecord/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/sales/erpSalesOutboundRecord/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/sales/erpSalesOutboundRecord/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/sales/erpSalesOutboundRecord/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/sales/erpSalesOutboundRecord/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/sales/erpSalesOutboundRecord/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/sales/erpSalesOutboundRecord/import',
      data
    )
  }
}
