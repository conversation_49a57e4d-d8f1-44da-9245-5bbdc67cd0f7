import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/supplier/erpSupplierProductionQuality/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/supplier/erpSupplierProductionQuality/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/supplier/erpSupplierProductionQuality/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionQuality/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/supplier/erpSupplierProductionQuality/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionQuality/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/supplier/erpSupplierProductionQuality/import',
      data
    )
  }
}
