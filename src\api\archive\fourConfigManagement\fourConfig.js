import http from "@/utils/request"
//系统配置四性检测管理配置
export default  {
  list: function (params) {
    return http.get(
       '/archive/config/fourConfig/list',
       params
    )
  },
  save: function (data) {
    return http.post(
       '/archive/config/fourConfig/save',
       data
    )
  },
  delete: function (params) {
    return http.delete(
       '/archive/config/fourConfig/delete',
       params
    )
  },
  template: function (params) {
    return http.get('/archive/config/fourConfig/import/template',params,{responseType: 'blob'},true)
  },
  export: function (params) {
    return http.get(
       '/archive/config/fourConfig/export',
       params,{responseType: 'blob'},true
    )
  },
}
