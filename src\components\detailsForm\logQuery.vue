<template>
	<div class="logQuery">
		<el-empty v-if="data.list.length == 0" description="暂无数据" />
		<el-timeline v-else>
			<el-timeline-item v-for="(activity, index) in data.list" :key="index" :hollow="true" type="primary">
				<div v-if="activity.type == 2" class="times" style="
						font-size: 12px;
						overflow: hidden;
						width: 200px;
					">
					<p>{{ activity.name }}</p>
					<p>{{ transformTimestamp(activity.updateDate) }}</p>
					<span>{{ activity.remark }}</span>
					<p>{{ activity.reviewIdea }}</p>
				</div>

				<div v-if="activity.type == 1" class="times" style="
						position: absolute;
						overflow: hidden;
						width: 200px;
						left: -220px;
						font-size: 12px;
						text-align: right;
					">
					<div>
						<p>
							{{ transformTimestamp(activity.updateDate) }}
							{{ activity.createBy.name }}
						</p>
						<p>
							{{ echo(activity.operateType) }} {{ activity.serviceType }}
							<span v-if="activity.operateType == 'upd'"
								  style="color: #2A76F8;cursor: pointer"
								  @click="detailLog(activity.operateBody)">查看详情
							</span>
						</p>
					</div>
				</div>
			</el-timeline-item>
		</el-timeline>
	</div>
	<div>
		<el-dialog v-model="dialogTableVisible" title="修改详情">
			<el-table :data="gridData" style="margin-bottom: 20px">
				<el-table-column align="center" label="字段名" property="name" />
				<el-table-column align="center" label="修改前" property="oldValue" />
				<el-table-column align="center" label="修改后" property="newValue" />
			</el-table>
		</el-dialog>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect} from "vue";

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
function dateData(property, bol) {
	return function (a, b) {
		var value1 = a[property];
		var value2 = b[property];
		if (bol) {
			if (value1 == value2) {
				return Date.parse(value2) - (Date.parse(value1) + 1);
			}
			// 升序
			return Date.parse(value1) - Date.parse(value2);
		} else {
			// 降序
			return Date.parse(value2) - Date.parse(value1);
		}
	};
}

const gridData = ref([])

const detailLog = (row) => {
	gridData.value = []
	gridData.value = row
	dialogTableVisible.value = true
}
const dialogTableVisible = ref(false)
const echo = (str) => {
	if (str == 'ins') {
		return '新增'
	} else if (str == 'upd') {
		return '修改'
	} else if (str == 'del') {
		return '删除'
	}
}
const timeFns = (arr1, arr2) => {
	arr1?.forEach((item) => {
		item.type = 2;
		item.updateDate = transformTimestamps(item.updateDate);
	});
	arr2?.forEach((item) => {
		item.type = 1;
		item.operateBody = JSON.parse(item.operateBody)
		item.updateDate = transformTimestamps(item.updateDate);
	});
	arr2 ? (data.list = [...arr1, ...arr2]) : (data.list = arr1);
	data.list.sort(dateData("updateDate", true));
};

function transformTimestamp(timestamp) {
	let a = new Date(timestamp).getTime();
	const date = new Date(a);
	const Y = date.getFullYear() + "-";
	const M =
		(date.getMonth() + 1 < 10
			? "0" + (date.getMonth() + 1)
			: date.getMonth() + 1) + "-";
	const D =
		(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
	// const h =
	// 	(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
	// const m =
	// 	date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
	// const s = date.getSeconds(); // 秒
	const dateString = Y + M + D;
	// console.log('dateString', dateString); // > dateString 2021-07-06 14:23
	return dateString;
}

function transformTimestamps(timestamp) {
	let a = new Date(timestamp).getTime();
	const date = new Date(a);
	const Y = date.getFullYear() + "-";
	const M =
		(date.getMonth() + 1 < 10
			? "0" + (date.getMonth() + 1)
			: date.getMonth() + 1) + "-";
	const D =
		(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
	const h =
		(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
	const m =
		date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
	// const s = date.getSeconds(); // 秒
	const dateString = Y + M + D + h + m;
	// console.log('dateString', dateString); // > dateString 2021-07-06 14:23
	return dateString;
}

const data = reactive({
	list: [],
});
onBeforeMount(() => {
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	timeFns,
	data,
});
</script>
<style lang="scss" scoped>
.logQuery {
	display: flex;
	justify-content: center;
	width: 100%;
	max-height: 520px;
	overflow: auto;

	.el-timeline {
		transform: translate(49%, 0);
	}
}

p {
	width: 99%;
	word-break: break-all;
}
span {
	width: 99%;
	word-break: break-all;
}

.el-timeline-item {
	height: 100px;
	margin-top: -10px;
}

.el-timeline-item:nth-child(1) {
	margin-top: 0;
}

.times {
	font-size: 12px;
	height: 80px;

}

.times:hover {
	overflow: auto !important;
}
</style>
