<!--
 * @Descripttion: 处理iframe持久化，涉及store(VUEX)
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2021年6月30日13:20:41
 * @LastEditors:
 * @LastEditTime:
-->

<template>
	<div v-show="$route.meta.type === 'iframe'" class="iframe-pages">
		<iframe v-for="item in iframeList" :key="item.meta.url" :src="$route.query.iframeUrl ? rootURL + $route.query.iframeUrl : item.meta.url" frameborder='0'></iframe>
	</div>
</template>

<script>
import tool from '@/utils/tool';

export default {
	data() {
		return {
			rootURL: process.env.VUE_APP_API
		}
	},
	watch: {
		$route(e) {
			this.push(e)
			this.initToken();
		},
	},
	created() {
		this.push(this.$route);
	},
	computed: {
		iframeList() {
			return this.$store.state.iframe.iframeList
		},
		ismobile() {
			return this.$store.state.global.ismobile
		},
		layoutTags() {
			return this.$store.state.global.layoutTags
		}
	},
	mounted() {
		console.log(this.$route.meta.url);
		this.initToken();
	},
	methods: {
		initToken() {
			this.iframeList.forEach(item => {
				console.log(item)
				if (item.fullPath.indexOf("designer") > -1) {
					item.meta.url = item.fullPath + "?queryParam=" + tool.cookie.get("TOKEN");
					console.log(item.meta.url);
				}
			});
		},
		push(route) {
			if (route.meta.type === 'iframe') {
				if (this.ismobile || !this.layoutTags) {
					this.$store.commit("setIframeList", route)
				} else {
					this.$store.commit("pushIframeList", route)
				}
			} else {
				if (this.ismobile || !this.layoutTags) {
					this.$store.commit("clearIframeList")
				}
			}
		}
	}
}
</script>

<style scoped>
.iframe-pages {
	width: 100%;
	height: 100%;
	background: #fff;
}

iframe {
	border: 0;
	width: 100%;
	height: 100%;
	display: block;
}
</style>
