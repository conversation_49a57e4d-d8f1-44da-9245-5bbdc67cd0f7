<template>
	<el-form ref="loginForm"
			 :model="form"
			 :rules="rules"
			 hide-required-asterisk
			 label-position="top"
			 label-width="0"
			 size="large"
			 @keyup.enter="login">
		<el-form-item label="手机号" prop="user">
			<el-input v-model="form.user" clearable placeholder="请输入手机号"/>
		</el-form-item>
		<el-form-item prop="password" label="验证码">
			<div class="verification-code-container">
				<el-input v-model="form.password" clearable placeholder="请输入验证码" />
				<el-button :disabled="disabled || !form.user" class="verification-code-button" @click="getYzm">
					<el-text v-if="disabled" size="small" type="info">{{ this.$t("login.smsGet") }} ({{ time }})</el-text>
					<el-text v-else size="small">{{ this.$t("login.smsGet") }}</el-text>
				</el-button>
			</div>
		</el-form-item>
		<el-form-item>
			<el-button :loading="islogin" class="submit-button" type="primary" @click="submitLogin">
				{{ $t("login.signIn") }}
			</el-button>
		</el-form-item>
	</el-form>
</template>

<script>
export default {
	props: {
		handlerReset: Function,
	},
	data() {
		// 手机号验证规则
		const validatePhone = (rule, value, callback) => {
			if (!value) {
				callback(new Error('请输入手机号'));
			} else if (!/^1[3456789]\d{9}$/.test(value)) {
				callback(new Error('请输入正确的手机号'));
			} else {
				callback();
			}
		};
		
		return {
			form: {
				user: "",
				password: "",
				autologin: false,
			},
			rules: {
				user: [
					{
						required: true,
						message: "请输入手机号",
						trigger: "blur",
					},
					{
						validator: validatePhone,
						trigger: "blur",
					}
				],
				password: [
					{
						required: true,
						message: "请输入验证码",
						trigger: "blur",
					},
				],
			},
			//登录按钮锁
			islogin: false,
			orgList: [],
			disabled: false,
			time: 0,
			timer: null,
		};
	},
	watch: {},
	mounted() { },
	beforeDestroy() {
		// 组件销毁前清除计时器
		if (this.timer) {
			clearInterval(this.timer);
			this.timer = null;
		}
	},
	methods: {
		async getYzm() {
			// 先验证手机号是否有效
			try {
				await this.$refs.loginForm.validateField("user");
				
				// 手机号验证通过，发送验证码
				this.$message.success(this.$t("login.smsSent"));
				this.disabled = true;
				this.time = 60;
				
				// 保存计时器引用以便销毁时清除
				if (this.timer) {
					clearInterval(this.timer);
				}
				
				this.timer = setInterval(() => {
					this.time -= 1;
					if (this.time < 1) {
						clearInterval(this.timer);
						this.timer = null;
						this.disabled = false;
						this.time = 0;
					}
				}, 1000);
			} catch (error) {
				// 手机号验证失败
				return false;
			}
		},
		/*
		 * 提交登录事件
		 * @author: 路正宁
		 * @date: 2023-03-21 11:17:03
		 */
		async submitLogin() {
			//表单校验
			try {
				await this.$refs.loginForm.validate();
				
				//登录请求体
				let loginBody = {
					username: this.form.user,
					//password: this.$TOOL.crypto.MD5(this.form.password)
					password: this.form.password,
				};
				//锁定登录按钮
				this.islogin = true;
				//执行登录
				let loginUser = await this.login(loginBody);
				if (this.$ObjectUtils.isEmpty(loginUser)) {
					//释放登录按钮
					this.islogin = false;
					return false;
				}
				//获取系统菜单
				await this.getMenuTree();
				this.$message.success("登录成功");
				//释放登录按钮
				this.islogin = false;
				//获取菜单
				this.$router.replace({
					path: "/home",
				});
			} catch (error) {
				this.islogin = false;
				return false;
			}
		},
		/*
		 *@functionName: 登录接口
		 *@params1: loginBody:登录参数
		 *@author: 路正宁
		 *@date: 2023-03-21 15:14:51
		 */
		async login(loginBody) {
			//执行登录
			let loginUser = await this.$API.auth.login(loginBody);
			//校验对象是否为空
			if (this.$ObjectUtils.isEmpty(loginUser)) {
				this.$MessageBox.errorNotie("登录失败，请重试");
				return null;
			}
			//校验状态码是否成功
			if (loginUser.code == 200) {
				//登录成功处理
				//保存token到cookie中
				this.$TOOL.cookie.set("TOKEN", loginUser.data.token, {
					expires: this.form.autologin ? 24 * 60 * 60 : 0,
				});
				//保存用户信息到cookie中
				this.$TOOL.data.set("USER_INFO", loginUser.data.userInfo);
				this.$TOOL.data.set("Organization", loginUser.data.orgList);
				this.$TOOL.data.set("ROLE_LIST", loginUser.data.roleList);
				// if (loginUser?.data?.orgList.length) {
				//   let res = await this.$API.system.menuTreeByOrg({ orgId: loginUser?.data?.orgList[0]?.id })
				//   this.$TOOL.data.set("USER_INFO", res);
				//   this.$TOOL.data.set("Organization", loginUser.data.orgList)
				// }

				return loginUser;
			} else {
				//登录失败处理
				this.$Response.errorNotice(loginUser, "登录失败，请重试");
				return null;
			}
		},
		/*
		 * 获取系统菜单
		 * @author: 路正宁
		 * @date: 2023-04-07 10:51:24
		 */
		async getMenuTree() {
			const orgList = this.$TOOL.data.get("Organization");
			if (orgList?.length) {
				let response = await this.$API.system.menuTreeByOrg({
					orgId: orgList[0]?.id,
				});
				let menuTree = this.sysMenuToUiTree(response.data);
				let permissions = this.getPermissions(response.data);
				//菜单数据写入本地
				this.$TOOL.data.set("MENU", menuTree);
				//权限标识写入本地
				this.$TOOL.data.set("PERMISSIONS", permissions);
				this.$TOOL.data.set("orgKey", 0);
				return;
			}
			let res = await this.$API.system.menuTree();
			if (res.code != 200) {
				this.$Response.errorNotice(res, "当前用户无任何菜单权限，请联系系统管理员");
				return false;
			}
			let menuTrees = this.sysMenuToUiTree(res.data);
			let permissionss = this.getPermissions(res.data);
			//菜单数据写入本地
			this.$TOOL.data.set("MENU", menuTrees);
			//权限标识写入本地
			this.$TOOL.data.set("PERMISSIONS", permissionss);
			return true;
		},

		/*
		 * 菜单数据转树UI数据结构
		 * @author: 路正宁
		 * @date: 2023-04-07 11:20:31
		 */
		sysMenuToUiTree(sysMenu) {
			let menuTree = [];
			for (let i = 0; i < sysMenu.length; i++) {
				menuTree[i] = {
					name: sysMenu[i].alias,
					path: sysMenu[i].path,
					meta: {
						title: sysMenu[i].name,
						icon: sysMenu[i].logo,
						type: sysMenu[i].type,
						hidden: sysMenu[i].hide,
						color: sysMenu[i].affix,
						fullpage: sysMenu[i].wholePageRoute,
					},
					component: sysMenu[i].view,
				};
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					menuTree[i].children = this.sysMenuToUiTree(sysMenu[i].children);
				}
			}
			return menuTree;
		},
		/*
		 *提取权限标识
		 * @author: 路正宁
		 * @date: 2023-04-07 15:21:52
		 */
		getPermissions(sysMenu) {
			let permissions = [];
			for (let i = 0; i < sysMenu.length; i++) {
				if (this.$ObjectUtils.isNotEmpty(sysMenu[i].permission)) {
					permissions.push(sysMenu[i].permission);
				}
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					let pe = this.getPermissions(sysMenu[i].children);
					for (let j = 0; j < pe.length; j++) {
						if (this.$ObjectUtils.isNotEmpty(pe[j])) {
							permissions.push(pe[j]);
						}
					}
				}
			}
			return permissions;
		},
		handlerChange() {
			this.handlerReset();
		},
	},
};
</script>

<style scoped>
.el-form {
	margin-top: 8px;
}

.el-form-item {
	margin-bottom: 24px;
}

:deep(.el-form-item__label) {
	padding-bottom: 8px;
	font-size: 14px;
	color: #606A87;
	line-height: 1;
}

:deep(.el-input__wrapper) {
	padding: 0 12px;
	box-shadow: 0 0 0 1px #DCDFE6 inset;
	border-radius: 4px;
	transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
	box-shadow: 0 0 0 1px #C0C4CC inset;
}

:deep(.el-input__wrapper.is-focus) {
	box-shadow: 0 0 0 1px #1664FF inset;
}

.verification-code-container {
	display: flex;
	gap: 12px;
}

.verification-code-container .el-input {
	flex: 1;
}

.verification-code-button {
	min-width: 120px;
	border-radius: 4px;
	border-color: #DCDFE6;
	transition: all 0.2s;
}

.verification-code-button:hover:not(:disabled) {
	border-color: #1664FF;
	color: #1664FF;
}

.submit-button {
	width: 100%;
	height: 44px;
	font-size: 16px;
	border-radius: 4px;
	margin-top: 8px;
	background-color: #1664FF;
	border-color: #1664FF;
	transition: all 0.3s;
}

.submit-button:hover {
	background-color: #4285F4;
	border-color: #4285F4;
}
</style>
