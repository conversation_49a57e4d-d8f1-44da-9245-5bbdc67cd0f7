<template>
	<link href="viewer.properties" rel="resource" type="application/l10n">
	<vue-pdf-app
		:config="configOther"
		:file-name="props.fileName"
		:page-scale="props.pageScale"
		:pdf="props.src"
		:style="`width: ${viewerWidth}; height: ${viewerHeight};`"
		:theme="props.theme"
		@pages-rendered="pagesRendered"
		@open="onOpen"
		@after-created="onAfterCreated">
		<!--		<template #toolbar-middle-append>-->
		<!--			<div class="splitToolbarButton">-->
		<!--				<button :id="idConfig.pageRotateCcw"-->
		<!--						class="toolbarButton rotateCcw vue-pdf-app-icon rotate-counter-clockwise"-->
		<!--						title="逆时针旋转">-->
		<!--				</button>-->
		<!--				<div class="splitToolbarButtonSeparator"></div>-->
		<!--				<button :id="idConfig.pageRotateCw"-->
		<!--						class="toolbarButton rotateCw vue-pdf-app-icon rotate-clockwise"-->
		<!--						title="顺时针旋转">-->
		<!--				</button>-->
		<!--			</div>-->
		<!--		</template>-->
	</vue-pdf-app>
</template>
<script setup>
import {computed, defineProps, onMounted, ref, watch} from "vue";
import VuePdfApp from 'vue3-pdf-app'
import 'vue3-pdf-app/dist/icons/main.css'

const props = defineProps({
	src: {
		type: String,
		default: ""
	},
	width: {
		type: String,
		default: ""
	},
	height: {
		type: String,
		default: ""
	},
	pageScale: {
		type: String,
		default: ""
	},
	theme: {
		type: String,
		default: ""
	},
	fileName: {
		type: String,
		default: ""
	},
})
const emit = defineEmits(['loaded', 'error'])

// 添加超时检测
let loadingTimeout = null
let isLoaded = false
const configOther = ref({
	sidebar: {
		viewThumbnail: true,
		viewOutline: true,
		viewAttachments: true,
	},
	secondaryToolbar: {
		secondaryPresentationMode: true,
		secondaryOpenFile: true,
		secondaryPrint: true,
		secondaryDownload: true,
		secondaryViewBookmark: true,
		firstPage: true,
		lastPage: true,
		pageRotateCw: true,
		pageRotateCcw: true,
		cursorSelectTool: true,
		cursorHandTool: true,
		scrollVertical: true,
		scrollHorizontal: true,
		scrollWrapped: true,
		spreadNone: true,
		spreadOdd: true,
		spreadEven: true,
		documentProperties: false,
	},
	toolbar: {
		toolbarViewerLeft: {
			findbar: false,
			previous: true,
			next: true,
			pageNumber: true,
		},
		toolbarViewerRight: {
			presentationMode: true,
			openFile: false,
			print: false,
			download: false,
			viewBookmark: false,
		},
		toolbarViewerMiddle: {
			zoomOut: true,
			zoomIn: true,
			scaleSelectContainer: true,
		},
	},
	errorWrapper: true,
});

const idConfig = ref({
	pageRotateCw: "pageRotateCw",
	pageRotateCcw: "pageRotateCcw"
});

onMounted(() => {
	console.log(props, 'props');
});

const viewerWidth = computed(() => {
	if (typeof props.width === 'number') {
		return props.width + 'px';
	} else {
		return props.width;
	}
});

const viewerHeight = computed(() => {
	if (typeof props.height === 'number') {
		return props.height + 'px';
	} else {
		return props.height;
	}
});

// 监听src变化，开始超时检测
watch(() => props.src, (newSrc, oldSrc) => {
	console.log('=== PDFViewer src变化 ===');
	console.log('旧URL:', oldSrc);
	console.log('新URL:', newSrc);

	if (newSrc) {
		console.log('开始加载PDF:', newSrc);
		isLoaded = false;
		// 清除之前的超时
		if (loadingTimeout) {
			clearTimeout(loadingTimeout);
			console.log('清除之前的超时');
		}
		// 设置5秒超时 - 给PDF文件更多加载时间
		loadingTimeout = setTimeout(() => {
			if (!isLoaded) {
				console.error('PDF加载超时，URL:', newSrc);
				console.error('可能原因：1.网络问题 2.文件被阻止 3.URL无效 4.服务器问题');
				emit('error', new Error('PDF加载超时，可能是网络问题或文件被阻止'));
			}
		}, 5000);
		console.log('设置5秒超时检测');
	} else {
		console.log('URL为空，清除超时');
		if (loadingTimeout) {
			clearTimeout(loadingTimeout);
			loadingTimeout = null;
		}
	}
});

function onAfterCreated(pdfApp) {
	console.log('PDF应用创建完成:', pdfApp);

	// 监听PDF.js的错误事件
	if (pdfApp && pdfApp.eventBus) {
		// 监听文档错误
		pdfApp.eventBus.on('documenterror', (evt) => {
			console.error('PDF文档错误事件:', evt);
			isLoaded = true; // 防止超时再次触发
			if (loadingTimeout) {
				clearTimeout(loadingTimeout);
				loadingTimeout = null;
			}
			emit('error', new Error('PDF文档加载失败: ' + (evt.reason || evt.message || '未知错误')));
		});

		// 监听页面错误
		pdfApp.eventBus.on('pageerror', (evt) => {
			console.error('PDF页面错误:', evt);
			isLoaded = true;
			if (loadingTimeout) {
				clearTimeout(loadingTimeout);
				loadingTimeout = null;
			}
			emit('error', new Error('PDF页面加载失败'));
		});
	}
}

function onOpen(pdfApp) {
	console.log('PDF文档打开:', pdfApp);
	// PDF成功打开，清除超时
	if (loadingTimeout) {
		clearTimeout(loadingTimeout);
		loadingTimeout = null;
	}
}

function pagesRendered(pdfApp) {
	setTimeout(() => {
		pdfApp.pdfViewer.currentScaleValue = "page-fit";
		pdfApp.pdfViewer._currentScaleValue = "page-fit";
		console.log('页面呈现完成, pdfApp:', pdfApp);
		isLoaded = true;
		// 清除超时
		if (loadingTimeout) {
			clearTimeout(loadingTimeout);
			loadingTimeout = null;
		}
		emit('loaded', pdfApp);
	});
}
</script>

<style scoped>
@themeColor: #1677FF;

.pdf-app.light {
	--pdf-app-background-color: #D4D4D7;
	--pdf-sidebar-content-color: rgb(245, 245, 245);
	--pdf-toolbar-sidebar-color: rgb(190, 190, 190);
	--pdf-toolbar-color: rgb(225, 225, 225);
	--pdf-loading-bar-color: #3f4b5b;
	--pdf-loading-bar-secondary-color: #666;
	--pdf-find-results-count-color: #3f4b5b;
	--pdf-find-results-count-font-color: hsla(0, 0%, 100%, .87);
	--pdf-find-message-font-color: hsla(0, 0%, 100%, .87);
	--pdf-not-found-color: brown;
	--pdf-split-toolbar-button-separator-color: #000;
	--pdf-toolbar-font-color: rgb(142, 142, 142);
	--pdf-button-hover-font-color: #666;
	--pdf-button-toggled-color: #3f4b5b;
	--pdf-horizontal-toolbar-separator-color: #000;
	--pdf-input-color: #3f4b5b;
	--pdf-input-font-color: #d9d9d9;
	--pdf-find-input-placeholder-font-color: #666;
	--pdf-thumbnail-selection-ring-color: hsla(208, 7%, 46%, .7);
	--pdf-thumbnail-selection-ring-selected-color: #3f4b5b;
	--pdf-error-wrapper-color: #f55;
	--pdf-error-more-info-color: #d9d9d9;
	--pdf-error-more-info-font-color: #000;
	--pdf-overlay-container-color: hsla(208, 7%, 46%, .7);
	--pdf-overlay-container-dialog-color: #6c757d;
	--pdf-overlay-container-dialog-font-color: #d9d9d9;
	--pdf-overlay-container-dialog-separator-color: #000;
	--pdf-dialog-button-font-color: #d9d9d9;
	--pdf-dialog-button-color: #3f4b5b;
	:deep(.thumbnail.selected>.thumbnailSelectionRing) {
		background-color: rgb(105, 105, 105);
	}
}

:deep(.vue-pdf-app-icon::before, .vue-pdf-app-icon::after) {
	font-family: "pdf";
	font-size: medium !important;
	display: inline;
	text-decoration: inherit;
	text-align: center;
	font-variant: normal;
	text-transform: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

:deep(.vue-pdf-app-icon:after, .vue-pdf-app-icon:before) {
	font-family: "pdf";
	font-size: medium !important;
	display: inline;
	text-decoration: inherit;
	text-align: center;
	font-variant: normal;
	text-transform: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

:deep(#thumbnailView) {
	position: absolute;
	width: 100% !important;
	top: 0;
	bottom: 0;
	padding: 10px 30px 0;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
}

:deep(#outlineView) {
	position: absolute;
	width: 100% !important;
	top: 0;
	bottom: 0;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}

:deep(.thumbnailSelectionRing) {
	display: flex;
	width: 100% !important;
	height: 100% !important;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
}

:deep(#thumbnailView) {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex-wrap: nowrap;
}

:deep(.pdfViewer .page) {
	margin: 6px auto 6px auto !important;
	border: none !important;
	background-image: none !important;
}

:deep(.spread .page) {
	margin: 6px 6px 6px 6px !important;
	border: none !important;
	background-image: none !important;
}
</style>
