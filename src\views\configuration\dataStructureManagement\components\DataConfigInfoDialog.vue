<template>
	<el-dialog v-if="props.open" v-model="props.open" title="数据结构详情" align-center width="92%"
			   @close="emit('close', '')" @opened="openDialog">
		<el-container style="height: 80vh;border: 1px solid var(--el-border-color);">
			<el-aside v-loading="data.menuLoading" width="400px">
				<el-container>
					<el-main class="noPadding">
						<el-tree ref="menu" :data="data.menuList" :expand-on-click-node="false" :props="data.menuProps"
								 check-strictly class="menu"
								 default-expand-all draggable highlight-current node-key="id"
								 show-checkbox @node-click="menuClick">
							<template #default="{ node, data }">
									<span class="custom-tree-node">
										<span class="label">
											{{ node.label }}
										</span>
										<span class="do">
											<el-button icon="Plus" size="small" @click.stop="add(node, data)"/>
											<el-button icon="Edit" size="small" @click.stop="edit(node, data)"/>
										</span>
									</span>
							</template>
						</el-tree>
					</el-main>
					<el-footer style="height: 51px">
						<el-button icon="el-icon-plus" size="small" type="primary" @click="add"/>
						<el-button icon="el-icon-delete" plain size="small" type="danger" @click="delMenu"/>
					</el-footer>
				</el-container>
			</el-aside>
			<el-main ref="main" class="noPadding" v-loading="data.dataLoading"
					 style="padding: 0 20px;display: flex;flex-direction: row;
					flex-wrap: nowrap;align-content: center;justify-content: center;align-items: center;">
				<el-result v-if="!data.details" icon="info" title="温馨提醒">
					<template #sub-title>
						<p>请选择左侧菜单进行操作</p>
					</template>
				</el-result>
				<el-tabs v-if="data.details" v-model="data.activeName" style="height: 100%;width: 100%;">
					<el-tab-pane v-if="data.detailsType === '1'" label="数据配置详情" name="first">
						<el-descriptions border column="1">
							<el-descriptions-item label="配置名称">
								{{ data.detailsData.name }}
							</el-descriptions-item>
							<el-descriptions-item label="目标名称">
								{{ data.detailsData.targetName }}
							</el-descriptions-item>
							<el-descriptions-item label="描述">
								{{ data.detailsData.remark }}
							</el-descriptions-item>
							<el-descriptions-item label="排序">
								{{ data.detailsData.sort }}
							</el-descriptions-item>
							<el-descriptions-item label="子级数量">
								{{ data.detailsData.children.length }}
							</el-descriptions-item>
						</el-descriptions>
					</el-tab-pane>
					<el-tab-pane v-if="data.detailsType === '2'" label="数据配置详情" name="first"
								 style="height: 100%;width: 100%;">
						<el-container>
							<el-header height="43px" style="padding: 0 0 10px 0">
								<el-button icon="Plus" type="primary" plain @click="() => {
									data.detailsData.configId = data.chooseData.id;
								    data.detailsTitle = '新增数据配置';
								    data.detailsOpen = true;
							    }">
									新增
								</el-button>
							</el-header>
							<el-main style="padding: 0;">
								<el-table v-loading="data.detailsLoading" :data="data.detailsList" border highlight-current-row
										  style="width: 100%;height: 100%">
									<el-table-column align="center" label="数据名称" min-width="210" prop="name"/>
									<el-table-column align="center" label="数据类型来源" min-width="83" prop="source">
										<template #default="scope">
											{{ scope.row.source === '1' ? '默认' : '自定义' }}
										</template>
									</el-table-column>
									<el-table-column align="center" label="数据展示方式" min-width="83" prop="showMethod">
										<template #default="scope">
											<el-tag v-if="scope.row.showMethod === '0'">无需设置</el-tag>
											<el-tag v-if="scope.row.showMethod === '1'">折叠面板</el-tag>
											<el-tag v-if="scope.row.showMethod === '2'">表格</el-tag>
										</template>
									</el-table-column>
									<el-table-column align="center" label="数据类型" prop="type">
										<template #default="scope">
											<el-tag v-if="scope.row.type === '1'">文本</el-tag>
											<el-tag v-if="scope.row.type === '2'">照片</el-tag>
											<el-tag v-if="scope.row.type === '3'">下拉框</el-tag>
											<el-tag v-if="scope.row.type === '4'">日期</el-tag>
											<el-tag v-if="scope.row.type === '5'">选择(是/否)</el-tag>
											<el-tag v-if="scope.row.type.length > 1">自定义</el-tag>
										</template>
									</el-table-column>
									<el-table-column align="center" label="数据长度" min-width="102" prop="length">
										<template #default="scope">
											{{ scope.row.length ? scope.row.length : '此类型无长度限制' }}
										</template>
									</el-table-column>
									<el-table-column align="center" label="是否隐藏" min-width="92" prop="isView">
										<template #default="scope">
											<el-tag v-if="scope.row.isView === '1'" effect="dark">
												{{ "是" }}
											</el-tag>
											<el-tag v-else effect="dark" type="success">
												{{ "否" }}
											</el-tag>
										</template>
									</el-table-column>
									<el-table-column align="center" label="列展示方式" min-width="83" prop="displayMode">
										<template #default="scope">
											<el-tag v-if="scope.row.displayMode === '1'" effect="dark">
												{{ "一列" }}
											</el-tag>
											<el-tag v-if="scope.row.displayMode === '2'" effect="dark">
												{{ "半行" }}
											</el-tag>
											<el-tag v-if="scope.row.displayMode === '3'" effect="dark">
												{{ "占满一行" }}
											</el-tag>
											<el-tag v-if="!scope.row.displayMode" effect="dark">
												{{ "无需设置" }}
											</el-tag>
										</template>
									</el-table-column>
									<el-table-column align="center" label="数据描述" min-width="183" prop="remark"/>
									<el-table-column align="center" fixed="right" label="操作" width="172">
										<template #default="scope">
											<el-button icon="Edit" link type="warning" @click="editDetailsInfo(scope.row)">
												修改
											</el-button>
											<el-button icon="Delete" link type="danger" @click="deleteDetailsInfo(scope.row)">
												删除
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</el-main>
							<el-footer>
								<div style="display: flex;justify-content: flex-end;">
									<pagination v-model:limit="data.queryParams.size" v-model:page="data.queryParams.current"
												:total="data.total" style="padding: 0" @pagination="getDetailsInfoList"
												:page-sizes="[20,40,60,80]"/>
								</div>
							</el-footer>
						</el-container>
					</el-tab-pane>
				</el-tabs>
			</el-main>

			<DataConfigDialog :data="data.configData" :open="data.configOpen" :title="data.configTitle"
							  @close="closeConfigDialog"/>
			<DataConfigDetailsDialog :data="data.detailsData" :open="data.detailsOpen" :title="data.detailsTitle"
									 :menuList="data.menuList" @close="closeDetailsDialog"/>
		</el-container>
	</el-dialog>
</template>

<script setup>
import dataStructure from '@/api/archive/dataStructureManagement/dataStructure';
import DataConfigDialog from "@/views/configuration/dataStructureManagement/components/DataConfigDialog.vue";
import DataConfigDetailsDialog
	from "@/views/configuration/dataStructureManagement/components/DataConfigDetailsDialog.vue";
import {getCurrentInstance, reactive, ref, watch} from "vue";

const {proxy} = getCurrentInstance();
const props = defineProps({
	open: {
		type: Boolean,
		default: false
	},
	data: {
		type: Object,
		default: () => {
			return {}
		}
	}
});
const data = reactive({
	//菜单加载中
	menuLoading: false,
	detailsLoading: false,
	//菜单列表
	menuList: [],
	queryParams: {
		current: 1,
		size: 20,
	},
	total: 0,
	//菜单筛选属性
	menuFilterText: "",
	// 数据结构ID选择集合
	list: [],
	// 右侧详情信息
	DataList: [],
	// 配置详情列表
	detailsList: [],
	// 值对照集合
	dataTableList: [],
	// 配置视图类型
	detailsType: '',
	configData: {},
	configOpen: false,
	configTitle: '',
	detailsData: {},
	chooseData: {},
	detailsOpen: false,
	detailsTitle: '',
	// 右边是否显示
	details: false,
	// tabs
	activeName: 'first',
	// 是否有下级分类
	children: false,
	dataDialogIsView: false,
	dataLoading: false,
	dataModel: {},
	menuProps: {
		label: (data) => {
			return data.name;
		},
	},
	dataModelFormRules: {
		name: [
			{required: true, message: '值名称不能为空', trigger: 'blur'}
		],
		value: [
			{required: true, message: '值数据不能为空', trigger: 'blur'}
		]
	},
	dataDialogTitle: ''
});
const menu = ref(null);
const save = ref(null);
const emit = defineEmits(['close']);

watch(() => data.menuFilterText, (newData) => {
	data.menuFilterText = newData;
}, {});

watch(() => props.data, (newData) => {
	data.form = newData;
}, {deep: true});

function openDialog() {
	data.details = false;
	data.detailsType = '';
	getMenu();
	getDetailsInfoList()
}

/*
 * 菜单点击
 * @author: saya
 * @date: 2023-03-23 11:23:10
 */
async function menuClick(dataInfo, node) {
	data.dataLoading = true;
	data.details = true;
	data.DataList = dataInfo.children
	// 详情查询
	dataStructure.queryById({
		id: dataInfo.id,
		current: data.queryParams.current,
		size: data.queryParams.size,
	}).then(res => {
		if (res.data.children.length !== 0) {
			data.children = true;
		}
		data.chooseData = dataInfo;
		//判断是否是父级类型
		if (res.data.parent.id > 0) {
			data.detailsType = '2';
			getDetailsInfoList();
		} else {
			data.detailsData = res.data;
			data.detailsType = '2';
			getDetailsInfoList();
		}
		data.dataLoading = false;
	}).catch(error => {
		proxy.msgError(error, "查询失败");
	});
}

/**
 * 查询左边列表
 * @author: saya
 */
function getMenu() {
	data.menuLoading = true;
	dataStructure.list({
		id: props.data.structureId
	}).then(res => {
		if (res.code === 200) {
			data.menuList = res.data;
		} else {
			proxy.msgError(res, "数据查询失败");
		}
	}).catch(error => {
		proxy.msgError(error, "数据查询失败");
	});
	data.menuLoading = false;
}

/*
 * 添加菜单
 */
async function add(node, dataInfo) {
	//组装基本菜单数据
	let newMenuSort = dataInfo ? dataInfo.children ? dataInfo.children.length + 1 : '1' : data.menuList.length + 1;
	let newMenuData = {
		//父级ID
		parent: {
			id: dataInfo ? dataInfo.id : "0",
		},
		//  名称
		name: "未命名" + newMenuSort,
		//  数据目标名称
		targetName: '',
		//  配置描述
		remark: '',
		//  排序
		sort: newMenuSort
	};
	//页面加载中
	data.menuLoading = true;
	// 保存菜单方法
	dataStructure.save(newMenuData).then(res => {
		if (res.code === 200) {
			getMenu();
			proxy.msgSuccess("添加成功");
		} else {
			//释放页面加载
			data.menuLoading = false;
			proxy.msgError(res, "添加失败");
		}
	}).catch(error => {
		//释放页面加载
		data.menuLoading = false;
		proxy.msgError(error, "添加失败");
	})
}

/*
 * 根据Id获取门类配置数据
 * @author: saya
 */
function edit(node, dataInfo) {
	dataStructure.queryById({
		id: dataInfo.id,
	}).then(res => {
		if (res.code === 200) {
			data.configTitle = '修改数据结构';
			data.configData = res.data;
			data.configOpen = true;
		} else {
			proxy.msgError(res, res.msg);
		}
	}).catch(error => {
		proxy.msgError(error, "查询失败");
	});
}

/*
 * 删除菜单
 * @author: saya
 * @date: 2023-03-23 17:46:37
 */
async function delMenu() {
	//获取选中的节点
	let CheckedNodes = menu.value.getCheckedNodes();
	if (CheckedNodes.length === 0) {
		proxy.msgError("请选择需要删除的项");
		return false;
	}
	//删除操作确认
	proxy.$confirm("确认删除已选择的菜单吗？", "提示", {
		type: "warning",
		confirmButtonText: "删除",
		confirmButtonClass: "el-button--danger",
		cancelButtonText: "取消",
		cancelButtonClass: "el-button--primary el-button--large"
	}).then(() => {
		//页面加载
		data.menuLoading = true;
		//请求参数处理删除id参数
		let reqData = CheckedNodes.map((item) => item.id).join(",");
		//调用删除接口
		dataStructure.delete({
			ids: reqData
		}).then((res) => {
			if (res.code === 200) {
				//在列表中移除已删除的菜单项
				CheckedNodes.forEach((item) => {
					let node = menu.value.getNode(item);
					//移除菜单项
					menu.value.remove(item);
					if (node.isCurrent) {
						//当前删除的是当前编辑的菜单，则清空编辑表单页面
						save.value.setData({});
					}
				});
				proxy.msgSuccess(res, "删除成功");
			} else {
				proxy.msgError(res.msg, "删除失败");
			}
		}).catch((error) => {
			proxy.msgError(error, "删除失败");
		});
		data.menuLoading = false;
	}).catch((error) => {
		proxy.msgError(error);
	});
}

function editDetailsInfo(info) {
	data.detailsData = {...info};
	if (data.detailsData.type === '3' || data.detailsData.type === '5') {
		data.dataTableList = JSON.parse(data.detailsData.dataConfigValue);
	}
	data.title = '修改数据配置';
	data.detailsOpen = true;
}

function deleteDetailsInfo(info) {
	dataStructure.deleteDetailsById({
		ids: info.id,
	}).then(res => {
		if (res.code === 200) {
			getDetailsInfoList();
			proxy.msgSuccess("删除成功");
		}
	}).catch(error => {
		proxy.msgError(error, "删除失败");
	});
}

function getDetailsInfoList() {
	data.menuLoading = true;
	dataStructure.getList({
		configId: data.chooseData.id,
		current: data.queryParams.current,
		size: data.queryParams.size
	}).then(res => {
		if (res.code === 200) {
			data.detailsList = res.data.records;
			data.total = res.data.total;
		}
		//释放页面加载
		data.menuLoading = false;
	}).catch(error => {
		proxy.msgError(error, "查询失败");
	});
}

function closeConfigDialog() {
	data.configData = {};
	data.configOpen = false;
	data.configTitle = '';
	getMenu();
}

function closeDetailsDialog() {
	data.detailsData = {};
	data.detailsOpen = false;
	data.detailsTitle = '';
	getDetailsInfoList();
}
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
	display: flex;
	flex: 1;
	height: 100%;
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	height: 100%;
	padding-right: 24px;
}

.custom-tree-node .label {
	display: flex;
	align-items: center;
	height: 100%;
}

.custom-tree-node .label .el-tag {
	margin-left: 5px;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .do {
	display: inline-block;
}

.demo-tabs > .el-tabs__content {
	padding: 32px;
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}
</style>
