import request from '@/utils/httpRequest'

export default {
  save: function (inputForm) {
    return request({
      url: '/erp/assist/erpTreatmentScopeSet/save',
      method: 'post',
      data: inputForm
    })
  },

  delete: function (ids) {
    return request({
      url: '/erp/assist/erpTreatmentScopeSet/delete',
      method: 'delete',
      params: {ids: ids}
    })
  },

  queryById: function (id) {
    return request({
      url: '/erp/assist/erpTreatmentScopeSet/queryById',
      method: 'get',
      params: {id: id}
    })
  },

  treeData: function () {
    return request({
      url: '/erp/assist/erpTreatmentScopeSet/treeData',
      method: 'get'
    })
  }
}
