<template>
    <el-container style="height: 500px;">
        <!-- <el-aside width="300px" v-loading="menuloading"> -->
        <el-container v-loading="openStatus">
            <el-main class="noPadding">
				<comDoorTable @clickChild="clickEven"/>
			</el-main>
        </el-container>
    </el-container>
    <el-container>
        <el-main>
            <div style="float: right;">
                <div>
                    <el-button type="primary" @click="() => collect()">确定</el-button>
                </div>
            </div>
        </el-main>
    </el-container>
</template>

<script setup>
import receiveList from "@/api/archive/archiveReception/receive"
import {defineEmits, getCurrentInstance, ref} from 'vue'
import {ElMessage} from "element-plus";

const { proxy } = getCurrentInstance()

const props = defineProps({
    handList: {
        type: Array
    }
})

const emit = defineEmits(["childEvent"]);
// 点击树结构的id
const clickEvenId = ref([])
const openStatus = ref(false)
function clickEven(val) {
    clickEvenId.value = val;
}

function collect() {
    let idStr = props.handList.map((v) => v.id);
	openStatus.value = true;
    //拼接的数组字符串，接口传参
	let ids = idStr.join(",");
    if (clickEvenId.value.recordGroupNum) {
        receiveList.mergeOriginalInfo({
            groupId: clickEvenId.value.id,
            originalIds: ids
        }).then(res => {
            if (res.code === 200) {
                emit("childEvent");
				openStatus.value = false;
				ElMessage({
					message: res.msg,
					grouping: true,
					offset: '28',
					type: 'success',
				})
            } else {
				openStatus.value = false;
				proxy.msgError(res.msg);
			}
        }).catch((e) => {
            proxy.msgError(e.msg);
        })
    } else {
        receiveList.mergeOriginalInfo({
            groupId: clickEvenId.value.recordGroupId,
            categoryId: clickEvenId.value.id,
            originalIds: ids
        }).then(res => {
            if (res.code === 200) {
				ElMessage({
					message: res.msg,
					grouping: true,
					offset: '28',
					type: 'success',
				})
				openStatus.value = false;
                emit("childEvent");
            } else {
				openStatus.value = false;
				proxy.msgError(res.msg);
			}
        }).catch((e) => {
			proxy.msgError(e.msg);
		})
    }
}

</script>
