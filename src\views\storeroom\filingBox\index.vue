<template>
	<el-container>
		<el-aside style="border-right: 0;padding: 10px 0 10px 10px;background-color: #fff" width="21%">
			<el-card :body-style="{ padding: '8px 0 0 0', height: '100%', width: '100%' }"
				style="height: 100%;width: 100%;">
				<comDoorTable @clickChild="clickEven" />
			</el-card>
		</el-aside>
		<el-main ref="main" class="noPadding" style="display: flex;flex-direction: column;
			flex-wrap: nowrap;align-items: stretch;padding: 10px;gap: 10px;height: 100%;overflow: hidden;">
			<el-card class="box-card" style="flex-shrink: 0;">
				<div style="display: flex;">
					<el-form ref="formList" :inline="true" :model="form" label-position="right" label-width="auto">
						<el-form-item label="档案盒名称" prop="name" style="width: 18vw;margin: 0;padding-right: 10px;">
							<el-input v-model="form.name" placeholder="请输入档案盒名称" />
						</el-form-item>
						<el-form-item style="margin: 0;padding-left: 10px;">
							<el-button icon="Search" type="primary" @click="getList">查询</el-button>
							<el-button icon="RefreshRight" plain @click="resetQuery">重置</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-card>
			<el-card body-style="height: 100%;width: 100%;padding: 0px 15px 0px 15px" class="box-card"
				style="flex: 1;min-height: 0;">
				<el-container style="padding: 0;height: 100%;">
					<el-header style="display: flex;justify-content:space-between;align-items:center;padding: 0">
						<div>
							<el-button icon="Plus" plain type="success" @click="addBoxing">
								新增
							</el-button>
							<el-button icon="Switch" plain type="primary" @click="moveBoxing">
								移动
							</el-button>
							<el-button icon="TakeawayBox" plain type="warning" @click="collectBoxing">
								拆盒
							</el-button>
						</div>
						<div>
							<span style="margin-right: 15px;" @click="getList">
								<el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
									<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
										<Refresh />
									</el-icon>
								</el-tooltip>
							</span>
							<span @click="screen">
								<el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
									<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
										<FullScreen />
									</el-icon>
								</el-tooltip>
							</span>
						</div>
					</el-header>
					<el-main v-loading="listLoading" style="padding: 0;overflow: hidden;">
						<el-table :data="receiveData" border style="height: 100%;
							width: 100%" @selection-change="handleSelectionChange">
							<el-table-column align="center" min-width="30" type="selection" width="50" fixed="left" />
							<el-table-column align="center" label="档案盒编号" min-width="172" prop="boxNum" />
							<el-table-column align="center" label="档案盒名称" min-width="172" prop="boxName" />
							<el-table-column align="center" label="档案盒规格" prop="boxSpecification" min-width="100" />
							<el-table-column align="center" label="年份" prop="boxYear" />
							<el-table-column align="center" label="容量" prop="boxSize" />
							<el-table-column align="center" label="装盒人" prop="boxIntoPerson.name" min-width="100" />
							<el-table-column align="center" label="备注" prop="boxRemark" show-overflow-tooltip>
								<template #default="scope">
									{{ scope.row.boxRemark ? scope.row.boxRemark : '暂无' }}
								</template>
							</el-table-column>
							<el-table-column align="center" fixed="right" label="操作" width="220">
								<template #default="scope">
									<el-button icon="View" link type="primary" @click="collectFile(scope.row)">
										查看
									</el-button>
									<el-button icon="Edit" link type="warning" @click="editFile(scope.row)">
										编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="collectDelete(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer style="display: flex;flex-direction: row;flex-wrap: nowrap;justify-content: flex-end;">
						<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
							:page-sizes="[15, 30, 45, 60]" :total="total" style="padding: 0" @pagination="getList()" />
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 拆盒 -->
		<Boxing :clickItem="clickItem" :handList="handList" :openStatus="openBoxing" :title="title"
			@closeMethod="closeOutBox" />
		<BoxControl :boxInfo="boxInfo" :moveModel="moveModel" :openStatus="openControlBox" :title="title"
			:viewModel="viewModel" @closeMethod="closeControlBox" />
	</el-container>
</template>

<script setup>
import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import boxList from '@/api/archive/storeroom/filingBox';
import tool from '@/utils/tool';
// 装盒弹窗
import Boxing from './boxing.vue';
import BoxControl from './boxControl.vue';

const data = reactive({
	queryParams: {
		current: 1,
		size: 15,
	}
})
const total = ref(0)
const { queryParams } = toRefs(data)
const { proxy } = getCurrentInstance()
// 接收库List
const receiveData = ref([])
// 点击查询列表
const listLoading = ref(false)
// 点击树结构的id
const clickItem = ref([])
// 头部查询
const form = ref({
	name: ''
})
// 数据集合
const handList = ref([])
// 弹窗 标题
const title = ref('')
// 是否弹窗
const openBoxing = ref(false)
const openControlBox = ref(false)
const viewModel = ref(false)
const moveModel = ref(false)
const boxInfo = ref({})

//初始方法
onMounted(() => {
	getList()
});

// 进入时查询全部
function getList() {
	listLoading.value = true;
	boxList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		boxName: form.value.name,
		'controlGroup.id': clickItem.value.recordGroupName ? clickItem.value.id : null,
		'controlCategory.id': clickItem.value.recordGroupName ? null : clickItem.value.id,
	}).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
			total.value = res.data.total;
		} else {
			proxy.msgError(res.msg);
		}
		listLoading.value = false;
	}).catch(() => {
		listLoading.value = false;
		proxy.msgError('查询失败');
	})
}

function clickEven(val) {
	clickItem.value = val;
	getList();
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}

// 重置
function resetQuery() {
	form.value.name = '';
	clickItem.value = [];
	getList();
}

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 查看
function collectFile(val) {
	title.value = '查看';
	boxInfo.value = val;
	viewModel.value = true;
	openControlBox.value = true;
}

// 编辑
function editFile(val) {
	title.value = '编辑';
	boxInfo.value = val;
	openControlBox.value = true;
}

// 拆盒
function collectBoxing() {
	if (handList.value.length > 0 && handList.value.length === 1) {
		openBoxing.value = true;
		title.value = '拆盒';
	} else if (handList.value.length === 0) {
		proxy.msgError('请先选择需要拆盒的数据！');
	} else if (handList.value.length > 1) {
		proxy.msgError('只能选择一条拆盒数据！');
	}
}

// 新增盒子
function addBoxing() {
	title.value = '新增';
	boxInfo.value = {};
	openControlBox.value = true;
}

// 移动盒子
function moveBoxing() {
	if (handList.value.length > 0 && handList.value.length === 1) {
		boxInfo.value = receiveData.value.filter(box => box.id === handList.value[0].id)[0];
		moveModel.value = true;
		openControlBox.value = true;
		title.value = '移动';
	} else if (handList.value.length === 0) {
		proxy.msgError('请先选择需要移动的数据！');
	} else if (handList.value.length > 1) {
		proxy.msgError('只能选择一条移动数据！');
	}
}

//关闭拆盒窗口
function closeOutBox() {
	openBoxing.value = false;
	handList.value = [];
	getList();
}

//关闭控制窗口
function closeControlBox() {
	openControlBox.value = false;
	viewModel.value = false;
	moveModel.value = false;
	getList();
}

// 退回
function collectDelete(val) {
	proxy.$confirm('是否要删除吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		boxList.delete({
			ids: val.id
		}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('删除成功');
				getList();
			}
		}).catch(() => {
			proxy.msgError('删除失败');
		})
	}).catch(() => {
		console.log('取消删除');
	})
}
</script>

<style scoped></style>
