import http from "@/utils/request";
// 系统配置  >  审核流程  api

export const inventoryApi = {
	saveFileOrder: function (data) {
		return http.post("/erp/procure/erpPurchaseWarehousingRecord/saveBill", data);
	},
	getFile: function (params) {
		return http.get("/erp/procure/erpPurchaseWarehousingRecord/queryFileById", params)
	},
	delFile: function (params) {
		return http.delete("/erp/procure/erpPurchaseWarehousingRecord/deleteFile", params)
	}
};

export const inventoryApi2 = {
	saveFileOrder: function (data) {
		return http.post("/erp/sales/erpSalesOutboundRecord/saveBill", data);
	},
	getFile: function (params) {
		return http.get("/erp/sales/erpSalesOutboundRecord/queryFileById", params)
	},
	delFile: function (params) {
		return http.delete("/erp/sales/erpSalesOutboundRecord/deleteFile", params)
	}
};
