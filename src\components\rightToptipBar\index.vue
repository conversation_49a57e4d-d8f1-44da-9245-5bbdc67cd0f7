<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-23 15:15:29
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-08 14:25:17
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\components\rightToptipBar\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div style="display: flex;justify-content: end;padding-right: 10px;align-items:center">
        <el-tooltip class="box-item" effect="dark" content="刷新" placement="top">
            <el-icon style="font-size: 20px;margin-right: 7px;color: #2a76f8;cursor: pointer;" @click="refreshClick">
                <Refresh />
            </el-icon>
        </el-tooltip>
        <el-tooltip class="box-item" effect="dark" content="全屏" placement="top">
            <el-icon style="font-size: 18px;margin-right: 7px;color: #2a76f8;cursor: pointer;" @click="fullScreenClick">
                <FullScreen />
            </el-icon>
        </el-tooltip>
        <el-tooltip class="box-item" effect="dark" content="显隐列" placement="top">
            <el-icon style="font-size: 19px;margin-right: 7px;color: #2a76f8;cursor: pointer;" @click="transferCol">
                <Menu />
            </el-icon>
            <!-- <el-dropdown trigger="click" :hide-on-click="false" :teleported="false">
                <span class="el-dropdown-link">
                    <el-icon style="font-size: 19px;margin-right: 7px;color: #2a76f8;cursor: pointer;">
                        <Menu />
                    </el-icon>
                </span>
                <template #dropdown>

                    <el-dropdown-menu>

                        <div style="max-height:300px;overflow-y:scroll">
                            <el-dropdown-item>
                                <el-checkbox-group v-model="checkList">
                                    <el-checkbox :checked="item.isShow" v-for="(item, index) in list" :key="item.prop"
                                        @change="(isCheck) => handleChange(isCheck, index)" :label="item.prop">{{ item.label
                                        }}</el-checkbox>
                                </el-checkbox-group>
                            </el-dropdown-item>
                        </div>
                    </el-dropdown-menu>


                </template>
            </el-dropdown> -->
        </el-tooltip>
        <el-dialog v-model="dialogVisible" v-if="dialogVisible" title="表格列" width="40%" :before-close="() => dialogVisible = false">
            <div style="margin:0 auto">
                <el-transfer v-model="checkList" :data="columns" :props="{ key: 'prop' }" :titles="['隐藏列', '显示列']"
                    @change="handleChange1" />
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>

    </div>
</template>

<script setup>
import { FullScreen, Menu, Refresh } from '@element-plus/icons-vue'
import { reactive, ref, getCurrentInstance, toRefs, defineProps, defineEmits, onMounted, watch } from 'vue'
import tool from '@/utils/tool';
const { proxy } = getCurrentInstance();
const emit = defineEmits()
const checkList = ref([])
const dialogVisible = ref(false)
const tarArr = ref([])
const props = defineProps({
    columns: {
        type: Array,
        default: () => { [] }
    }
})
watch(() => dialogVisible.value, (newValue, oldValue) => {
    if (dialogVisible.value) {
        checkList.value = []
        list.value.forEach(v => {
            if (v.isShow) {
                checkList.value.push(v.prop)
            }
        })
    }
})
const list = ref([])
const { columns } = toRefs(props)
const transferCol = () => {
    dialogVisible.value = true
}
const handleChange1 = (arr, position) => {
    checkList.value = arr
}
const handleSubmit = () => {
    list.value.forEach(v => {
        if (checkList.value.includes(v.prop)) {
            v.isShow = true
        }
    })
    emit("update:columns", list.value)
    dialogVisible.value = false
}
const refreshClick = () => {
    emit("handleRefresh");
}
const fullScreenClick = () => {
    tool.screen(document.querySelectorAll('.el-container')[0])
}
onMounted(() => {
    columns.value.forEach(item => {
        if (item.isShow !== false) {
            item.isShow = true
        }

    })
    list.value = columns.value

})
</script>

<style lang="scss" scoped>
::v-deep .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>