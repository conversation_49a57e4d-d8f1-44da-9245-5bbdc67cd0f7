<template>
	<div
		style="width: 100%;height: 100%;display: flex;flex-direction: row;flex-wrap: wrap;align-content: stretch;justify-content: space-evenly;">
		<el-card class="box-card BottomStyle">
			<div style="display:flex;">
				<el-avatar :size="70" :src="userPhoto" fit="contain"></el-avatar>
				<div class="user">
					<p class="username">{{ username }}</p>
					<p class="userSection">{{ userSection }} | {{ userInstitution }}</p>
				</div>
				<div class="right">
					<div style="display:flex;">
						<div>
							<div style="display:flex;">
								<div class="circle"></div>
								<p class="right_P">入职档案</p>
							</div>
							<p class="right_Number">2</p>
						</div>
						<div style="margin-left:30px;">
							<div style="display:flex;">
								<div class="circle" style="background:#FFF7E6;"></div>
								<p class="right_P">健康档案</p>
							</div>
							<p class="right_Number">3</p>
						</div>
						<div style="margin-left:30px;">
							<div style="display:flex;">
								<div class="circle" style=" background: #F6FFED;"></div>
								<p class="right_P">培训档案</p>
							</div>
							<p class="right_Number">4</p>
						</div>
						<div style="margin-left:30px;">
							<div style="display:flex;">
								<div class="circle" style="background: #FDEDFF;"></div>
								<p class="right_P">劳动合同</p>
							</div>
							<p class="right_Number">5</p>
						</div>
					</div>
				</div>
			</div>
		</el-card>
		<div class="center">
			<el-row :gutter="10">
				<el-col :span="16">
					<el-card style="margin-bottom: 0;height: 100%">
						<div style="display: flex;">
							<img class="center_img" src="../../../assets/personage/backlog.png" alt="">
							<p class="center_span">我的借阅申请</p>
						</div>
						<el-divider style="margin: 12px 0"/>
						<div style="">
							<el-scrollbar height="230px" class="scrollbar">
								<div class="center_left_div" v-for="(item) in borrowlist" :key="item.id"
									 @click="routerpush('1', item.borrowAuditStatus)">
									<p class="center_left_div_p">{{ item.borrowApply.name }}</p>
									<div class="bottomTools">
										<p>
											{{ moment(item.borrowStartTime).format('YYYY-MM-DD HH:mm:ss') }}
										</p>
										<el-tag :type="item.borrowAuditStatus === '7' ? 'success' : 'primary'"
												effect="plain">
											{{ item.borrowAuditStatus === '7' ? '已审批' : '待审批' }}
										</el-tag>
									</div>
								</div>
							</el-scrollbar>
						</div>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card style="height: 100%">
						<div style="display: flex;">
							<img class="center_img" src="../../../assets/personage/remind.png" alt="">
							<p class="center_span">即将到期借阅</p>
						</div>
						<el-divider style="margin: 12px 0;"/>
						<p style="text-align: right; margin-bottom: 10px">
							已借阅<span style="color:blue;">{{ infolist.length }}</span>份档案
						</p>
						<div style="display: flex; flex-flow: wrap;">
							<el-scrollbar height="230px" style="width: 100%;" view-style="display: grid;
								grid-template-columns: 1fr 1fr 1fr;grid-row-gap: 22px;justify-content: center;
								justify-items: center;align-items: center;align-content: center;">
								<div class="center_div" v-for="item in infolist" :key="item.id"
									 @click="routerpush('2')">
									<p class="center_div_p">
										{{
											item.infoList.length > 1 ? item.infoList[0].detailsInfo.name + '等' + item.infoList.length + '份档案' : item.infoList[0].detailsInfo.name + '档案'
										}}
									</p>
									<div class="tagBox">
										<div v-if="item.expireDays > 0" class="triangle-green"></div>
										<div v-if="item.expireDays > 0" class="residue-green">
											<p style="color:#FFF7E6;">剩余{{ item.expireDays }}天</p>
										</div>
										<div v-if="moment(item.borrowEndTime).format('YYYY-MM-DD') === moment(new Date()).format('YYYY-MM-DD') && item.expireDays === 0"
											 class="triangle-yellow"></div>
										<div v-if="moment(item.borrowEndTime).format('YYYY-MM-DD') === moment(new Date()).format('YYYY-MM-DD') && item.expireDays === 0"
											 class="residue-yellow">
											<p style="color:#FFF7E6;">今日到期</p>
										</div>
										<div v-if="item.expireDays < 0" class="triangle"></div>
										<div v-if="item.expireDays < 0" class="residue">
											<p style="color:#FFF7E6;">逾期{{ -item.expireDays }}天</p>
										</div>
									</div>
								</div>
							</el-scrollbar>
						</div>
					</el-card>
				</el-col>
			</el-row>
		</div>
		<div class="bottom">
			<el-row :gutter="10">
				<el-col :span="16">
					<el-card style="height:33vh;">
						<div style="display: flex;">
							<img class="bottom_img" src="../../../assets/personage/inform.png" alt="">
							<p class="bottom_span">公告通知</p>
						</div>
						<el-divider style="margin-top:12px;"/>
						<div class="bottom_div" v-if="noticlist.length > 0 && noticlist[0]">
							<div style="margin:0px 10px 10px 10px;">
								<div class="bottom_div_date">{{ year }}</div>
								<div class="bottom_div_bottomdate">{{ day }}</div>
							</div>
							<div style="margin-left:5px;">
								<p style="font-size:16px;font-weight: 400;margin-bottom: 5px;">
									{{ noticlist[0].noticeTitle }}
								</p>
								<p>{{ noticlist[0].noticeContent }}</p>
							</div>
						</div>
						<el-scrollbar height="150px" style="margin:-13px 0 -13px 0;">
							<div style="margin:-10px 0 10px 10px; line-height:22px;" v-for="(item, index) in noticlist"
								 :key="item.id">
								<div v-if="index !== 0">
									<p class="notice">
										{{ item.noticeContent }}
										<span style=" float:right;">
											{{
												item.updateDate ? moment(item.updateDate).format('YYYY-MM-DD HH:mm:ss') : moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')
											}}
										</span>
									</p>
								</div>
							</div>
						</el-scrollbar>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card style="height: 33vh;">
						<div style="display: flex;">
							<img class="bottom_img" src="../../../assets/personage/information.png" alt="">
							<p class="bottom_span">我的借阅统计</p>
						</div>
						<el-divider style="margin-top: 15px;"/>
						<div style="display: flex;align-content: center;align-self: center">
							<echarts :value="3" tit="借阅次数" :hig="3 / 100" unit="%"
									 :gradientColors="['#21C2FF ', '#0071F5', '#414FFF']"/>
							<echarts :value="6" tit="借阅份数" :hig="6 / 100" unit="%"
									 :gradientColors="['#FFD144  ', '#FF7D50', '#E46A34']"/>
						</div>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</div>
</template>

<script setup>
import {ref} from 'vue'
import moment from "moment";
import work from '@/api/archive/work'
import echarts from '../work/echarts.vue'
import router from "@/router";

const borrowlist = ref([])
const noticlist = ref([])
const infolist = ref([])
const infoArr = ref([])
const infoArr2 = ref([])
const username = ref('')
const userPhoto = ref('')
const leftValue = ref('')
const userInstitution = ref('')
const rightValue = ref('')
const userSection = ref('')
const date = ref('')
const year = ref('')
const userId = ref('')
const day = ref('')
const userinfo = () => {
	username.value = JSON.parse(localStorage.getItem("USER_INFO")).content.name;
	userPhoto.value = JSON.parse(localStorage.getItem("USER_INFO")).content.photo;
	userId.value = JSON.parse(localStorage.getItem("USER_INFO")).content.id;
	userInstitution.value = JSON.parse(localStorage.getItem("USER_INFO")).content.sysOffice.name;
	userSection.value = JSON.parse(localStorage.getItem("USER_INFO")).content.sysOrg.name;
	date.value = moment(new Date()).format('YYYY-MM-DD')
	year.value = date.value.substring(0, 7)
	day.value = date.value.substring(8, 10)
}
//借阅申请
const borrowList = () => {
	work.borrowList({'borrowApply.id': userId.value}).then(res => {
		if (res.code === 200) {
			borrowlist.value = res.data.records
		}
	});
}
// 公告通知
const noticList = () => {
	work.noticList().then(res => {
		if (res.code === 200) {
			noticlist.value = res.data.records
		}
	});
}
// 到期档案
const infoList = () => {
	work.info().then(res => {
		if (res.code === 200) {
			infolist.value = res.data
			infolist.value.forEach((v) => {
				infoArr.value.push(v.infoList)
			})
			leftValue.value = infoArr.value.length
			console.log(typeof leftValue.value);
			infoArr.value.forEach((i) => {
				i.forEach((v) => {
					infoArr2.value.push(v)
					// console.log(:hig="leftValue / 100" unit="%");
					rightValue.value = Number(infoArr2.value.length)
				})
			})
		}
	});
}
//路由跳转
const routerpush = (type, auditStatus) => {
	if (type == '1') {
		// 根据审批状态跳转到相应的标签页
		// auditStatus === '7' 表示已审批，否则为待审批
		const tabName = auditStatus === '7' ? '2' : '1';
		router.push({
			path: '/borrowingApproval',
			query: { tab: tabName }
		})
	}
	if (type == '2') {
		router.push('/returnManagement')
	}
}
userinfo()
borrowList()
noticList()
infoList()
</script>

<style lang="scss" scoped>
::v-deep .scrollbar .el-scrollbar__view {
	display: flex;
	flex-flow: wrap;
	// height:50px;
}

.sleepContentView {
	margin: 10px 0px 0px 70px;
	width: 130px;
	height: 130px;
	background-image: linear-gradient(#21C2FF, #0071F5);
	border-radius: 50%;
	padding: 2px;
}

.sleepContentView .circle {
	width: 100%;
	height: 100%;
	background: #fff;
	border-radius: 50%;
}

.waterPolo {
	width: 115px;
	height: 56px;
	position: relative;
	top: 65px;
	right: -6px;
	background: linear-gradient(90deg, #21C2FF, #0071F5);
	border-radius: 0 0 56px 56px;
	line-height: 50px;
}

.notice {
	margin-top: 20px;
	// margin-right:60px;
}

.residue {
	width: 100px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	position: relative;
	right: -14.2px;
	top: -1px;
	background-color: red;
}

.triangle {
	width: 0;
	height: 0;
	border-left: 11px solid transparent;
	border-right: 11px solid transparent;
	border-top: 11px solid #A72424;
	position: relative;
	right: -14px;
	transform: rotate(44deg);
}

.residue-green {
	width: 100px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	position: relative;
	right: -14.2px;
	top: -1px;
	background-color: #67C23A;
}

.triangle-green {
	width: 0;
	height: 0;
	border-left: 11px solid transparent;
	border-right: 11px solid transparent;
	border-top: 11px solid #67C23A;
	position: relative;
	right: -14px;
	transform: rotate(44deg);
}

.residue-yellow {
	width: 100px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	position: relative;
	right: -14.2px;
	top: -1px;
	background-color: #FFA84B;
}

.triangle-yellow {
	width: 0;
	height: 0;
	border-left: 11px solid transparent;
	border-right: 11px solid transparent;
	border-top: 11px solid #FFA84B;
	position: relative;
	right: -14px;
	transform: rotate(44deg);
}

::v-deep .BottomStyle {
	margin: 10px 10px 5px 10px;
	width: 100%;
	height: 13vh;

	.el-card__body {
		padding-bottom: 0px
	}
}

.center {
	width: 100%;
	margin: 5px 10px 5px 10px;

	.center_span {
		margin-left: 15px;
		margin-top: -3px;
		// width: 88px;
		height: 14px;
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}

	.center_left_div {
		// display: flex;
		width: 24%;
		height: 100px;
		margin: 5px;
		background: #F9FBFC;
		border-radius: 4px;
		cursor: pointer;
		box-shadow: 5px 5px 5px #E4E7ED;
		border: 1px solid #E4E7ED;

		.center_left_div_p {
			margin: 20px;
			font-size: 15px;
			font-weight: 545;
			color: #333333;
		}

		.bottomTools {
			display: flex;
			justify-content: space-around;
			align-content: center;
			align-items: center;
			flex-wrap: nowrap;
			flex-direction: row;
		}

		.center_left_div_bottomP {
			margin: -5px 0px 0px 20px;
		}

		.center_left_div_button {
			margin: -30px 0px 0px 200px;
		}
	}

	.center_img {
		margin-left: 0;
		margin-top: -5px;
	}

	.center_div {
		width: 81%;
		height: 142px;
		background-color: #E9F1FE;
		cursor: pointer;
		padding-top: 22px;
		border: 1px solid #E4E7ED;
		border-radius: 4px;

		.center_div_p {
			width: 100%;
			height: 55%;
			text-align: center;
			font-size: 14px;
			padding: 0 5px;
			font-weight: 400;
			color: black;
		}

		.tagBox {
			display: flex;
			flex-direction: column;
			align-content: flex-end;
			flex-wrap: wrap;
			align-items: flex-end;
		}
	}

	.center_div:hover {
		background: #FFFFFF;
		border-radius: 4px;
		border: 1px solid gainsboro;
		box-shadow: 5px 5px 10px rgba(45, 74, 234, 0.14);
	}
}

.bottom {
	width: 100%;
	margin: 5px 10px 10px 10px;

	.bottom_span {
		margin-left: 15px;
		margin-top: -3px;
		// width: 88px;
		height: 14px;
		font-size: 14px;
		font-weight: bold;
		color: #333333;

	}

	.bottom_img {
		margin-left: 0px;
		margin-top: -5px;
	}

	.bottom_div {
		display: flex;

		.bottom_div_date {
			width: 73px;
			height: 25px;
			text-align: center;
			line-height: 25px;
			color: #FFFFFF;
			background: #4199FF;
		}

		.bottom_div_bottomdate {
			width: 73px;
			height: 30px;
			text-align: center;
			font-size: 18px;
			line-height: 30px;
			color: #2A76F8;
			background: #ffffff;
			box-shadow: 0px 2px 9px 0px rgba(45, 74, 234, 0.11);
		}
	}
}

.contain {
	width: 68px;
	height: 68px;
	margin: 5px 0 20px 20px;
	border-radius: 50%;
	background-color: aqua;
	background-image: url('@/assets/icons/pngs.png');
	background-size: 100px;
}

.user {
	margin-left: 20px;
	padding-top: 20px;
	margin-right: auto
}

.username {
	font-size: 16px;
	font-weight: 400;
	color: #000000;
}

.userSection {
	margin-top: 5px;
	font-size: 12px;
	font-weight: 400;
	color: #666666;
}

.circle {
	border-radius: 50%;
	width: 24px;
	height: 24px;
	background: #E6F7FF;
}

.right {
	margin-right: 40px;
	margin-top: 15px;
}

.right_P {
	margin-left: 10px;
	font-size: 14px;
	font-weight: 400;
	color: #6D7070;
}

.right_Number {
	width: 11px;
	height: 18px;
	font-size: 22px;
	font-weight: 400;
	color: #333333;
	margin-left: auto;
}
</style>
