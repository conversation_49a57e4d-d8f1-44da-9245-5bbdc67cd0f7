<template>
	<el-dialog
		v-model="visible"
		title="系统菜单选择"
		top="2vh"
		width="52%"
		@closed="$emit('closed')"
	>
		<el-container v-loading="listLoading" style="cursor: pointer; height: 80vh">
			<el-main
				style="
				  	padding: 0;
				  	border: 1px solid var(--el-border-color-light);
				  	display: flex;
				  	justify-content: space-between;
				  	align-items: stretch;
				  	flex-direction: column;
				  	flex-wrap: nowrap;
				"
			>
				<el-header>
					<div class="right-panel-search">
						<el-input
							v-model="filterText"
							placeholder="输入关键字进行过滤"
							style="width: 220px"
							@input="input_change"
						>
						</el-input>
					</div>
				</el-header>
				<el-main class="noPadding" style="height: 360px">
					<right-menu ref="right_menu" @onMenuClick="onMenuClick"/>
					<el-tree
						ref="tree"
						:data="dataList"
						:default-checked-keys="selects"
						:filter-node-method="filterNode"
						:props="menuProps"
						check-strictly
						class="filter-tree"
						default-expand-all
						highlight-current
						node-key="id"
						show-checkbox
						@node-contextmenu="right_btn"
						@node-click="menuNodeClick"
					>
					</el-tree>
					<div class="buttons">
						<!-- <el-button @click="resetChecked">清空</el-button> -->
					</div>
				</el-main>
			</el-main>
			<el-aside
				style="
				  	border: 1px solid var(--el-border-color-light);
				  	display: flex;
				  	flex-direction: column;
				  	flex-wrap: nowrap;
				  	align-items: stretch;
				  	justify-content: space-between;
				"
			>
				<el-header>
					<h4>数据权限列表</h4>
				</el-header>
				<el-main style="padding: 0">
					<el-table
						ref="dataRuleTable"
						v-loading="dataRuleLoading"
						:data="dataRuleList"
						:row-key="id"
						@select="dataRuleSelectClick"
					>
						<el-table-column type="selection" width="55"></el-table-column>
						<el-table-column label="权限名称" prop="ruleName"></el-table-column>
					</el-table>
				</el-main>
			</el-aside>
		</el-container>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" @click="submitComplet()">确 定</el-button>
		</template>
	</el-dialog>
</template>

<script>
import sysDataRulesService from "@/api/model/sys/sysDataRulesService";
import sysRoleDataruleService from "@/api/model/sys/sysRoleDataruleService";
import RightMenu from "@/components/system/sysMenuSelect/rightMenu.vue";

export default {
	components: {
		RightMenu,
	},
	props: {
		//是否多选
		isMultiple: {type: Boolean, default: false},
		//回调函数
		selectChange: {type: Function},
		//当前角色
		orgId: {type: String, default: ""},
	},
	data() {
		return {
			//数据列表
			dataList: [],
			filterText: "",
			menuProps: {
				children: "children",
				label: "name",
			},
			//查询表单
			searchForm: {},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
			//已选择的数据
			selectData: [],
			selects: [],
			visible: true,
			//当前角色
			role: {},
			//当前菜单
			menu: {},

			//菜单权限列表
			dataRuleList: [],
			dataRuleLoading: false,
		};
	},
	mounted() {
		setTimeout(() => {
			//刷新数据列表
			this.getDataList();
		}, 0);
	},
	methods: {
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getDataList() {
			//初始化数据列表
			this.dataList = [];
			//请求接口
			this.listLoading = true;
			let res = await this.$API.sysRoleMenuService.findByOrg({
				orgId: this.orgId,
				//查询参数
				...this.searchForm,
			});
			this.listLoading = false;
			if (res.code == 200) {
				//数据列表
				this.dataList = res.data;
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},

		/*
		 * 选中的数据
		 * @author: 路正宁
		 * @date: 2023-03-31 17:26:29
		 */
		selecteds(role, menuIds) {
			//选中指定的系统菜单
			this.selects = menuIds;
			this.role = role;
		},
		/*
		 * 提交选择结果
		 * @author: 路正宁
		 * @date: 2023-04-03 09:55:23
		 */
		submitComplet() {
			this.selectChange(this.role, this.$refs.tree.getCheckedKeys());
			this.visible = false;
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		input_change() {
			this.$refs.tree.filter(this.filterText);
		},
		right_btn(e, data) {
			this.$refs.right_menu.onButtonClick(e, data);
		},
		onMenuClick(ind, data) {
			this.$refs.tree.setChecked(data, ind === 1);
			if (data.children) {
				data.children.forEach((v) => {
					this.onMenuClick(ind, v);
				});
			}
		},
		/*
		 * 系统菜单点击事件
		 * @author: 路正宁
		 * @date: 2023-04-04 10:30:34
		 */
		async menuNodeClick(row, node, rootNode) {
			//初始化数据列表
			this.dataRuleList = [];
			this.menu = row;
			//请求接口
			this.dataRuleLoading = true;
			let res = await sysDataRulesService.list({
				"sysMenu.id": row.id,
				//当前页码
				current: 1,
				//每页条数
				size: 1000,
			});

			if (res.code == 200) {
				//数据列表
				this.dataRuleList = res.data.records;
			} else {
				this.$Response.errorNotice(res, "查询失败");
				return;
			}
			//角色数据权限查询
			let result = await sysRoleDataruleService.list({
				"sysRole.id": this.role.id,
				menuId: row.id,
			});
			if (result.code != 200) {
				this.$Response.errorNotice(res, "查询失败");
				return;
			}
			this.dataRuleLoading = false;
			//遍历数据权限列表
			this.dataRuleList.forEach((item) => {
				//选中已配置的数据权限
				for (let j = 0; j < result.data.records.length; j++) {
					if (result.data.records[j].sysDataRules.id == item.id) {
						this.$nextTick(() => {
							this.$refs.dataRuleTable.toggleRowSelection(item);
						});
					}
				}
			});
		},
		/*
		 * 数据权限勾选事件
		 * @author: 路正宁
		 * @date: 2023-04-04 13:27:13
		 */
		async dataRuleSelectClick(selection, row) {
			let ruleData = [];
			for (let j = 0; j < selection.length; j++) {
				ruleData[j] = selection[j].id;
			}
			//数据权限ID
			let ruleDataIds = ruleData.join(",");
			//角色
			let roleId = this.role.id;
			//菜单id
			let menuId = this.menu.id;
			this.dataRuleLoading = true;
			let result = await sysRoleDataruleService.configDataRule(
				roleId,
				menuId,
				ruleDataIds
			);
			if (result.code == 200) {
				this.$message.success("配置成功");
			} else {
				this.$Response.errorNotice(result, "查询失败");
				return;
			}
			this.dataRuleLoading = false;
		},
	},
};
</script>

<style scoped>
.el-dialog__body {
	padding: 10px 20px;
}
</style>
