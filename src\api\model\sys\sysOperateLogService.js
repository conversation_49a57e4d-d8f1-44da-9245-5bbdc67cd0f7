import http from "@/utils/request"

/*
 *@description: 操作日志
 *@author: 路正宁
 *@date: 2023-03-17 11:35:22
 *@version: V1.0
*/
export default {
	save: function (inputForm) {
		return http.post(
			'/sys/operateLog/save',
			inputForm
		)
	},

	delete: function (ids) {
		return http.delete(
			'/sys/operateLog/delete',
			{ids: ids}
		)
	},

	queryById: function (id) {
		return http.get(
			'/sys/operateLog/queryById',
			{id: id}
		)
	},

	list: function (params) {
		return http.get(
			'/sys/operateLog/list',
			params
		)
	},

	selectCountByDate: function () {
		return http.get('/sys/operateLog/selectCountByDate')
	},

	retryLogByMethod: function (id) {
		return http.get(
			'/sys/operateLog/retryLogByMethod',
			{id: id}
		)
	},

	exportTemplate: function () {
		return http.get(
			'/sys/operateLog/import/template',
			'blob'
		)
	},

	exportExcel: function (params) {
		return http.get(
			'/sys/operateLog/export',
			params,
			'blob'
		)
	},

	importExcel: function (data) {
		return http.post(
			'/sys/operateLog/import',
			data
		)
	}
}
