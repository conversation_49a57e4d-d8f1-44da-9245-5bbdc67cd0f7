import http from "@/utils/request"

export default {
   // 查询档案盒列表数据
   list: function (params) {
      return http.get(
         '/archive/info/box/list',
         params
      )
   },
   // 根据Id获取档案盒数据
   queryById: function (params) {
      return http.get(
         '/archive/info/box/queryById',
         params
      )
   },
   // 档案(删除)
   delete: function (params) {
      return http.delete(
         '/archive/info/box/delete',
         params
      )
   },
   // 档案拆盒
   dismantleInfo: function (params) {
      return http.get(
         '/archive/info/main/dismantleInfo',
         params
      )
   },
   // 查询档案信息列表数据
   mainList: function (params) {
      return http.get(
         '/archive/info/main/list',
         params
      )
   },
  
}