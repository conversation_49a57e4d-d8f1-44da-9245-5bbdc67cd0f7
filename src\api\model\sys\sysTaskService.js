import http from "@/utils/request"

/*
 *@description: 系统任务
 *@author: 路正宁
 *@date: 2023-03-17 12:03:01
 *@version: V1.0
*/
export default {
	save: function (inputForm) {
		return http.post(
			'sys/task/save',
			inputForm
		)
	},

	delete: function (ids) {
		return http.delete(
			'sys/task/delete',
			{idStr: ids}
		)
	},

	queryById: function (id) {
		return http.get(
			'sys/task/queryById',
			{id: id}
		)
	},

	list: function (params) {
		return http.get(
			'sys/task/list',
			params
		)
	},

	exportTemplate: function () {
		return http.get(
			'sys/task/import/template',
			'blob'
		)
	},

	exportExcel: function (params) {
		return http.get(
			'sys/task/export',
			params,
			'blob'
		)
	},

	importExcel: function (data) {
		return http.post(
			'sys/task/import',
			data
		)
	},

	/*立即执行一次*/
	control: function (params) {
		return http.get(
			'sys/task/control',
			params
		)
	},
}
