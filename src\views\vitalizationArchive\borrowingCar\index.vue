
<template>
    <el-container>
        <el-main ref="main" class="noPadding">
			<el-card body-style="padding-bottom: 0;" class="box-card" style="margin: 10px;">
				<div style="margin-bottom: 10px;display: flex;justify-content:space-between;align-items:center;">
					<div>
						<el-button icon="Stamp" plain type="primary" @click="() => collectFile()">借阅</el-button>
						<el-button icon="Delete" plain type="danger" @click="() => carDelete()">删除</el-button>
					</div>
					<div>
                        <span style="margin-right: 15px;" @click="getList">
                            <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                    <Refresh/>
                                </el-icon>
                            </el-tooltip>
                        </span>
                        <span @click="screen">
                            <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                <el-icon :size="20" color="#409EFC"
                                    style="cursor:pointer;"><el-icon-full-screen /></el-icon>
                            </el-tooltip>
                        </span>
                    </div>
                </div>
                <el-table ref="refTable" :data="receiveData" border @selection-change="handleSelectionChange">
                    <el-table-column align="center" fixed="left" min-width="30" type="selection" width="50"/>
                    <el-table-column align="left" label="档案名称" min-width="200" prop="info.name" show-overflow-tooltip/>
                    <el-table-column align="center" label="档案号" min-width="120" prop="info.num" show-overflow-tooltip/>
					<el-table-column align="center" label="档案存址" min-width="120" prop="storageAddress" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.storageAddress ? scope.row.storageAddress : '暂无' }}
						</template>
					</el-table-column>
                    <el-table-column align="center" label="保留年限" min-width="100" prop="retentionPeriod">
                        <template #default="scope">
							{{ reserve(scope.row.info.retentionPeriod) }}
						</template>
                    </el-table-column>
                    <el-table-column align="center" label="控制等级" min-width="150" prop="controlStatus">
                        <template #default="scope">
							{{ control(scope.row.info.controlStatus) }}
						</template>
                    </el-table-column>
					<el-table-column align="center" label="所属部门" min-width="120" prop="office.name">
						<template #default="scope">
							{{ scope?.row && scope.row.info && scope.row.info.office && scope.row.info.office.name ? scope.row.info.office.name : '暂无' }}
						</template>
					</el-table-column>
					<el-table-column align="center" label="归档时间" min-width="180" prop="archiveTime">
						<template #default="scope">
							{{
								(scope.row.info ? moment(scope.row.info.archiveTime).format('YYYY-MM-DD HH:mm:ss') : undefined) || '暂无'
							}}
						</template>
					</el-table-column>
					<el-table-column align="center" fixed="right" label="操作" min-width="120px">
						<template #default="scope">
							<el-button icon="View" link size="small" type="primary" @click="collectView(scope.row)">查看</el-button>
							<el-button icon="Delete" link size="small" type="danger" @click="carDelete(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<div style="display: flex;justify-content: flex-end">
					<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
								small="small" style="padding: 22px" @pagination="getList()"/>
				</div>
			</el-card>
        </el-main>
        <!-- 借阅 -->
        <el-dialog v-if="open" v-model="open" :title="title" append-to-body width="65%">
            <Borrowing :handList="handList" @childMove="parentMove"></Borrowing>
        </el-dialog>
        <!-- 查看 -->
        <el-dialog v-if="openView" v-model="openView" :title="title" append-to-body width="80%">
            <viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
        </el-dialog>
    </el-container>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from 'vue'
import {Refresh} from '@element-plus/icons-vue'
import tool from '@/utils/tool';
import Borrowing from './borrowing.vue'
import {ElMessage} from 'element-plus'
// 查看档案详情
import viewFiles from '@/views/archiveReception/view.vue';
import retrievalList from '@/api/archive/retrieval';
import moment from "moment/moment";

const data = reactive({
    queryParams: {
		current: 1,
		size: 10,
	}
})
const {queryParams} = toRefs(data)
const {proxy} = getCurrentInstance()
// 借阅车数据List
const receiveData = ref([])
// 档案借阅
const open = ref(false)
//数据总数
const total = ref(0);
const title = ref('')

function getList() {
	retrievalList.carList({
		current: queryParams.value.current,
		size: queryParams.value.size,
		'person.id': tool.data.get('USER_INFO').id,
	}).then(res => {
		if (res.code === 200) {
			console.log(res);
			receiveData.value = res.data.records
			total.value = res.data.total;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })

}
//全屏
function screen() {
    let element = document.documentElement;
    tool.screen(element);
}

const handList = ref([])
// 选中表格
function handleSelectionChange(val) {
    handList.value = val;
}
// 借阅
function collectFile() {
    if (handList.value.length > 0) {
        open.value = true;
        title.value = '档案借阅';
    } else {
        ElMessage({
            message: '请选择需要借阅的文件！',
            type: 'warning',
        })
    }
}

// 取消
function parentMove() {
    open.value = false;
    getList();
}

// 查看receiveId
const receiveId = ref({})
const openView = ref(false)
function collectView(val) {
    receiveId.value = val.info;
    title.value = '查看';
    openView.value = true;
}
// 关闭查看
function parentView() {
    openView.value = false;
}

// 删除
function carDelete(val) {
    if (val) {
        retrievalList.carDelete({
            ids: val.id
        }).then(res => {
            if (res.code === 200) {
                getList();
                proxy.msgSuccess('删除成功');
            }
        }).catch(() => {
            proxy.msgError('查询失败');
        })
    } else {
		if (handList.value.length > 0) {
			let idStr = handList.value.map((v) => v.id);
			//拼接的数组字符串，接口传参
			let ids = idStr.join(",");
			retrievalList.carDelete({
				ids: ids
			}).then(res => {
				if (res.code === 200) {
					getList();
					proxy.msgSuccess('删除成功');
				}
			}).catch(() => {
				proxy.msgError('查询失败');
			})
		} else {
			ElMessage({
				message: '请选择需要删除的信息！',
				type: 'warning',
			})
		}
	}

}

// 保留年限
function reserve(val) {
    if (val == 'Y') {
        return '永久'
    } else if (val == 'D5') {
        return '5年'
    } else if (val == 'D10') {
        return '10年 '
    } else if (val == 'D20') {
        return '20年'
    } else if (val == 'D30') {
        return '30年'
    } else {
		return '暂无'
	}
}
// 控制等级
function control(val) {
    if (val == '1') {
        return '公开'
    } else if (val == '2') {
        return '公司内部开放'
    } else if (val == '3') {
        return '部门内部开放 '
    } else if (val == '4') {
        return '控制'
    } else {
		return '暂无'
	}
}

getList()
</script>

<style scoped>
</style>
