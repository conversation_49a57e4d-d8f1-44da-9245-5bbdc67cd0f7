<!--

 * @Author: 赵克强 <EMAIL>

 * @Date: 2023-06-08 16:04:35

 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git

 * @LastEditTime: 2023-06-21 11:50:35

 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\components\dragTableColumn\index.vue

 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE

-->

<template>
	<el-table :key="className" v-loading="loading" :class="[className, 'dragTable']" :data="tableData" :row-key="className"
		border style="width: 100%" v-bind="$attrs">
		<el-table-column v-for="(item, index) in list" :key="`col_${className + item.prop}`" :align="item.type == 'operate' ||
				item.type == 'selection' ||
				item.type == 'sort'
				? 'center'
				: 'left'
			" :class-name="item.type == 'operate' ||
			item.type == 'selection' ||
			item.type == 'sort'
			? 'noMove'
			: 'isMove'
		" :column-key="`col_${className + item.prop}`" :fixed="item.fixed" :label="item.label"
			:min-width="item.minWidth ? item.minWidth : '120'" :prop="item.prop" :resizable="false" :show-overflow-tooltip="item.type == 'operate' ||
					item.type == 'selection' ||
					item.type == 'sort'
					? false
					: true
				" :sortable="item.type == 'date'" :type="item.type">
			<template #default="scope">
				<slot v-if="item.type == 'operate'" :scopeData="scope" name="operate" />

				<span v-if="item.type == 'date'">{{
					scope.row[item.prop]
					? moment(scope.row[item.prop]).format("YYYY-MM-DD")
					: "--"
				}}</span>

				<span v-if="item.type == 'status' && item.filters.length">{{
					formDict(item.filters, scope.row[item.prop])
				}}</span>

				<span v-if="item.type == 'sort'">{{
					(queryParams.current - 1) * queryParams.size +
					scope.$index +
					1
				}}</span>

				<span v-if="!item.type">{{
					prop(scope.row, item.prop) ?? "--"
				}}</span>
			</template>

			<template v-slot:header #header="scope">
				<span v-if="item.type == 'status' && item.filters.length">
					<p style="display: flex; align-items: center">
						<el-dropdown trigger="click" @command="(command) =>
								handleCommand(
									command,
									item.prop,
									item.searchKey
								)
							">
							<span class="el-dropdown-link">
								{{
									item.label
								}}<el-icon style="
										margin-left: 10px;
										color: #2a76f8;
										font-size: 16px;
										cursor: pointer;
									">
									<BrushFilled />
								</el-icon>
							</span>

							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item :command="''">全部</el-dropdown-item>

									<el-dropdown-item v-for="(x, i) in item.filters" :key="x.value" :command="x.value">{{
										x.name }}</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</p>
				</span>

				<span v-else-if="item.type == 'operate'">操作</span>

				<span v-else>{{ item.label }}</span>
			</template>
		</el-table-column>
	</el-table>
</template>

<script setup>
import { defineEmits, defineProps, getCurrentInstance, nextTick, onMounted, ref, toRefs, useAttrs, watch, } from "vue";

import Sortable from "sortablejs";

import { BrushFilled } from "@element-plus/icons-vue";

import moment from "moment";

import tool from "@/utils/tool";

import tableConfigApi from "@/api/erp/table/config";

import emitter from "@/utils/bus";

const emit = defineEmits();

const $attrs = useAttrs();

const { proxy } = getCurrentInstance();

const props = defineProps({
	tableData: {
		type: Array,

		default: () => {
			[];
		},
	},

	columns: {
		type: Array,

		default: () => {
			[];
		},
	},

	className: {
		type: String,

		default: "",
	},

	getList: {
		type: Function,

		default: () => {
			[];
		},
	},

	queryParams: {
		type: Object,

		default: () => {
			{
			}
		},
	},
});

const cloneData = ref([]);

const list = ref([]);

const params = ref({});

const { tableData, columns, className, getList, queryParams } = toRefs(props);

const loading = ref(false);
const prop = (row, props) => {
	if (props?.includes('.')) {
		const strArr = props?.split('.')
		return row?.[strArr[0]]?.[strArr[1]]
	}
	return row?.[props]
}

const handleCommand = (v, prop, searchKey) => {
	params.value = {
		...queryParams.value,

		[searchKey ? searchKey : prop]: v,
	};

	emit("update:queryParams", params.value);

	getList.value();
};

watch(
	() => cloneData.value,
	(newValue, oldValue) => {
		if (cloneData.value?.length) {
			list.value = cloneData.value?.filter((x) => x.isShow !== false);
		}
	},
	{ deep: true, immediate: true }
);

const formDict = (data, val) => {
	let value = "";

	if (val === true || val === false) {
		if (val === true) {
			value = "1";
		}

		if (val === false) {
			value = "0";
		}
	} else {
		value = val;
	}

	return data.length ? proxy.selectDictLabel(data, value) : "--";
};

// 远程储存数据

const saveTableColunms = (tableColunms, id) => {
	tableConfigApi.save({
		userId: tool.data.get("USER_INFO").id,

		formKey: className.value,

		formValue: tableColunms,

		id: id,
	});
};

// 获取config数据

const getTableColunms = () => {
	tableConfigApi
		.queryById({
			userId: tool.data.get("USER_INFO").id,
			formKey: className.value,
		})
		.then((res) => {
			if (res.code == 200) {
				if (res?.data?.records?.[0]) {
					saveTableColunms(
						JSON.stringify(cloneData.value),
						res.data.records[0]?.id
					).then((res) => {
						if (res.code == 200) {
							cloneData.value = JSON.parse(
								res?.data?.records?.[0]?.formValue
							);

							list.value = cloneData.value?.filter(
								(x) => x?.isShow !== false
							);
						}
					});
				}
			}
		})
		.catch(() => {
		});
};

const getColunms = () => {
	// loading.value = true;

	tableConfigApi
		.queryById({
			userId: tool.data.get("USER_INFO").id,
			formKey: className.value,
		})
		.then((res) => {
			if (res.code == 200) {
				if (res?.data?.records?.[0]) {
					cloneData.value = JSON.parse(
						res?.data?.records?.[0]?.formValue
					);

					list.value = cloneData.value?.filter(
						(x) => x?.isShow !== false
					);

					loading.value = false;
				} else {
					saveTableColunms(JSON.stringify(columns.value)).then(
						(res) => {
							if (res.code == 200) {
								tableConfigApi
									.queryById({
										userId: tool.data.get("USER_INFO").id,

										formKey: className.value,
									})
									.then((res) => {
										if (res.code == 200) {
											cloneData.value = JSON.parse(
												res?.data?.records?.[0]
													?.formValue
											);

											list.value =
												cloneData.value?.filter(
													(x) => x?.isShow !== false
												);
										}

										loading.value = false;
									});
							}
							loading.value = false;
						}
					);
				}
			}
		})
		.catch(() => {
		});
};
onMounted(() => {
	list.value = columns.value?.filter((x) => x?.isShow !== false);
	cloneData.value = list.value;
	getColunms();
	nextTick(() => {
		const wrapperTr = document.querySelector(
			`.${className.value ? className.value + " " : " "
			}.el-table__header-wrapper tr`
		);
		Sortable.create(wrapperTr, {
			animation: 180,
			delay: 0,
			filter: ".noMove",
			onMove: function (evt) {
				if (evt.related.className.includes("noMove")) {
					return false;
				} else {
					return true;
				}
			},
			onEnd: (evt) => {
				const oldItem = cloneData.value[evt.oldIndex];
				cloneData.value.splice(evt.oldIndex, 1);
				cloneData.value.splice(evt.newIndex, 0, oldItem);
				list.value = cloneData.value?.filter(
					(x) => x?.isShow !== false
				);
				getTableColunms();
			},
		});
		emitter.on("getColunms", getColunms);
	});
});
</script>

<style lang="scss" scoped>
::v-deep .el-table__header-wrapper {
	cursor: move;
}

::v-deep .el-table__header--dragging {
	opacity: 0.5;

	background-color: #f2f6fc;
}

::v-deep .el-table__column-resize-proxy {
	background: #f2f6fc;

	opacity: 0.8;

	width: 5px;
}

.btn {
	display: flex;
}

::v-deep .noMove {
	.cell {
		display: contents !important;
	}

	// text-align: center !important;
}
</style>
