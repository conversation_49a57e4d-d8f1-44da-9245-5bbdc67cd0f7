<template>
	<el-dialog v-model="dialogIsOpen" v-loading="loading" align-center title="标签选择" width="46%"
			   @close="closeDialog">
		<div class="dialogHeader">
			<el-input v-model="dataNameSearch" class="w-50 m-2" placeholder="请输入需要查询的名称"
					  @keydown.enter="selectList"/>
			<el-button :icon="Search" style="margin-left: 20px;" type="primary" @click="selectList">
				查询
			</el-button>
			<el-button :icon="RefreshRight" plain @click="() => {dataNameSearch = '';selectList();}">
				重置
			</el-button>
		</div>
		<div class="dialogBody">
			<el-checkbox-group v-model="choseTags" style="width: 100%;margin-bottom: 10px;">
				<el-space style="width: 100%;" wrap>
					<el-checkbox v-for="tag in tagList" :key="tag.id" :label="tag.id" border class="tagCard">
						<div @click="tagChose(tag.id)">
							<el-tag
								:effect="judgeTheme(tag.tagTheme).type"
								:round="judgeShape(tag.tagRound).type"
								:type="judgeColor(tag.tagColor).type"
								auto-close="300"
							>
								{{ tag.tagName }}
							</el-tag>
						</div>
					</el-checkbox>
				</el-space>
			</el-checkbox-group>
			<div style="display: flex;justify-content: flex-end">
				<pagination v-model:limit="data.queryParams.size" v-model:page="data.queryParams.current" :total="total"
							small="small" style="padding-bottom: 5px" @pagination="selectList()"/>
			</div>
		</div>
		<template #footer>
		  	<span class="dialog-footer">
				<el-button icon="Close" plain type="warning" @click="closeDialog">取消</el-button>
				<el-button icon="Check" plain type="primary" @click="submitMethod">确认</el-button>
		  	</span>
		</template>
	</el-dialog>
</template>

<script setup>
import {defineEmits, defineProps, onBeforeMount, reactive, ref} from 'vue'
import tagsManagement from '@/api/archive/tagsManagement';
import {RefreshRight, Search} from "@element-plus/icons-vue";
//回调事件
const emit = defineEmits(["choseTagMethodReturn"]);
//初始参数
const props = defineProps({
	id: {
		type: String,
		required: false,
	},
	//对话框打开状态
	dialogIsOpen: {
		type: Boolean,
		required: true,
	},
	//选中的数据
	choseTagsId: {
		type: String,
		required: true
	}
})
//传输参数
const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
});
//标签数据集合
const tagList = ref([]);
//数据总数
const total = ref(0);
//选择的数据
const choseTags = ref([]);
//加载状态
const loading = ref(false);
//对话框打开状态
const dialogIsOpen = ref(false);
//名称
const dataNameSearch = ref("");
``
//初始化事件
onBeforeMount(() => {
	if (props.dialogIsOpen) {
		dialogIsOpen.value = props.dialogIsOpen;
	}
	if (props.choseTagsId) {
		choseTags.value = props.choseTagsId.indexOf(',') >= 0 ? props.choseTagsId.split(",") : props.choseTagsId.split(" ");
		console.log(choseTags.value);
	}
	selectList();
});

//查询数据集合
function selectList() {
	loading.value = true
	tagsManagement.getList({
		tagName: dataNameSearch.value,
		current: data.queryParams.current,
		size: data.queryParams.size
	}).then(res => {
		if (res.code === 200) {
			tagList.value = res.data.records;
			total.value = res.data.total;
			loading.value = false;
		}
	});
}

//关闭选择窗口
function closeDialog() {
	dialogIsOpen.value = false;
	emit("choseTagMethodReturn", props.choseTagsId ? [props.choseTagsId] : []);
}

//提交信息
function submitMethod() {
	dialogIsOpen.value = false;
	emit("choseTagMethodReturn", choseTags.value);
}

//标签选中
function tagChose() {
	console.log(choseTags.value);
}

//判断主题
function judgeTheme(type) {
	if (type === '1') {
		return {label: '深色', type: 'dark'};
	} else if (type === '2') {
		return {label: '浅色', type: 'light'};
	} else {
		return {label: '默认', type: 'plain'};
	}
}

//判断形状
function judgeShape(type) {
	if (type === '1') {
		return {label: '圆角', type: false};
	} else if (type === '2') {
		return {label: '椭圆', type: true};
	} else {
		return {label: '圆角', type: false};
	}
}

//判断颜色
function judgeColor(type) {
	if (type === '1') {
		return {label: '蓝色', type: ""};
	} else if (type === '2') {
		return {label: '绿色', type: 'success'};
	} else if (type === '3') {
		return {label: '灰色', type: 'info'};
	} else if (type === '4') {
		return {label: '红色', type: 'danger'};
	} else if (type === '5') {
		return {label: '橙色', type: 'warning'};
	} else {
		return {label: '蓝色', type: ""};
	}
}
</script>
<style scoped>
.dialogHeader {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: stretch;
	justify-content: space-evenly;
	align-items: center;
	width: 80%;
}

.dialogBody {
	margin-top: 10px;
}

.tagCard {
	width: 132px;
	height: 52px;
	background-color: #FFFFFF;
	border-radius: 5px;
	box-shadow: 0px 0px 6px lightgray;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: stretch;
	justify-content: space-evenly;
	align-items: center;
}

.selected {
	background-color: #ccc;
}
</style>
