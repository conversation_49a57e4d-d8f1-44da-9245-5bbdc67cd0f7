<template>
	<el-container>
		<el-main class="noPadding">
			<div v-if="!open" style="height: 100%;width: 100%">
				<el-tree ref="menu" :data="menuList" :expand-on-click-node="false" check-strictly default-expand-all
						 draggable highlight-current node-key="id" @node-click="menuClick">
					<template #default="{ data }">
							<span class="custom-tree-node">
								<span class="label">
									{{ data.name }}
								</span>
							</span>
					</template>
				</el-tree>
			</div>
			<div v-if="open" style="height: 100%;width: 100%">
				<el-container>
					<el-main class="noPadding">
						<comDoorTable @clickChild="clickEven"/>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<el-button type="primary" @click="() => collect()">确定</el-button>
						</div>
					</el-footer>
				</el-container>
			</div>
		</el-main>
	</el-container>
</template>
<script setup>
import {defineEmits, getCurrentInstance, onMounted, ref} from 'vue'
import classificationManagement from "@/api/archive/classificationManagement"

const {proxy} = getCurrentInstance()
const emit = defineEmits(["menuPropsList", "clickEvenListAdd", "changeTitle"]);
const menuList = ref([])
const menuLoading = ref(false);
// 点击树结构的id
const clickEvenListAdd = ref({})
// 是否选择分类
const open = ref(false);
const menuPropsList = ref({})

onMounted(() => {
	treeData()
});

// 查询树状列表
function treeData() {
	menuLoading.value = true;
	classificationManagement.treeData().then(res => {
		if (res.code === 200) {
			menuList.value = res.data;
			menuLoading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 点击查询分类列表
function menuClick(data) {
	if (data && data.parentId !== '0' && !data.children || data.children.length === 0) {
		open.value = true;
		menuPropsList.value = data;
		emit("changeTitle", "选择门类");
		// emit("menuPropsList",data);
	} else {
		proxy.msgError('表单只能选择最后一级');
	}
}

function clickEven(val) {
    if(!val.children || val.children.length === 0){
        clickEvenListAdd.value = val;
    }else{
        proxy.msgError('门类只能选择最后一级');
    }

}

function collect() {
    if(menuPropsList.value?.id && clickEvenListAdd.value?.id){
        emit("menuPropsList", menuPropsList.value);
        emit("clickEvenListAdd", clickEvenListAdd.value);
    } else {
        return proxy.msgError('请选择门类');
    }


}
</script>

<style scoped></style>
