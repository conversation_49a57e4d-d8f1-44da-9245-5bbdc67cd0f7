/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-06 15:41:20
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-07 11:16:24
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\utils\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}
export function deepClone(obj) {
  if (typeof obj !== "object" || obj == null) {
    return obj
  }
  let res
  if (obj instanceof Array) {
    res = []
  } else {
    res = {}
  }
  for ( let key in obj) {
    if (obj.hasOwnProperty(key)) {
      res[key] = deepClone(obj[key])
    }
  }
  return res
}
/**
 * 从 content-disposition 响应头中提取文件名
 * @param {string} contentDisposition - content-disposition 响应头的值
 * @returns {string|null} 提取的文件名，如果提取失败则返回 null
 */
export function extractFilenameFromContentDisposition(contentDisposition) {
  if (!contentDisposition) {
    return null
  }

  // 尝试匹配 filename*=UTF-8''encoded_filename 格式（RFC 5987）
  const utf8Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/)
  if (utf8Match && utf8Match[1]) {
    try {
      return decodeURIComponent(utf8Match[1])
    } catch (e) {
      console.warn('Failed to decode UTF-8 filename:', utf8Match[1])
    }
  }

  // 尝试匹配 filename="filename" 或 filename=filename 格式
  // 支持带引号和不带引号的格式，以及 URL 编码的文件名
  const fileNameMatch = contentDisposition.match(/filename\s*=\s*(?:"([^"]+)"|'([^']+)'|([^;\s]+))/)
  if (fileNameMatch) {
    // 获取匹配的文件名（可能在不同的捕获组中）
    const filename = fileNameMatch[1] || fileNameMatch[2] || fileNameMatch[3]
    if (filename) {
      try {
        // 尝试 URL 解码文件名
        return decodeURIComponent(filename)
      } catch (e) {
        console.warn('Failed to decode filename:', filename)
        // 如果解码失败，返回原始文件名
        return filename
      }
    }
  }

  return null
}

export function download(data,type,fileName){
  let blob = new Blob([data.data], { type });
      let url = window.URL.createObjectURL(blob);
      const link = document.createElement("a"); // 创建a标签
      link.href = url;
      link.download = fileName || extractFilenameFromContentDisposition(data.headers["content-disposition"]) || 'download'
      link.click();
      URL.revokeObjectURL(url); // 释放内
}

/**
 * @param {Function} func  防抖的回调
 * @param {number} wait  等待时间
 * @param {boolean} immediate   是否立即执行
 * @return {*}
 * 防抖函数
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}