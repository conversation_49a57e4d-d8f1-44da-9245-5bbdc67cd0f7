import { ElNotification, ElMessageBox } from 'element-plus';

/*
 * 弹框提示
 * @author: 路正宁
 * @date: 2023-03-21 11:55:52
 * @version: V1.0
*/
export default {
	/*
     * 成功提示
     * @author: 路正宁
     * @date: 2023-03-21 11:52:56
    */
    successNotie: function (message) {
		ElNotification.success({
            title: "提示",
            message: message,
            position: "top-right"
        });
	},
    /*
     * 警告提示
     * @author: 路正宁
     * @date: 2023-03-21 11:53:45
    */
    warnNotie: function (message) {
		ElNotification.warning({
            title: "警告",
            message: message,
            position: "top-right"
        });
	},
    /*
     * 提示信息
     * @author: 路正宁
     * @date: 2023-03-21 11:54:29
    */
    infoNotie: function (message) {
		ElNotification.info({
            title: "提示",
            message: message,
            position: "top-right"
        });
	},
    /*
     * 错误提示
     * @author: 路正宁
     * @date: 2023-03-21 11:45:12
    */
	errorNotie: function (message) {
		ElNotification.error({
            title: "错误",
            message: message,
            position: "top-right"
        });
	},
    show:function(){
        ElMessageBox.show('当前用户已被登出或无权限访问当前资源，请尝试重新登录后再操作。', '无权限访问', {
            type: 'error',
            closeOnClickModal: false,
            center: true,
            confirmButtonText: '确认',
            beforeClose: (action, instance, done) => {
                done()
            }
        })
    },
    confirm:function(){
        ElMessageBox.confirm('当前用户已被登出或无权限访问当前资源，请尝试重新登录后再操作。', '无权限访问', {
            type: 'error',
            closeOnClickModal: false,
            center: true,
            confirmButtonText: '重新登录',
            beforeClose: (action, instance, done) => {
                done()
            }
        }).then(() => {
            //router.replace({path: '/login'});
        }).catch(() => {})
    }
}
