import http from "@/utils/request"
//档案数据管理
export default {
	getList: function (params) {
		return http.get(
			'/archive/info/relationship/list',
			params
		)
	},
	getDetailsList: function (inputForm) {
		return http.get(
			'/archive/config/dataConfig/details/list',
			inputForm
		)
	},
	getAllTableList: function (params) {
		return http.get(
			'/archive/info/dataManager/getAllTableList',
			params
		)
	},
	dataConfigList: function (params) {
		return http.get(
			'/archive/config/dataConfig/treeData',
			params
		)
	},
	classifyList: function (params) {
		return http.get(
			'/archive/config/classifyConfig/treeData',
			params
		)
	},
	queryById: function (params) {
		return http.get(
			'/archive/info/relationship/queryById',
			params
		)
	},
	dataConfigQueryById: function (params) {
		return http.get(
			'/archive/config/dataConfig/queryById',
			params
		)
	},
	save: function (data) {
		return http.post(
			'/archive/info/relationship/save',
			data
		)
	},
	delete: function (params) {
		return http.delete(
			'/archive/info/relationship/delete',
			params
		)
	},
}
