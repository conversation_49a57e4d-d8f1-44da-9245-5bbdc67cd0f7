<template>
	<el-container
		style="padding: 15px;background-color: #FFFFFF;box-shadow: var(--el-box-shadow-light); border-radius: 3px;">
		<el-aside width="22%">
			<div style="height: 100%;
						width: 100%;
						display: flex;
						flex-flow: wrap;
						flex-direction: column;
						flex-wrap: nowrap;
						align-items: stretch;
						justify-content: space-between;
						padding: 10px 15px 0 10px;">
				<el-form ref="formList" :model="form" label-position="right">
					<el-form-item label="档案标签:" prop="tagInfo">
						<el-button plain type="primary" @click="openTagsChose = true">选择标签</el-button>
						<tagsListDialog v-if="openTagsChose" :choseTagsId="form.tagInfo" :dialogIsOpen="openTagsChose"
										@choseTagMethodReturn="choseTagMethodReturn"/>
					</el-form-item>
					<el-form-item label="档案年份:" prop="year">
						<el-date-picker v-model="form.year" clearable format="YYYY"
										placeholder="请选择档案年份" style="width: 100%" type="year" value-format="YYYY" @change="handleQuery"/>
					</el-form-item>
					<el-form-item label="档案全宗:" prop="boxGroupId">
						<el-select v-model="form.boxGroupId" clearable placeholder="请选择全宗" @change="handleQuery">
							<el-option v-for="item in fourConfig.group" :key="item.id"
									   :label="item.recordGroupName" :value="item.id"/>
						</el-select>
					</el-form-item>
					<el-form-item label="档案门类:" prop="boxCategoryId">
						<el-tree-select v-model="form.boxCategoryId" :data="fourConfig.category"
										:props="{ value: 'id', label: 'name',children: 'children',isLeaf: (data) => !data.children || data.children.length === 0 }"
										check-strictly clearable
                                        filterable
                                        highlight-current
										placeholder="请选择门类" @change="getCategoryFields">
							<template #default="{ data }">
								<span style="float: left;">{{ data.name }}</span>
							</template>
						</el-tree-select>
					</el-form-item>
					<!-- 动态查询字段 -->
					<template v-for="field in categoryFields" :key="field.id">
						<!-- 文本类型字段 -->
						<el-form-item v-if="field.type === 'text'" :label="field.remark + ':'" :prop="field.name"
									  class="dynamic-field">
							<el-input
								v-model="form[field.name]"
								:placeholder="`请输入${field.remark}`"
								@change="handleQuery"
							/>
						</el-form-item>

						<!-- 数字类型字段 -->
						<el-form-item v-if="field.type === 'number'" :label="field.remark + ':'" :prop="field.name"
									  class="dynamic-field">
							<el-input
								v-model="form[field.name]"
								:placeholder="`请输入${field.remark}`"
								@change="handleQuery"
								@input="prependParam(field, $event)"
							/>
						</el-form-item>

						<!-- 精确时间类型字段 -->
						<el-form-item v-if="field.type === 'date' && field.param === '1'" :label="field.remark + ':'"
									  :prop="field.name + 'Range'" class="dynamic-field">
							<el-date-picker
								v-model="form[field.name]"
								:placeholder="`请选择${field.remark}`"
								type="date"
								value-format="YYYY-MM-DD"
								@change="handleQuery"
							/>
						</el-form-item>

						<!-- 范围时间类型字段 -->
						<el-form-item v-if="field.type === 'date' && field.param === '2'" :label="field.remark + ':'"
									  :prop="field.name + 'Range'" class="dynamic-field">
							<el-date-picker
								v-model="form[field.name + 'Range']"
								:end-placeholder="`${field.remark}`"
								:start-placeholder="`${field.remark}`"
								range-separator="-"
								type="daterange"
								value-format="YYYY-MM-DD"
								@change="handleQuery"
							/>
						</el-form-item>
					</template>
				</el-form>
				<div style="width: 100%;display: flex;justify-content: flex-end;">
					<el-button icon="RefreshRight" plain @click="resetQuery">
						重置
					</el-button>
				</div>
			</div>
		</el-aside>
		<el-main style="padding: 0 0 0 10px">
			<el-container v-loading="searchLoading">
				<el-main style="padding: 0">
					<div v-if="searchHits && searchHits.length > 0" style="height: 100%;width: 100%;overflow-y: scroll;padding-right: 10px">
						<div v-for="(item, index) in searchHits" :key="index" class="retrieval_result infoBox">
							<div style="padding: 10px;">
								<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
										justify-content: flex-start;align-items: center;">
									<div style="line-height: 26px;font-size: 16px;font-weight: bold;"
										 v-html="item.name"></div>
									<div v-if="item.tagInfo">
										<el-tag v-for="tagInfo in choseTageInfo(item.tagInfo)"
												:effect="judgeTheme(tagInfo.tagTheme).type"
												:round="judgeShape(tagInfo.tagRound).type"
												:type="judgeColor(tagInfo.tagColor).type"
												auto-close="300"
												style="margin-left: 10px"
										>
											{{ tagInfo.tagName }}
										</el-tag>
									</div>
								</div>
								<div style="line-height: 26px;font-size: 12px;"
									 v-html="item.digestContent ? item.digestContent : '暂无档案摘要'"/>
								<div style="line-height: 26px;font-size: 12px;"
									 v-html="item.jsonDataStr ? '基本信息:' + item.jsonDataStr : '暂无基本信息'"/>
								<div style="display: flex;flex-direction: row;flex-wrap: nowrap;
										align-content: center;justify-content: flex-start;align-items: center;gap: 15px">
									<div style="line-height: 26px;font-size: 12px;">档号:
										{{ item.num ? item.num : "暂无" }}
									</div>
									<div style="line-height: 26px;font-size: 12px;">档案存址:
										{{ item.storageAddress ? item.storageAddress : "暂无" }}
									</div>
									<div style="line-height: 26px;font-size: 12px;">档案版本:
										{{ item.version ? item.version : "暂无" }}
									</div>
								</div>
							</div>
							<div style="width: 16%;display: flex;flex-direction: row;flex-wrap: nowrap;
									align-content: center;justify-content: space-around;align-items: center;">
								<div style="display: flex;align-items: center;margin-right: 12px">
									<el-button icon="Edit" link style="font-size: 25px;" type="warning"
											   @click="borrowEvent(item)"/>
									<el-button icon="ShoppingTrolley" link style="font-size: 25px;" type="primary"
											   @click="filesJoin(item)"/>
									<el-button round style="margin-right: 15px" type="primary"
											   @click="collectView(item)">
										详情
									</el-button>
								</div>
							</div>
						</div>
					</div>
                    <div v-else class="none-data">
                        <img src="@/assets/images/none.png">
                        <span>暂无数据</span>
                    </div>
				</el-main>
				<el-footer style="padding: 20px 10px 0 10px">
					<div style="display: flex;justify-content: flex-end">
						<pagination v-model:limit="queryParam.size" v-model:page="queryParam.current" :total="total"
									style="padding: 0" @pagination="handleQuery"/>
					</div>
				</el-footer>
			</el-container>
		</el-main>

		<!-- 查看 -->
		<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
			<viewFiles :receiveId="receiveId" @childMove="parentView"/>
		</el-dialog>
		<!-- 借阅 -->
		<el-dialog v-if="openBorrow" v-model="openBorrow" :title="title" append-to-body width="1300px">
			<Borrowing :handList="handList" @childMove="parentMove"/>
		</el-dialog>
	</el-container>
</template>

<script setup>
//档案标签选择弹窗
import tagsListDialog from "@/views/archiveReception/common/tagsListDialog.vue";
import {getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from "vue";
import retrievalList from "@/api/archive/retrieval";
import view from "@/api/archive/managementFile";
import completeManagement from "@/api/archive/systemConfiguration/completeManagement";
import category from "@/api/archive/categoryManagement/category";
import viewFiles from '@/views/archiveReception/view.vue';
import Borrowing from "@/views/retrieval/archivalRetrieval/borrowing.vue";
import tagsManagement from "@/api/archive/tagsManagement";
import tool from "@/utils/tool";

const {proxy} = getCurrentInstance();
const props = defineProps({
	searchString: {
		type: String,
		default: ''
	}
});
const emit = defineEmits(["returnBack", "shakeStatusChange"]);
//头部查询
const form = ref({
	year: null,
	boxGroupId: null,
	boxCategoryId: null,
	tagInfo: null
});
const data = reactive({
	queryParam: {
		current: 1,
		size: 10,
	},
	queryParams: {
		current: 1,
		size: -1,
	},
	fourConfig: {},
});
const categoryFields = ref([]); // 存储查询字段
const {queryParams, queryParam, fourConfig} = toRefs(data);
const total = ref(0);
const title = ref('');
const receiveId = ref({});
const handList = ref([]);
const searchHits = ref([]);
//标签信息集合
const tagsList = ref([]);
const formList = ref(null);
const openView = ref(false);
const openBorrow = ref(false);
const openTagsChose = ref(false);
const searchLoading = ref(false);
const paramValue = ref('');

watch(() => props.searchString, (newVal) => {
		// 避免重复请求
		if (newVal === '') {
			resetQuery();
		}
	}
);

defineExpose({
	handleQuery,
	resetQuery
});

onMounted(() => {
	getManagement();
	dataInfo();
	getTagsList();
});

function prependParam(field, event) {
	paramValue.value = field.param;
}

function snakeToCamel(str) {
	return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

// 重置
function resetQuery() {
	props.searchString = '';
	formList.value.resetFields();
	getCategoryFields();
	handleQuery();
}

function handleQuery() {
	searchLoading.value = true;
	// 初始化一个空数组来存储键值对
	const fieldPairs = [];
	// 添加动态字段到查询参数
	categoryFields.value.forEach(field => {
		const fieldValue = form.value[field.name];
		const camelCaseName = field.name; // 转换命名
		if (field.type === 'date' && field.param === '2') {
			const dateRange = form.value[field.name + 'Range'];
			if (dateRange && dateRange.length === 2) {
				const startDate = dateRange[0];
				const endDate = dateRange[1];
				fieldPairs.push(`${camelCaseName}Start:${startDate}`);
				fieldPairs.push(`${camelCaseName}End:${endDate}`);
			}
		}

		// 只添加有值的字段
		if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
			if (field.type === 'number') {
				const fieldParam = paramValue.value + fieldValue;
				fieldPairs.push(`${camelCaseName}:${fieldParam}`);
			} else {
				fieldPairs.push(`${camelCaseName}:${fieldValue}`);
			}

		}
	});
	const params = {
		tagManagerInfo: form.value.tagInfo,
		queryString: props.searchString ? props.searchString : null,
		current: queryParam.value.current,
		size: queryParam.value.size,
		year: form.value.year,
		'controlGroup.id': form.value.boxGroupId,
		'controlCategory.id': form.value.boxCategoryId,
		jsonData: fieldPairs.join(','),
	};
    searchHits.value = [];
	retrievalList.listByIndex(params).then(res => {
		if (res.code === 200) {
			searchHits.value = res.data.list;
			searchHits.value.forEach(item => {
				item.tagManagerInfo = tagsList.value.filter(tag => tag.id === item.tagInfo)[0];
			});
			total.value = res.data.total;
		} else if(res.code === 500){
            proxy.msgError(res.msg);
            total.value = 0;
        }
		searchLoading.value = false;
	}).catch(() => {
		searchLoading.value = false;
	});
}

// 新增获取字段方法
function getCategoryFields() {
	categoryFields.value = [];
	// 先清空现有字段
	const oldFields = categoryFields.value;

	// 清空表单中旧的动态字段值
	oldFields.forEach(field => {
		if (field && field.name && form.value.hasOwnProperty(field.name)) {
			delete form.value[field.name];
		}
	});
	category.getFieldsByCategory({
		id: form.value.boxCategoryId
	}).then(res => {
		if (res.code === 200) {
			categoryFields.value = Array.isArray(res.data) ? res.data : [];
		}
	}).catch(err => {
		console.error('字段获取失败:', err);
		data.categoryFields = [];
	});

	// 只有叶子节点才执行查询
	if (isLeafNode(form.value.boxCategoryId)) {
		handleQuery();
	}

}

// 新增方法：判断是否为叶子节点
function isLeafNode(nodeId) {
	// 遍历树结构查找节点
	const findNode = (nodes) => {
		for (const node of nodes) {
			if (node.id === nodeId) {
				return node;
			}
			if (node.children && node.children.length > 0) {
				const found = findNode(node.children);
				if (found) return found;
			}
		}
		return null;
	};

	const node = findNode(fourConfig.value.category);
	return node && (!node.children || node.children.length === 0);
}

//标签选择回调方法
function choseTagMethodReturn(data) {
	if (data.length > 0) {
		form.value.tagInfo = data.join(' ');
		openTagsChose.value = false;
		handleQuery();
	} else {
		form.value.tagInfo = null;
		openTagsChose.value = false;
		handleQuery();
	}
}

// 获取档案标签数据
function getTagsList() {
	tagsManagement.getList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			tagsList.value = res.data.records;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

//查询标签数据集合
function choseTageInfo(choseTagsId) {
	let split = choseTagsId.split(",");
	return tagsList.value.filter(record => {
		return split.includes(record.id);
	});
}

// 查询全宗
function getManagement() {
	completeManagement.getList(queryParams.value).then(res => {
		if (res.code === 200) {
			fourConfig.value.group = res.data.records;
		}
	});
}

// 查询门类
function dataInfo() {
	category.groupList().then(res => {
		if (res.code === 200) {
			fourConfig.value.category = res.data;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

//判断主题
function judgeTheme(type) {
	if (type === '1') {
		return {label: '深色', type: 'dark'};
	} else if (type === '2') {
		return {label: '浅色', type: 'light'};
	} else {
		return {label: '默认', type: 'plain'};
	}
}

//判断形状
function judgeShape(type) {
	if (type === '1') {
		return {label: '圆角', type: false};
	} else if (type === '2') {
		return {label: '椭圆', type: true};
	} else {
		return {label: '圆角', type: false};
	}
}

//判断颜色
function judgeColor(type) {
	if (type === '1') {
		return {label: '蓝色', type: ""};
	} else if (type === '2') {
		return {label: '绿色', type: 'success'};
	} else if (type === '3') {
		return {label: '灰色', type: 'info'};
	} else if (type === '4') {
		return {label: '红色', type: 'danger'};
	} else if (type === '5') {
		return {label: '橙色', type: 'warning'};
	} else {
		return {label: '蓝色', type: ""};
	}
}

// 借阅
function borrowEvent(val) {
	handList.value = val;
	title.value = '借阅';
	openBorrow.value = true;
}

// 档案加入
function filesJoin(val) {
    borrowSave(val.id);
}

function borrowSave(val) {
	retrievalList.save({
		info: {
			id: val
		},
		person: {
			id: tool.data.get('USER_INFO').id,
		}
	}).then(res => {
		if (res.code === 200) {
            proxy.msgSuccess('加入成功');
			emit("shakeStatusChange", "");
        }else if(res.code === 500){
            proxy.msgError(res.msg);
		}
	}).catch((err) => {
		proxy.msgError(err.msg);
	})
}

function collectView(val) {
	view.queryById({
		id: val.id,
		showDeleteInfo: true
	}).then(res => {
		if (res.code === 200) {
			receiveId.value = res.data;
			title.value = '查看';
			openView.value = true;
        }
	}).catch(() => {
		proxy.msgError('查看失败');
	})
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 取消借阅
function parentMove() {
	openBorrow.value = false;
}
</script>

<style scoped>
.retrieval_result {
	width: 100%;
	border-radius: 10px;
	border: 1px solid #dfe2e8;
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.infoBox:hover {
	box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.12);
}

/* 统一动态字段的标签宽度 */
.dynamic-field :deep(.el-form-item__label) {
	width: 73px !important; /* 根据标签长度调整 */
}

/* 强制所有输入组件撑满剩余宽度 */
.dynamic-field :deep(.el-input),
.dynamic-field :deep(.el-date-editor),
.dynamic-field :deep(.el-tree-select) {
	width: 100% !important; /* 与标签宽度匹配 */
}

/* 调整日期范围选择器宽度 */
.dynamic-field :deep(.el-date-editor--daterange) {
	width: 100% !important;
}
.none-data{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    height: 100%;
    color: #606266;
    img{
        width: 15%;
    }
}

</style>
