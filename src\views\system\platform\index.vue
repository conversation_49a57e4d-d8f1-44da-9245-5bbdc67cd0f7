<template>
  <el-container v-loading="listLoading">
    <el-header>
      <div class="left-panel">
        <el-button type="primary" icon="el-icon-plus" @click="addForm"></el-button>
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="selection.length == 0"
          @click="deleteDatas"
        ></el-button>
      </div>
      <div class="right-panel">
        <div class="right-panel-search">
          <el-input
            v-model="searchForm.name"
            placeholder="输入平台名称查询"
            clearable
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"></el-button>
        </div>
      </div>
    </el-header>
    <el-main class="noPadding">
		<ytzhTable
			ref="dataTable"
			:data="dataList"
			:pageChangeHandle="getDataList"
			:refreshDataListHandle="getDataList"
			:tablePage="tablePage"
			row-key="id"
			stripe
			@selection-change="selectionChange"
		>
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column label="平台名称" prop="name"></el-table-column>
        <el-table-column label="平台链接" prop="url"></el-table-column>
        <el-table-column label="操作" fixed="right" align="right" width="300">
          <template #default="scope">
            <el-button plain size="small" @click="viewForm(scope.row)">查看</el-button>
            <el-button type="primary" plain size="small" @click="editForm(scope.row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="deleteData(scope.row, scope.$index)"
            >
              <template #reference>
                <el-button plain type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ytzhTable>
    </el-main>
  </el-container>

  <form-dialog
    v-if="dialog.form"
    ref="formDialog"
    :callback="formCallback"
    @success="handleSaveSuccess"
    @closed="dialog.form = false"
  ></form-dialog>
</template>

<script>
import formDialog from "./form";

export default {
  name: "listCrud",
  components: {
    formDialog,
  },
  data() {
    return {
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
        //排序
        //orders: [{ column: "createDate", asc: false }],
      },
      //查询表单
      searchForm: {
        name: "",
        url: "",
      },
      //数据列选中行
      selection: [],
      //列表加载
      listLoading: false,
      //弹框控制
      dialog: {
        //数据表单显示隐藏
        form: false,
      },
    };
  },
  mounted() {
    //刷新数据列表
    this.getDataList();
  },
  methods: {
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //页面加载
      this.listLoading = true;
      this.dataList = null;
      var res = await this.$API.sysPlatformService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: this.tablePage.pageSize,
        //排序查询
        orders: this.tablePage.orders,
        ...this.searchForm,
      });
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.listLoading = false;
    },
    /*
     * 添加数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:27
     */
    addForm() {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.addView();
      });
    },
    /*
     * 编辑数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:41
     */
    editForm(row) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.editView(row);
      });
    },
    /*
     * 查看数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:55
     */
    viewForm(row) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.view(row);
      });
    },
    /*
     * 删除数据，行内删除
     * @author: 路正宁
     * @date: 2023-03-24 14:35:00
     */
    async deleteData(row, index) {
      this.listLoading = true;
      var res = await this.$API.sysPlatformService.delete(row.id);
      if (res.code == 200) {
        this.$refs.dataTable.removeIndex(index);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      this.listLoading = false;
    },
    /*
     * 批量删除
     * @author: 路正宁
     * @date: 2023-03-24 14:36:11
     */
    async deleteDatas() {
      //确认删除弹框
      var confirmRes = await this.$confirm(
        `确定删除选中的 ${this.selection.length} 项吗？`,
        "提示",
        {
          type: "warning",
          confirmButtonText: "删除",
          confirmButtonClass: "el-button--danger",
        }
      ).catch(() => {});
      //确认结果处理
      if (!confirmRes) {
        return false;
      }
      //要删除的id数组
      var ids = this.selection.map((v) => v.id);
      //拼接的数组字符串，接口传参
      var idStr = this.selection.map((v) => v.id).join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysPlatformService.delete(idStr);
      if (res.code == 200) {
        //从列表中移除已删除的数据
        this.$refs.dataTable.removeKeys(ids);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 表格选择后回调事件
     * @author: 路正宁
     * @date: 2023-03-24 14:51:09
     */
    selectionChange(selection) {
      this.selection = selection;
    },
    /*
     * 数据表单回调函数，表单提交成功以后会调用此方法
     * 为了减少网络请求，直接变更表格内存数据
     * @author: 路正宁
     * @date: 2023-03-24 14:57:49
     */
    formCallback(data, mode) {
      if (mode == "add") {
        this.$refs.dataTable.unshiftRow(data);
      } else if (mode == "edit") {
        this.$refs.dataTable.updateKey(data);
      }
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    search() {
      this.getDataList();
    },
  },
};
</script>

<style></style>
