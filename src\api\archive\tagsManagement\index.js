import http from "@/utils/request"
//档案数据管理
export default {
	getList: function (params) {
		return http.get(
			'/archive/sysConfig/tagsManager/list',
			params
		)
	},
	queryById: function (params) {
		return http.get(
			'/archive/sysConfig/tagsManager/queryById',
			params
		)
	},
	save: function (data) {
		return http.post(
			'/archive/sysConfig/tagsManager/save',
			data
		)
	},
	delete: function (params) {
		return http.delete(
			'/archive/sysConfig/tagsManager/delete',
			params
		)
	},
}
