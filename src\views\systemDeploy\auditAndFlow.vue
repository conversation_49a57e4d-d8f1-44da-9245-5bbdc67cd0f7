<template>
	<div style="padding: 10px">
		<!-- 审核主流程配置 -->
		<el-card class="box-card">
			<template #header>
				<div class="card-header">
					<span>审核主流程配置</span>
					<el-button class="button" plain icon="Plus" type="primary" @click="dialogFormVisible1 = true">新增</el-button>
				</div>
			</template>
			<div>
				<el-table v-loading="loading1" :data="data.flowData" highlight-current-row style="width: 100%" @row-click="(row) => details(row)" border>
					<el-table-column label="流程名称" prop="name" align="center"/>
					<el-table-column label="发起角色" prop="roles" align="center"/>
					<el-table-column label="对应表单名称" prop="forms" align="center"/>
					<el-table-column label="备注" prop="remark" align="center"/>
					<el-table-column label="操作" align="center" width="220">
						<template #default="scope">
							<el-button link type="primary" icon="Edit" @click="editBtn(scope.row)">编辑</el-button>
							<el-button link type="danger" icon="Delete" @click="delFlow(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-pagination
				v-model:current-page="data.pageNum"
				v-model:page-size="data.pageSize"
				:background="true"
				:total="data.total"
				layout="->,total, prev, pager, next, jumper"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-card>
		<!-- 审核步骤配置 -->
		<el-card class="box-card">
			<template #header>
				<div class="card-header">
					<span>审核步骤配置</span>
					<el-button v-show="dataTwo.newNodeFlag" plain icon="Plus" class="button" type="primary" @click="dialogFormVisible2 = true">新增</el-button>
				</div>
			</template>
			<div>
				<el-table v-loading="loading2" :cell-style="{ textAlign: 'center' }" :data="dataTwo.nodeData" :header-cell-style="{ 'text-align': 'center' }" style="width: 100%" border>
					<el-table-column label="步骤名称" prop="node_name" align="center"/>
					<el-table-column label="步骤数" prop="node_id" align="center"/>
					<el-table-column label="审核人" prop="users" align="center"/>
					<el-table-column label="备注" prop="remark" align="center"/>
					<el-table-column label="操作" align="center" width="220">
						<template #default="scope">
							<el-button link type="primary" icon="Edit" @click="editSteps(scope.row)">编辑</el-button>
							<el-button link type="danger" icon="Delete" @click="delStep(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-pagination
				v-model:current-page="dataTwo.pageNum"
				v-model:page-size="dataTwo.pageSize"
				:background="true"
				:total="dataTwo.total"
				layout="->,total, prev, pager, next, jumper"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange2"
			/>
		</el-card>
		<!-- ---------  -->

		<!-- 对话框Form表单 新增主流程配置-->
		<el-dialog
			v-model="dialogFormVisible1"
			title="新增主流程配置"
			width="500px"
		>
			<el-form
				ref="creatform"
				:label-position="labelPosition"
				:model="formLabelAlign1"
				:rules="creatRules"
				label-width="100px"
				style="max-width: 90%; margin-top: 10px"
			>
				<el-form-item label="流程名称" prop="name">
					<el-input
						v-model="formLabelAlign1.name"
						placeholder="请输入流程名称"
					/>
				</el-form-item>
				<el-form-item label="表单名称" prop="formId">
					<el-select
						v-model="formLabelAlign1.formId"
						class="m-2"
						placeholder="请选择对应表单名称"
						size="default"
						style="width: 100%"
					>
						<el-option
							v-for="(item, index) in data.typeData"
							:key="index"
							:label="item.name"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="发起角色" prop="roleId">
					<el-select
						v-model="formLabelAlign1.roleId"
						:reserve-keyword="false"
						allow-create
						default-first-option
						filterable
						multiple
						collapse-tags
						collapse-tags-tooltip
						placeholder="请选择发起角色"
						size="default"
						style="width: 100%"
					>
						<el-option
							v-for="(item, index) in data.roleData"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="formLabelAlign1.remark"
						:rows="5"
						maxlength="500"
						show-word-limit
						placeholder="请输入备注"
						type="textarea"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogFormVisible1 = false"
					>取消</el-button
					>
					<el-button type="primary" @click="flowAdd(creatform)">
						确定
					</el-button>
				</span>
			</template>
		</el-dialog>
		<!-- 对话框Form表单 主流程配置编辑-->
		<el-dialog
			v-model="dialogFormVisible3"
			title="主流程配置编辑"
			width="500px"
		>
			<el-form
				ref="creatform3"
				:label-position="labelPosition"
				:model="data.editStr"
				:rules="creatRules3"
				label-width="100px"
				style="max-width: 90%; margin-top: 10px"
			>
				<el-form-item label="流程名称" prop="name">
					<el-input
						v-model="data.editStr.name"
						placeholder="请输入流程名称"
					/>
				</el-form-item>
				<el-form-item label="表单名称" prop="formIds">
					<el-select
						v-model="data.editStr.formIds"
						class="m-2"
						placeholder="请选择对应表单名称"
						size="default"
						style="width: 100%"
					>
						<el-option
							v-for="(item, index) in data.typeData"
							:key="index"
							:label="item.name"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="发起角色" prop="roleIds">
					<el-select
						v-model="data.editStr.roleIds"
						:reserve-keyword="false"
						allow-create
						default-first-option
						filterable
						multiple
						collapse-tags
						collapse-tags-tooltip
						placeholder="请选择发起角色"
						size="default"
						style="width: 100%"
					>
						<el-option
							v-for="(item, index) in data.roleData"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="data.editStr.remark"
						:rows="5"
						maxlength="500"
						show-word-limit
						placeholder="请输入备注"
						type="textarea"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogFormVisible3 = false"
					>取消</el-button
					>
					<el-button type="primary" @click="editSave(creatform3)">
						确定
					</el-button>
				</span>
			</template>
		</el-dialog>
		<!-- 对话框Form表单 新增步骤配置-->
		<el-dialog
			v-model="dialogFormVisible2"
			title="新增步骤配置"
			width="500px"
		>
			<el-form
				ref="creatform2"
				:label-position="labelPosition"
				:model="formLabelAlign2"
				:rules="creatRules2"
				label-width="100px"
				style="max-width: 90%; margin-top: 10px"
			>
				<el-form-item label="所属主流程">
					<b>{{ data.detailsStr.name }}</b>
				</el-form-item>
				<el-form-item label="步骤名称" prop="name">
					<el-input
						v-model="formLabelAlign2.name"
						placeholder="请输入步骤名称"
					/>
				</el-form-item>

				<el-form-item label="组织机构" prop="institution">
					<el-select
						v-model="formLabelAlign2.institution"
						class="m-2"
						placeholder="请选择组织机构"
						size="default"
					>
						<el-option
							v-for="(item, index) in dataTwo.tissueType"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="审核人" prop="users">
					<el-select
						v-model="formLabelAlign2.users"
						:disabled="formLabelAlign2.institution == ''"
						:reserve-keyword="false"
						allow-create
						default-first-option
						filterable
						multiple
						placeholder="请选择审核人"
						size="default"
						style="width: 100%"
					>
						<el-input
							v-model="staffDs.value"
							:prefix-icon="Search"
							class="w-50 m-2"
							placeholder="输入审核人"
							@keydown.enter="getStaff()"
						/>
						<el-option
							v-for="(item, index) in dataTwo.staffType"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
						<el-pagination
							v-model:current-page="staffDs.pageNum"
							v-model:page-size="staffDs.pageSize"
							:total="staffDs.total"
							layout="->,total, prev, pager, next, jumper"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange3"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="审核角色">
					<el-select
						v-model="formLabelAlign2.roles"
						:disabled="formLabelAlign2.users == ''"
						class="m-2"
						placeholder="请选择审核角色"
						size="default"
					>
						<el-option
							v-for="(item, index) in data.roleData"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="formLabelAlign2.remark"
						:rows="5"
						placeholder="请输入备注"
						type="textarea"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogFormVisible2 = false"
					>取消</el-button
					>
					<el-button type="primary" @click="stepsBtn(creatform2)">
						确定
					</el-button>
				</span>
			</template>
		</el-dialog>
		<!-- 对话框Form表单 编辑步骤配置-->
		<el-dialog
			v-model="dialogFormVisible4"
			title="编辑步骤配置"
			width="500px"
		>
			<el-form
				ref="creatform4"
				:label-position="labelPosition"
				:model="staffDs.editStr"
				:rules="creatRules4"
				label-width="100px"
				style="max-width: 90%; margin-top: 10px"
			>
				<el-form-item label="所属主流程">
					<b>{{ data.detailsStr.name }}</b>
				</el-form-item>
				<el-form-item label="步骤数">
					<b>{{ staffDs.editStr.node_id }}</b>
				</el-form-item>
				<el-form-item label="步骤名称" prop="node_name">
					<el-input
						v-model="staffDs.editStr.node_name"
						placeholder="请输入步骤名称"
					/>
				</el-form-item>

				<el-form-item label="组织机构" prop="institution">
					<el-select
						v-model="staffDs.editStr.institution"
						class="m-2"
						placeholder="请选择组织机构"
						size="default"
						style="width: 100%"
					>
						<el-option
							v-for="(item, index) in dataTwo.tissueType"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="审核人" prop="userIds">
					<el-select
						v-model="staffDs.editStr.userIds"
						:disabled="staffDs.editStr.institution === ''"
						:reserve-keyword="false"
						allow-create
						default-first-option
						filterable
						multiple
						collapse-tags
						collapse-tags-tooltip
						placeholder="请选择审核人"
						size="default"
						style="width: 100%"
					>
						<el-input
							v-model="staffDs.value"
							:prefix-icon="Search"
							placeholder="输入审核人"
							@keydown.enter="getStaff(null)"
						/>
						<el-option
							v-for="(item, index) in dataTwo.staffType"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="审核角色">
					<el-select
						v-model="staffDs.editStr.roleIds"
						:disabled="staffDs.editStr.users == ''"
						class="m-2"
						placeholder="请选择审核角色"
						size="default"
						style="width: 100%"
					>
						<el-option
							v-for="(item, index) in data.roleData"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="staffDs.editStr.remark"
						:rows="5"
						maxlength="500"
						show-word-limit
						placeholder="请输入备注"
						type="textarea"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogFormVisible4 = false"
					>取消</el-button
					>
					<el-button type="primary" @click="editStepsBtn(creatform4)">
						确定
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watch, watchEffect,} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import {flows, node} from "@/api/model/systemDeploy/auditAndFlow/index";
import {Search} from "@element-plus/icons-vue";
/**
 * 仓库
 */
/**
 * 路由对象
 */
/**
 * 路由实例
 */
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const dialogFormVisible1 = ref(false);
const dialogFormVisible2 = ref(false);
const dialogFormVisible3 = ref(false);
const dialogFormVisible4 = ref(false);
const labelPosition = ref("right"); //表格方向
const loading1 = ref(false);
const loading2 = ref(false);
const data = reactive({
	flowData: [],
	roleData: [], //角色数据
	typeData: [], // 表单名称数据
	pageSize: 4, //每页条数
	pageNum: 1, //流程 当前页码
	total: 0, //总数据数量
	editStr: {}, //流程编辑数据
	stepData: [],
	detailsStr: {},
});
const dataTwo = reactive({
	nodeData: [],
	total: 0,
	pageNum: 1,
	pageSize: 4,
	newNodeFlag: false,
	tissueType: [],
	staffType: [],
});
const formLabelAlign1 = reactive({
	name: "",
	remark: "",
	roleId: "",
	formId: "",
});
const formLabelAlign2 = reactive({
	name: "",
	remark: "",
	roles: "",
	users: "",
	auditProcessId: "",
	institution: "",
});

const staffDs = reactive({
	id: "",
	pageNum: 1,
	size: 10,
	total: 0,
	value: "",
	editStr: {},
});

const creatform = ref(); //验证表单/流程新增
const creatRules = reactive({
	name: [{required: true, message: "请输入流程名称", trigger: "blur"}],
	formId: [{required: true, message: "请选择对应表单", trigger: "blur"}],
	roleId: [{required: true, message: "请选择发起角色", trigger: "blur"}],
});
const creatform2 = ref(); //验证表单//步骤新增
const creatRules2 = reactive({
	name: [{required: true, message: "请输入步骤名称", trigger: "blur"}],
	institution: [
		{required: true, message: "请选择组织机构", trigger: "blur"},
	],
	users: [{required: true, message: "请选择审核人", trigger: "blur"}],
});
const creatform3 = ref(); //验证表单//步骤新增
const creatRules3 = reactive({
	name: [{required: true, message: "请输入流程名称", trigger: "blur"}],
	formIds: [{required: true, message: "请选择对应表单", trigger: "blur"}],
	roleIds: [{required: true, message: "请选择发起角色", trigger: "blur"}],
});
const creatform4 = ref(); //验证表单//步骤新增
const creatRules4 = reactive({
	node_name: [{required: true, message: "请输入流程名称", trigger: "blur"}],
	userIds: [{required: true, message: "请选择审核人", trigger: "blur"}],
	roleIds: [{required: true, message: "请选择审核角色", trigger: "blur"}],
});

watch(
	() => formLabelAlign2.institution,
	(newValue) => {
		staffDs.id = newValue;
		node.staff({"sysOrg.id": newValue}).then((res) => {
			dataTwo.staffType = res.data.records;
			formLabelAlign2.users = [];
			staffDs.total = res.data.total;
		});
	}
);
watch(
	() => staffDs.editStr.institution,
	(newValue) => {
		staffDs.id = newValue;
		node.staff({"sysOrg.id": newValue}).then((res) => {
			dataTwo.staffType = res.data.records;
			staffDs.editStr.userIds = [];
			staffDs.total = res.data.total;
		});
	}
);
const delStep = (row) => {
	ElMessageBox.confirm("确认删除此步骤配置?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			node.delNode({ids: row.id})
				.then((res) => {
					if (res.code == 200) {
						ElMessage({
							message: "删除成功",
							type: "success",
						});
						details(data.detailsStr);
					} else {
						ElMessage.error("删除失败，请稍后重试");
					}
				})
				.catch(() => {
					ElMessage.error("删除失败，请稍后重试");
				});
		})
		.catch(() => {
		});
};
const editSteps = (row) => {
	dialogFormVisible4.value = true;
	staffDs.editStr = {...row};
	staffDs.editStr.userIds = row.userIds.split(",");
	getStaff(null);
};
const editStepsBtn = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			node.saveNode({
				id: staffDs.editStr.id,
				auditProcessId: data.detailsStr.id,
				nodeName: staffDs.editStr.node_name,
				remark: staffDs.editStr.remark,
				auditorId: staffDs.editStr.userIds.toString(),
				auditorRoleUser: staffDs.editStr.roleIds,
			})
				.then((res) => {
					if (res.code == 200) {
						details(data.detailsStr);
						ElMessage({
							message: "编辑成功",
							type: "success",
						});
						dialogFormVisible4.value = false;
					}
				})
				.catch(() => {
					ElMessage.error("编辑失败，请稍后重试");
				});
		}
	});
};
//主流程删除
const delFlow = (row) => {
	ElMessageBox.confirm("确认删除此主流程配置?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			flows
				.delFLow({ids: row.id})
				.then((res) => {
					if (res.code == 200) {
						ElMessage({
							message: "删除成功",
							type: "success",
						});
						if (data.detailsStr.id == row.id) {
							dataTwo.nodeData = [];
							dataTwo.newNodeFlag = false;
						}
						getFlowData();
					} else {
						ElMessage.error("删除失败，请稍后重试");
					}
				})
				.catch(() => {
					ElMessage.error("删除失败，请稍后重试");
				});
		})
		.catch(() => {
		});
};

const details = (row) => {
	data.detailsStr = {...row};
	dataTwo.newNodeFlag = true;
	loading2.value = true;
	node.nodeList({
		"auditProcess.id": row.id,
		current: dataTwo.pageNum,
		size: dataTwo.pageSize,
	}).then((res) => {
		dataTwo.nodeData = res.data.records;
		dataTwo.total = res.data.total;
		loading2.value = false;
	});
};
const handleCurrentChange = (val) => {
	data.pageNum = val;
	getFlowData();
};
const handleCurrentChange2 = (val) => {
	dataTwo.pageNum = val;
	details(data.detailsStr);
};
const handleCurrentChange3 = (val) => {
	staffDs.pageNum = val;
	getStaff();
};
const getStaff = (id) => {
	node.staff({
		"sysOrg.id": staffDs.id,
		current: 1,
		size: -1,
		name: staffDs.value,
		id: id,
	}).then((res) => {
		staffDs.total = res.data.total;
		dataTwo.staffType = res.data.records;
		console.log(id, 'id');
		console.log(dataTwo.staffType, 'staffType');
	});
};
const handleSizeChange = () => {
	// console.log(`${val} items per page`)
};
const editBtn = (row) => {
	dialogFormVisible3.value = true;
	data.editStr = {...row};
	data.editStr.roleIds = row.roleIds.split(",");
};
const editSave = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			flows
				.saveFlow({
					id: data.editStr.id,
					name: data.editStr.name,
					remark: data.editStr.remark,
					auditor: data.editStr.roleIds.toString(),
					formId: data.editStr.formIds,
				})
				.then((res) => {
					if (res.code == 200) {
						ElMessage({
							message: "编辑成功",
							type: "success",
						});
						getFlowData();
						dialogFormVisible3.value = false;
					} else {
						ElMessage.error(res.msg);
					}
				});
		}
	});
};
const stepsBtn = async (formEl) => {
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			node.saveNode({
				auditProcessId: data.detailsStr.id,
				nodeName: formLabelAlign2.name,
				remark: formLabelAlign2.remark,
				auditorId: formLabelAlign2.users.toString(),
				auditorRoleUser: formLabelAlign2.roles,
			})
				.then((res) => {
					if (res.code == 200) {
						details(data.detailsStr);
						ElMessage({
							message: "添加成功",
							type: "success",
						});
						dialogFormVisible2.value = false;
					}
				})
				.catch(() => {
					ElMessage.error("添加失败，请稍后重试");
				});
		}
	});
};
const flowAdd = async (formEl) => {
	// dialogFormVisible2.value = false
	if (!formEl) return;
	await formEl.validate((valid) => {
		if (valid) {
			flows
				.saveFlow({
					name: formLabelAlign1.name,
					remark: formLabelAlign1.remark,
					auditor: formLabelAlign1.roleId.toString(),
					formId: formLabelAlign1.formId,
				})
				.then((res) => {
					if (res.code == 200) {
						ElMessage({
							message: "添加成功",
							type: "success",
						});
						getFlowData();
						dialogFormVisible1.value = false;
						formLabelAlign1.name = "";
						(formLabelAlign1.remark = ""),
							(formLabelAlign1.roleId = ""),
							(formLabelAlign1.formId = "");
					} else {
						ElMessage.error(res.msg);
					}
				})
				.catch(() => {
					ElMessage.error("添加失败，请稍后重试");
				});
		}
	});
};
const getFlowData = () => {
	loading1.value = true;
	flows
		.flowData({
			current: data.pageNum,
			size: data.pageSize,
		})
		.then((res) => {
			loading1.value = false;
			if (res.code == 200) {
				data.flowData = res.data.records;
				data.total = res.data.total;
			}
		});
};
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	getFlowData();
	flows.role(
		{
			size: 1000
		}
	).then((res) => {
		data.roleData = res.data.records;
	});
	flows
		.formData({
			dictType: "audit_form",
			size: 1000
		})
		.then((res) => {
			data.typeData = res.data.records;
		});
	node.tissue().then((res) => {
		dataTwo.tissueType = res.data.records;
	});
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	...toRefs(data),
});
</script>
<style lang="scss" scoped>
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.text {
	font-size: 14px;
}

.item {
	margin-bottom: 18px;
}

.el-pagination {
	margin-top: 20px;
}

.box-card {
	margin: 0 0 10px 0;
}

.el-table__body tr.current-row > td {
	background-color: #69a8ea !important;
	color: #fff;
}

.el-select-dropdown__list {
	.el-input {
		width: 90%;
		margin-left: 5%;
		margin-bottom: 10px;
	}

	.el-pagination {
		margin-right: 20px;
	}
}
</style>
