import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/storage/erpGoodsShelves/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/storage/erpGoodsShelves/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/storage/erpGoodsShelves/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/storage/erpGoodsShelves/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/storage/erpGoodsShelves/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/storage/erpGoodsShelves/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/storage/erpGoodsShelves/import',
      data
    )
  }
}
