import http from "@/utils/request"

export default {
  //出库记录
	//出库记录列表
  list: function (params) {
    return http.get('/archive/warehouse/outInStorage/list', params)
  },
	//保存出库记录
  save: function (params) {
    return http.post('/archive/warehouse/outInStorage/save', params)
  },
  //删除出库记录
  delete: function (ids) {
    return http.delete('/archive/warehouse/outInStorage/delete', ids)
  },
   //出库记录详情
   detail: function (ids) {
	   return http.get('/archive/warehouse/outInStorage/queryById', ids)
  },

	//入库记录
	//入库记录列表
  enterlist: function (params) {
    return http.get('/archive/warehouse/putInStorage/list', params)
  },
	//保存入库记录
  entersave: function (params) {
    return http.post('/archive/warehouse/putInStorage/save', params)
  },
  //删除入库记录
  enterdelete: function (ids) {
    return http.delete('/archive/warehouse/putInStorage/delete', ids)
  },
   //入库记录详情
  enterdetail: function (ids) {
	  return http.get('/archive/warehouse/putInStorage/queryById', ids)
  },

  //上架记录列表
  upRecords: function (params) {
    return http.get('/archive/info/box/up/list', params)
  },
  //上架记录详情
  updetail: function (ids) {
    return http.get('/archive/info/box/up/queryById', ids)
  },
   //下架记录列表
   downRecords: function (params) {
    return http.get('/archive/info/box/down/list', params)
  },
  //下架记录详情
  downdetail: function (ids) {
    return http.get('/archive/info/box/down/queryById', ids)
  },
  //档案详情
  archivesdetail: function (ids) {
    return http.get('/archive/info/main/list', ids)
  },
}
