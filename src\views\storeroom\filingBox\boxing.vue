<template>
	<el-dialog v-if="props.openStatus" v-model="props.openStatus" :close-on-click-modal="false" :close-on-press-escape="false" :title="props.title"
			   align-center append-to-body destroy-on-close width="72%"
			   @close="cancellation">
		<el-container style="height: 72vh">
			<el-header style="padding-top: 0">
				<div>
					<el-form ref="formList" :inline="true" :model="form" label-position="left" label-width="auto">
						<el-form-item label="档案名称" prop="name" style="margin-bottom: 0;">
							<el-input v-model="form.name" placeholder="请输入档案名称"/>
						</el-form-item>
						<el-form-item label="档案号" prop="numFormat" style="margin-bottom: 0;">
							<el-input v-model="form.numFormat" placeholder="请输入档案号"/>
						</el-form-item>
						<el-form-item style="margin-bottom: 0;">
							<el-button icon="Search" type="primary" @click="handleQuery">
								查询
							</el-button>
							<el-button icon="RefreshRight" plain @click="resetQuery">
								重置
							</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-header>
			<el-main ref="main" v-loading="loading" class="noPadding">
				<el-table :data="receiveData" border height="100%" @selection-change="handleSelectionChange">
					<el-table-column align="center" min-width="30" type="selection" width="50"/>
					<el-table-column align="center" label="档案名称" prop="name"/>
					<el-table-column align="center" label="档案号" prop="num">
						<template #default="scope">
							{{ scope.row.num ? scope.row.num : '暂无' }}
						</template>
					</el-table-column>
					<el-table-column align="center" label="档案年份" prop="year" width="80"/>
					<el-table-column align="center" label="档案月份" prop="month" width="80"/>
					<el-table-column align="center" label="档案创建时间" prop="createDate" width="180">
						<template #default="scope">
							{{
								(scope.row.createDate ? moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') : undefined) || '--'
							}}
						</template>
					</el-table-column>
					<el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="100px">
						<template #default="scope">
							<el-button link type="primary" @click="collectFile(scope.row)">
								查看
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-main>
			<el-footer>
				<div style="float: right;">
					<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
								:total="total" style="padding: 0" @pagination="getList"/>
				</div>
			</el-footer>

			<!-- 查看 -->
			<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="8vh" width="92%">
				<detailsOfArchives :receiveId="receiveId" @childMove="parentView"></detailsOfArchives>
			</el-dialog>
		</el-container>
		<template #footer>
			<el-button plain @click="cancellation">
				取消
			</el-button>
			<el-button v-if="!props.handList.id" type="primary" @click="collectRemove">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import boxList from '@/api/archive/storeroom/filingBox';
import {defineProps, getCurrentInstance, reactive, ref, toRefs, watch} from 'vue'
import moment from 'moment';
import detailsOfArchives from '@/components/DetailsOfArchives/index.vue';

const emit = defineEmits(["closeMethod"]);
const props = defineProps({
	title: {
		type: String,
		default: ''
	},
	openStatus: {
		type: Boolean,
		default: false
	},
	handList: {
		type: Array,
		default: []
	},
	clickEvenId: {
		type: Array,
		default: []
	}
})
const loading = ref(false)
const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	},
	fourConfig: {},
})
const total = ref(0)
const {queryParams} = toRefs(data)
const {proxy} = getCurrentInstance()
const receiveData = ref([])
const form = ref([])
const receiveId = ref('')
const title = ref('')
const openView = ref(false)
const handData = ref([])

watch(() => props.openStatus, (newData) => {
	if (newData) {
		getList();
	}
});

function handleQuery() {
    queryParams.value.current = 1;
    getList();
}

// 重置
function resetQuery() {
	form.value = [];
	getList();
}

// 取消
function cancellation() {
	emit("closeMethod");
}

function collectFile(val) {
	title.value = '查看';
	receiveId.value = val;
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 选中表格
function handleSelectionChange(val) {
	handData.value = val;
}

// 拆盒时移除档案
function collectRemove() {
	if (handData.value.length > 0) {
		proxy.$confirm('是否要移除档案?', '提示', {
			type: 'warning',
			confirmButtonText: "确定",
			cancelButtonText: "取消",
		}).then(() => {
			let idStr = handData.value.map((v) => v.id);
			//拼接的数组字符串，接口传参
			let ids = idStr.join(",");
			boxList.dismantleInfo({
				recordIds: ids
			}).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess('移除成功');
					cancellation();
					getList();
				}
			}).catch((err) => {
				console.log(err);
				proxy.msgError('移除失败');
			})
		}).catch((err) => {
			console.log(err);
		})
	} else {
		proxy.msgError('请先选择需要移除的数据！');
	}
}

// 进入时查询全部
function getList() {
	let id = '';
	if (props.handList.id) {
		id = props.handList.id
	} else {
		id = props.handList[0].id
	}
    loading.value = true;
    receiveData.value = [];
	boxList.mainList({
		current: queryParams.value.current,
		size: queryParams.value.size,
        name: form.value.name,
        numFormat: form.value.numFormat,
		'box.id': id
	}).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
			loading.value = false;
			total.value = res.data.total;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}
</script>

<style scoped>

</style>
