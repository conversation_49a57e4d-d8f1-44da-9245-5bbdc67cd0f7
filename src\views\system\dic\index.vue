<template>
	<el-container>
		<el-aside v-loading="menuLoading" width="42%">
			<el-container>
				<el-header>
					<el-input
						v-model="menuFilterText"
						clearable
						placeholder="输入关键字进行过滤"
					></el-input>
				</el-header>
				<el-main class="noPadding">
					<el-tree
						ref="menu"
						:data="menuList"
						:expand-on-click-node="false"
						:filter-node-method="menuFilterNode"
						:props="menuProps"
						check-strictly
						class="menu"
						draggable
						highlight-current
						node-key="id"
						show-checkbox
						@node-click="menuClick"
						@node-drop="nodeDrop"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
								  	{{ node.label }}
								</span>
								<span class="code">{{ data.code }}</span>
								<span class="do">
								  	<el-button-group>
										<el-button
											icon="el-icon-plus"
											size="small"
											@click.stop="addDicType(node)">
										</el-button>
										<el-button
											icon="el-icon-edit"
											size="small"
											@click.stop="editDicType(node, data)">
										</el-button>
										<el-button
											icon="el-icon-delete"
											size="small"
											@click.stop="delDicType(node)">
										</el-button>
								  </el-button-group>
								</span>
							  </span>
						</template>
					</el-tree>
				</el-main>
				<el-footer style="height: 51px">
					<el-button
						icon="el-icon-plus"
						size="small"
						type="primary"
						@click="addDicType(null)"
					></el-button>
					<el-button
						icon="el-icon-delete"
						plain
						size="small"
						type="danger"
						@click="delDicTypes"
					></el-button>
				</el-footer>
			</el-container>
		</el-aside>
		<el-container v-loading="listLoading" class="is-vertical">
			<el-header>
				<div class="left-panel">
					<el-button icon="el-icon-plus" type="primary" @click="addForm"></el-button>
					<el-button
						:disabled="selection.length == 0"
						icon="el-icon-delete"
						plain
						type="danger"
						@click="deleteDatas"
					></el-button>
				</div>
			</el-header>
			<el-main class="noPadding">
				<ytzhTable
					ref="dataTable"
					:data="dictValueList"
					:pageChangeHandle="getdicValueList"
					:refreshDataListHandle="getdicValueList"
					:tablePage="tablePage"
					:hide-do="true"
					row-key="id"
					stripe
					@selection-change="selectionChange"
				>
					<el-table-column type="selection" width="50"></el-table-column>
					<!-- <el-table-column label="" width="60">
					  <template #default>
						<el-tag class="move" style="cursor: move"
						  ><el-icon-d-caret style="width: 1em; height: 1em"
						/></el-tag>
					  </template>
					</el-table-column> -->
					<el-table-column label="名称" prop="name"></el-table-column>
					<el-table-column align="center" label="编码" prop="code" width="150"></el-table-column>
					<el-table-column align="center" label="值" prop="value" width="150"></el-table-column>
					<!-- <el-table-column label="是否有效" prop="yx" width="100">
					  <template #default="scope">
						<el-switch
						  v-model="scope.row.yx"
						  @change="changeSwitch($event, scope.row)"
						  :loading="scope.row.$switch_yx"
						  active-value="1"
						  inactive-value="0"
						></el-switch>
					  </template>
					</el-table-column> -->
					<el-table-column align="center" fixed="right" label="操作" width="120">
						<template #default="scope">
							<el-button-group>
								<el-button size="small" text type="primary" @click="editForm(scope.row)">
									编辑
								</el-button>
								<el-popconfirm title="确定删除吗？" @confirm="deleteData(scope.row, scope.$index)">
									<template #reference>
										<el-button size="small" text type="primary">删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</ytzhTable>
			</el-main>
		</el-container>
	</el-container>
	<dic-type-dialog
		v-if="dicTypeDialogDisplay"
		ref="dicTypeDialog"
		@closed="dicTypeDialogDisplay = false"
		@success="handleDicSuccess"
	></dic-type-dialog>
	<form-dialog
		v-if="formDialogDisplay"
		ref="formDialog"
		:callback="formCallback"
		@closed="formDialogDisplay = false"
		@success="handleSaveSuccess"
	></form-dialog>
</template>

<script>
import dicTypeDialog from "./dic";
import formDialog from "./form";

export default {
	components: {
		dicTypeDialog,
		formDialog,
	},
	data() {
		return {
			//字典类型表单
			dicTypeDialogDisplay: false,
			//字典数据表单
			formDialogDisplay: false,
			//字典类型加载中
			menuLoading: false,
			//字典类型列表
			menuList: [],
			menuProps: {
				label: (data) => {
					return data.name;
				},
			},
			//当前选中的字典类型
			currentDicType: {},
			//字典类型筛选属性
			menuFilterText: "",
			//数据列表
			dictValueList: [],
			//分页参数
			tablePage: {
				//数据总数
				total: 0,
				//当前页码
				currentPage: 1,
				//每页条数
				pageSize: 10,
			},
			//查询表单
			searchForm: {
				name: "",
			},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
		};
	},
	watch: {
		//字典类型过滤，顶部搜索框
		menuFilterText(val) {
			this.$refs.menu.filter(val);
		},
	},
	mounted() {
		this.getMenu();
	},
	methods: {
		/*
		 * 加载字典类型数据
		 * @author: 路正宁
		 * @date: 2023-03-23 11:22:39
		 */
		async getMenu() {
			this.menuLoading = true;
			var res = await this.$API.sysDictTypeService.treeData();
			if (res.code == 200) {
				this.menuList = res.data;
			} else {
				this.$Response.errorNotice(res, "字典查询失败");
			}
			this.menuLoading = false;
		},
		/*
		 * 字典类型点击
		 * @author: 路正宁
		 * @date: 2023-03-23 11:23:10
		 */
		menuClick(data, node) {
			this.currentDicType = data;
			this.getdicValueList();
			node;
		},
		/*
		 * 字典类型搜索，字典类型顶部搜索框
		 * @author: 路正宁
		 * @date: 2023-03-23 11:27:25
		 */
		menuFilterNode(value, data) {
			if (!value) return true;
			var targetText = data.name;
			return targetText.indexOf(value) !== -1;
		},
		/*
		 * 字典类型拖拽移动
		 * @author: 路正宁
		 * @date: 2023-03-23 11:11:01
		 */
		async nodeDrop(draggingNode, dropNode, dropType) {
			var draggingNodeId, dropNodeId, sort;
			//拖拽对象ID
			draggingNodeId = draggingNode.data.id;
			//释放对象ID
			dropNodeId = dropNode.data.id;
			if (dropType === "before") {
				//之前
				sort = dropNode.data.sort + 1;
				draggingNode.data.sort = sort;
			} else if (dropType === "after") {
				//之后
				sort = dropNode.data.sort - 1;
				draggingNode.data.sort = sort;
			} else {
				sort = 100;
			}
			//排序接口调用
			var res = await this.$API.sysDictTypeService.updateSort(
				draggingNodeId,
				dropNodeId,
				dropType,
				sort
			);
			//处理结果
			if (res.code == 200) {
				draggingNode.data = res.data;
				this.$message.success("排序成功");
			} else {
				this.$Response.errorNotice(res, "排序失败");
			}
		},
		/*
		 * 添加字典类型
		 * @author: 路正宁
		 * @date: 2023-03-23 11:24:13
		 */
		async addDicType(parentNode) {
			this.dicTypeDialogDisplay = true;
			this.$nextTick(() => {
				this.$refs.dicTypeDialog.addView(parentNode);
			});
		},
		/*
		 * 编辑字典类型
		 * @author: 路正宁
		 * @date: 2023-03-28 16:32:58
		 */
		async editDicType(parentNode, data) {
			this.dicTypeDialogDisplay = true;
			this.$nextTick(() => {
				this.$refs.dicTypeDialog.editView(parentNode, data);
			});
		},
		/*
		 * 批量删除字典类型
		 * @author: 路正宁
		 * @date: 2023-03-23 17:46:37
		 */
		async delDicTypes() {
			//获取选中的节点
			var CheckedNodes = this.$refs.menu.getCheckedNodes();
			if (CheckedNodes.length == 0) {
				this.$message.warning("请选择需要删除的项");
				return false;
			}
			//删除操作确认
			var confirm = await this.$confirm("确认删除已选择的字典类型吗？", "提示", {
				type: "warning",
				confirmButtonText: "删除",
				confirmButtonClass: "el-button--danger",
			}).catch(() => {
			});
			if (confirm != "confirm") {
				return false;
			}
			//页面加载
			this.menuLoading = true;
			//请求参数处理删除id参数
			var reqData = CheckedNodes.map((item) => item.id).join(",");
			//调用删除接口
			var res = await this.$API.sysDictTypeService.delete(reqData);
			this.menuLoading = false;
			if (res.code == 200) {
				//在列表中移除已删除的字典类型项
				CheckedNodes.forEach((item) => {
					var node = this.$refs.menu.getNode(item);
					//移除字典类型项
					this.$refs.menu.remove(item);
					if (node.isCurrent) {
						//当前删除的是当前编辑的字典类型，则清空当前选中的字典类型对象
						this.currentDicType = null;
						//清空字典数据表
						this.dictValueList = null;
					}
				});
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
		},
		/*
		 * 行内删除字典类型
		 * @author: 路正宁
		 * @date: 2023-03-28 16:27:32
		 */
		async delDicType(node) {
			//删除操作确认
			var confirm = await this.$confirm("确认删除已选择的字典类型吗？", "提示", {
				type: "warning",
				confirmButtonText: "删除",
				confirmButtonClass: "el-button--danger",
			}).catch(() => {
			});
			if (confirm != "confirm") {
				return false;
			}
			//页面加载
			this.menuLoading = true;
			//调用删除接口
			var res = await this.$API.sysDictTypeService.delete(node.data.id);
			this.menuLoading = false;
			if (res.code == 200) {
				//在列表中移除已删除的字典类型项
				var item = this.$refs.menu.getNode(node);
				//移除字典类型项
				this.$refs.menu.remove(node);
				//如果删除的是当前节点
				if (item.isCurrent) {
					//当前删除的是当前编辑的字典类型，则清空当前选中的字典类型对象
					this.currentDicType = null;
					//清空字典数据表
					this.dictValueList = null;
				}
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
		},
		/*
		 * 本地更新数据,表单回调
		 * @author: 路正宁
		 * @date: 2023-03-28 16:35:52
		 */
		handleDicSuccess(parentNode, newNodedata, mode) {
			if ("add" == mode) {
				//释放页面加载
				//动态追加添加后的字典类型节点
				this.$refs.menu.append(newNodedata, parentNode);
				//设置字典类型列表当前节点的焦点
				this.$refs.menu.setCurrentKey(newNodedata.id);
			}
		},
		/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getdicValueList() {
			this.dictValueList = null;
			if (this.$ObjectUtils.isEmpty(this.currentDicType)) {
				this.$message.warning("请点击选择左侧字典类型");
				return;
			}
			//查询条件
			this.searchForm.dictType = this.currentDicType.code;
			//页面加载
			this.listLoading = true;

			var res = await this.$API.sysDictValueService.list({
				//当前页码
				current: this.tablePage.currentPage,
				//每页条数
				size: this.tablePage.pageSize,
				//排序查询
				orders: this.tablePage.orders,
				...this.searchForm,
			});
			if (res.code == 200) {
				//总数据条数
				this.tablePage.total = res.data.total;
				//数据列表
				this.dictValueList = res.data.records;
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
			this.listLoading = false;
		},
		/*
		 * 添加数据
		 * @author: 路正宁
		 * @date: 2023-03-24 14:32:27
		 */
		addForm() {
			if (this.$ObjectUtils.isEmpty(this.currentDicType)) {
				this.$message.warning("请选择字典类型");
				return;
			}
			this.formDialogDisplay = true;
			this.$nextTick(() => {
				this.$refs.formDialog.addView(this.currentDicType);
			});
		},
		/*
		 * 编辑数据
		 * @author: 路正宁
		 * @date: 2023-03-24 14:32:41
		 */
		editForm(row) {
			this.formDialogDisplay = true;
			this.$nextTick(() => {
				this.$refs.formDialog.editView(row, this.currentDicType);
			});
		},
		/*
		 * 查看数据
		 * @author: 路正宁
		 * @date: 2023-03-24 14:32:55
		 */
		viewForm(row) {
			this.formDialogDisplay = true;
			this.$nextTick(() => {
				this.$refs.formDialog.view(row);
			});
		},
		/*
		 * 删除数据，行内删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:35:00
		 */
		async deleteData(row, index) {
			this.listLoading = true;
			var res = await this.$API.sysDictValueService.delete(row.id);
			if (res.code == 200) {
				this.$refs.dataTable.removeIndex(index);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
			this.listLoading = false;
		},
		/*
		 * 批量删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:36:11
		 */
		async deleteDatas() {
			//确认删除弹框
			var confirmRes = await this.$confirm(
				`确定删除选中的 ${this.selection.length} 项吗？`,
				"提示",
				{
					type: "warning",
					confirmButtonText: "删除",
					confirmButtonClass: "el-button--danger",
				}
			).catch(() => {
			});
			//确认结果处理
			if (!confirmRes) {
				return false;
			}
			//要删除的id数组
			var ids = this.selection.map((v) => v.id);
			//拼接的数组字符串，接口传参
			var idStr = this.selection.map((v) => v.id).join(",");
			//页面加载中
			this.listLoading = true;
			var res = await this.$API.sysDictValueService.delete(idStr);
			if (res.code == 200) {
				//从列表中移除已删除的数据
				this.$refs.dataTable.removeKeys(ids);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
			//释放页面加载中
			this.listLoading = false;
		},
		/*
		 * 表格选择后回调事件
		 * @author: 路正宁
		 * @date: 2023-03-24 14:51:09
		 */
		selectionChange(selection) {
			this.selection = selection;
		},
		/*
		 * 数据表单回调函数，表单提交成功以后会调用此方法
		 * 为了减少网络请求，直接变更表格内存数据
		 * @author: 路正宁
		 * @date: 2023-03-24 14:57:49
		 */
		formCallback(data, mode) {
			if (mode == "add") {
				this.$refs.dataTable.unshiftRow(data);
			} else if (mode == "edit") {
				this.$refs.dataTable.updateKey(data);
			}
		},
		/*
		 * 头部搜索框
		 * @author: 路正宁
		 * @date: 2023-03-24 14:58:47
		 */
		search() {
			this.getdicValueList();
		},
	},
};
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
	display: flex;
	flex: 1;
	height: 100%;
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 24px;
	height: 100%;
}

.custom-tree-node .code {
	font-size: 12px;
	color: #999;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node:hover .code {
	display: none;
}

.custom-tree-node:hover .do {
	display: inline-block;
}
</style>
