<!--
 * @Author: saya
 * @Date: 2023-07-24 13:45:13
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2024-12-24 16:29:28
 * @FilePath: \archive-manage-front\src\views\managementFile\view.vue
 * @Description:
-->
<template>
	<el-tabs v-model="activeName" v-loading="loading" class="demo-tabs" @tab-click="handleClick">
		<el-tab-pane label="档案信息" name="first" style="height: 63vh;">
			<el-descriptions :column="4" border class="margin-top" size="default">
				<el-descriptions-item align="center" label="档案名称" label-align="center">
					{{ archivesData.name }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="全宗名称" label-align="center">
					{{ archivesData.controlGroup?.recordGroupName || '暂无' }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案年份" label-align="center">
					{{ archivesData.year }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案月份" label-align="center">
					{{ archivesData.month }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="文件日期" label-align="center">
					{{ archivesData.createDate ? moment(archivesData.createDate).format('YYYY-MM-DD HH:mm:ss') : '暂无' }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="保留年限" label-align="center">
					{{ reserve(archivesData.retentionPeriod) }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="保密等级" label-align="center">
					{{ secrecy(archivesData.protectLevel) }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="控制等级" label-align="center">
					{{ control(archivesData.controlStatus) }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="所属机构" label-align="center">
					{{ archivesData.org?.name || '暂无' }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="所属部门" label-align="center">
					{{ archivesData.office?.name || "无" }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案标签" label-align="center">
					{{ archivesData.tagManagerInfo ? choseTageInfo(archivesData.tagManagerInfo) : "无" }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="附件数量" label-align="center">
					{{ archivesData.infoOfFileCount }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="备注" label-align="center">
					{{ archivesData.remark }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="是否全电文档" label-align="center">
					{{ archivesData.isElectronic === '1' ? "是" : "否" }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案摘要" label-align="center">
					{{ archivesData.digestContent ? archivesData.digestContent : "无" }}
				</el-descriptions-item>
			</el-descriptions>
		</el-tab-pane>
		<el-tab-pane label="基本信息" name="second" style="height: 63vh;overflow-y: scroll">
			<el-descriptions :column="4" border style="margin-bottom: 20px;width: 100%;">
				<template #title>
					<div class="cell-item" style="font-size: 14px;">
						{{ "基本信息" }}
					</div>
				</template>
				<el-descriptions-item
					v-for="(item) in basicInfoList"
					:label="item.config?.remark || '暂无'"
					:span="
						item.config?.displayMode === '1'
						  	? 1
						  	: item.config?.displayMode === '2'
						  		? 2
						  		: item.config?.displayMode === '3'
						  			? 4
						  			: null
					"
					align="left"
					label-align="left"
					class-name="labelCustomClass"
					label-class-name="labelCustomClass"
					min-width="12.5%"
				>
					<div v-if="item.value">
						<div v-if="item.config?.type === '1'">
							{{ item.value ? item.value : "无" }}
						</div>
						<div v-if="item.config?.type === '2'" style="width: 100%;height: 100%;display: flex;
							justify-content: flex-start;align-items: center;column-gap: 10px;">
							<el-image v-for="(imgItem, index) in disposeImg(item.value)" v-if="disposeImg(item.value)"
									  :key="index" :preview-src-list="srcList" :src="imgItem" fit="fill"
									  style="width: 100px;height: 100px;border: 1px solid var(--el-border-color);
									  border-radius: var(--el-border-radius-base);box-shadow: var(--el-box-shadow-lighter)"
									  @click="openImg(imgItem)">
								<template #error>
									<div class="image-slot">
										<el-text>
											{{ "无" }}
										</el-text>
									</div>
								</template>
							</el-image>
							<el-text v-else>
								{{ "无" }}
							</el-text>
						</div>
						<div v-if="item.config?.type === '3'">
							{{ item.value }}
						</div>
						<div v-if="item.config?.type === '4'">
							{{
								item.value
									? moment(item.value).format(item.config?.dataConfigValue || 'YYYY-MM-DD')
									: "无"
							}}
						</div>
						<div v-if="item.config?.type === '5'">
							{{ item.value }}
						</div>
					</div>
					<div v-else>
						{{ "无" }}
					</div>
				</el-descriptions-item>
			</el-descriptions>
			<el-collapse v-model="chooseCollapse">
				<el-collapse-item v-for="(item, index) in descriptionsLabel" :name="index">
					<template #title>
						<el-text tag="b" truncated>
							<el-icon>
								<Expand/>
							</el-icon>
							{{ item.config?.remark || '暂无' }}
						</el-text>
					</template>
					<el-descriptions v-for="(childItem, index) in item.children"
									 v-if="item.config?.showMethod === '1'"
									 :column="4" border style="margin-bottom: 20px;width: 100%;">
						<template v-if="item.children.length > 1" #title>
							<el-divider direction="vertical" style="border-width: 4px;border-color: #e6e9f0;"/>
							<el-text>
								{{ "第" + (index + 1) + "个" }}
							</el-text>
						</template>
						<el-descriptions-item
							v-for="(deepChildItem, index) in childItem"
							:label="deepChildItem.config?.remark || '暂无'"
							:span="deepChildItem.config?.displayMode === '1'
								? 1
								: deepChildItem.config?.displayMode === '2'
									? 2
									: deepChildItem.config?.displayMode === '3'
										? 4
										: null"
							align="left"
							label-align="left"
							class-name="labelCustomClass"
							label-class-name="labelCustomClass"
							min-width="12.5%">
							<div v-if="deepChildItem.value" style="width: 100%;height: 100%">
								<div v-if="deepChildItem.config?.type === '1'" style="width: 100%;height: 100%">
									<el-popover v-if="deepChildItem.value && deepChildItem.value.length >= 320"
												:content="deepChildItem.value ? deepChildItem.value : '无'"
												placement="bottom"
												trigger="hover"
												width="60vw">
										<template #reference>
											<el-text line-clamp="6">
												{{ deepChildItem.value ? deepChildItem.value : "无" }}
											</el-text>
										</template>
										<el-scrollbar height="312px">
											{{ deepChildItem.value ? deepChildItem.value : "无" }}
										</el-scrollbar>
									</el-popover>
									<el-text v-else>
										{{ deepChildItem.value ? deepChildItem.value : "无" }}
									</el-text>
								</div>
								<div v-if="deepChildItem.config?.type === '2'" style="width: 100%;height: 100%;display: flex;
										justify-content: flex-start;align-items: center;column-gap: 10px;">
									<el-image v-for="(imgItem, index) in disposeImg(deepChildItem.value)"
											  v-if="disposeImg(deepChildItem.value)"
											  :key="index" :preview-src-list="srcList" :src="imgItem" fit="fill"
											  style="width: 100px;height: 100px;border: 1px solid var(--el-border-color);
									  		  border-radius: var(--el-border-radius-base);box-shadow: var(--el-box-shadow-lighter)"
											  @click="openImg(imgItem)">
										<template #error>
											<div class="image-slot">
												<el-text>
													{{ "无" }}
												</el-text>
											</div>
										</template>
									</el-image>
									<el-text v-else>
										{{ "无" }}
									</el-text>
								</div>
								<div v-if="deepChildItem.config?.type === '3'">
									{{ deepChildItem.value }}
								</div>
								<div v-if="deepChildItem.config?.type === '4'">
									{{
										deepChildItem.value
											? moment(deepChildItem.value).format(deepChildItem.config?.dataConfigValue || 'YYYY-MM-DD')
											: "无"
									}}
								</div>
								<div v-if="deepChildItem.config?.type === '5'">
									{{ deepChildItem.value }}
								</div>
							</div>
							<el-text v-else>
								{{ "无" }}
							</el-text>
						</el-descriptions-item>
					</el-descriptions>
					<el-table v-if="item.config?.showMethod === '2'" :data="disposeTableData(item.children, 1)"
							  fit highlight-current-row show-overflow-tooltip stripe>
						<el-table-column align="center" label="序号" prop="sort" width="52">
							<template #default="scope">
								{{ scope.$index + 1 }}
							</template>
						</el-table-column>
						<el-table-column v-for="(column, index) in disposeTableData(item.children, 2)"
										 :label="column"
										 :min-width="disposeTableData(item.children, 1)[0][column].config?.length || 100"
										 :prop="column" align="center">
							<template #default="scope">
								<div v-if="scope.row[column].config?.type === '1'">
									{{ scope.row[column].value ? scope.row[column].value : "无" }}
								</div>
								<div v-if="scope.row[column].config?.type === '2'" style="width: 100%;height: 100%;
										display: flex;justify-content: flex-start;align-items: center;column-gap: 10px;">
									<el-image v-for="(imgItem, index) in disposeImg(scope.row[column].value)"
											  v-if="checkFileType(scope.row[column].value) === 'image' && disposeImg(scope.row[column].value)"
											  :key="index" :preview-src-list="srcList" :src="imgItem" fit="fill"
											  style="width: 100px;height: 100px;border: 1px solid var(--el-border-color);
									  		  border-radius: var(--el-border-radius-base);box-shadow: var(--el-box-shadow-lighter)"
											  @click="openImg(imgItem)">
										<template #error>
											<div class="image-slot">
												<el-text>
													{{ "无" }}
												</el-text>
											</div>
										</template>
									</el-image>
									<el-button v-else-if="checkFileType(scope.row[column].value) === 'pdf'">
										<el-button link type="primary" @click="openFileBox(scope.row[column].value)">
											查看
										</el-button>
									</el-button>
									<el-image v-else style="width: 100px;height: 100px;">
										<template #error>
											<div class="image-slot">
												<el-text>
													{{ "无" }}
												</el-text>
											</div>
										</template>
									</el-image>
								</div>
								<div v-if="scope.row[column].config?.type === '3'">
									{{ scope.row[column].value || '无' }}
								</div>
								<div v-if="scope.row[column].config?.type === '4'">
									{{
										scope.row[column].value
											? moment(scope.row[column].value).format(scope.row[column].config?.dataConfigValue || 'YYYY-MM-DD')
											: "无"
									}}
								</div>
								<div v-if="scope.row[column].config?.type === '5'">
									{{ scope.row[column].value || '无' }}
								</div>
							</template>
						</el-table-column>
					</el-table>
				</el-collapse-item>
			</el-collapse>
		</el-tab-pane>
		<el-tab-pane label="附件" name="third" style="height: 63vh">
			<div v-if="tableData.length > 0">附件总数: {{ tableData.length }}</div>
			<el-container class="resizable-container">
				<el-aside :width="leftWidth + 'px'" style="padding-left: 0px">
					<div
						v-for="item in tableData"
						:key="item"
						class="fileUrl"
						@click="handleViewFile(item.fileUrl)"
					>
						<div style="display: flex; justify-content: space-between">
							<el-tooltip
								:content="item.fileName"
								class="box-item"
								effect="dark"
								placement="right"
							>
								<el-text style="cursor: pointer" truncated>
									{{ item.fileName }}
								</el-text>
							</el-tooltip>
						</div>
					</div>
				</el-aside>
				<!-- 拖动条 -->
				<div class="resize-handle" @mousedown="startResize"></div>
				<el-main style="background-color: #e4e7ed; padding: 5px">
					<el-scrollbar ref="scrollbar" style="border-radius: 5px">
						<div
							v-if="tableData.length > 0 && !pdfError"
							ref="main"
							v-loading="pdfLoading"
							style="width: 100%; height: 100%"
						>
							<PDFViewer
								:src="pdfRef"
								height="100%"
								pageScale="page-fit"
								theme="light"
								width="100%"
								@loaded="onLoaded"
								@error="onPdfError"
							/>
						</div>
						<div
							v-else-if="tableData.length > 0 && pdfError"
							style="
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;
								height: 100%;
								padding: 20px;
							"
						>
							<el-result
								:icon="pdfErrorType === 'download' ? 'info' : 'warning'"
								:title="pdfErrorType === 'download' ? 'PDF文件仅支持下载' : 'PDF预览失败'">
								<template #sub-title>
									<div v-if="pdfErrorType === 'download'">
										<p>该PDF文件被服务器配置为下载模式，无法在线预览</p>
										<p>请点击下载按钮获取文件后查看</p>
									</div>
									<div v-else-if="pdfErrorType === 'network'">
										<p>PDF文件被浏览器阻止加载，可能的解决方案：</p>
										<ul style="text-align: left; margin: 10px 0;">
											<li>检查浏览器的广告拦截器设置</li>
											<li>刷新页面重新加载</li>
											<li>检查网络连接</li>
										</ul>
									</div>
									<div v-else-if="pdfErrorType === 'notfound'">
										<p>PDF文件不存在或已被删除</p>
										<p>请联系管理员确认文件状态</p>
									</div>
									<div v-else-if="pdfErrorType === 'permission'">
										<p>没有权限访问该PDF文件</p>
										<p>请联系管理员获取访问权限</p>
									</div>
									<div v-else>
										<p>PDF文件无法在浏览器中预览，可能的原因：</p>
										<ul style="text-align: left; margin: 10px 0;">
											<li>服务器配置问题</li>
											<li>文件格式不兼容</li>
											<li>网络连接异常</li>
										</ul>
									</div>
								</template>
								<template #extra>
									<el-button
										v-if="pdfErrorType !== 'notfound'"
										type="primary"
										@click="downloadPdf">
										下载文件
									</el-button>
								</template>
							</el-result>
						</div>
						<div
							v-else
							style="
								display: flex;
								flex-direction: row;
								flex-wrap: nowrap;
								align-content: center;
								justify-content: center;
								align-items: center;
							"
						>
							<el-result icon="info" title="温馨提醒">
								<template #sub-title>
									<p>此档案无相关附件</p>
								</template>
							</el-result>
						</div>
					</el-scrollbar>
				</el-main>
			</el-container>
		</el-tab-pane>
		<el-main>
			<div style="float: right;">
				<el-button plain @click="() => cancellation()">取消</el-button>
			</div>
		</el-main>

		<PdfViewPlus :file-data-list="fileBoxDataList" :open-status="fileBoxStatus"
					 @closeFileBox="() => {fileBoxStatus = false}"/>
	</el-tabs>
</template>
<script setup>
import view from "@/api/archive/managementFile";
import {computed, defineProps, getCurrentInstance, nextTick, onBeforeUnmount, onMounted, reactive, ref,} from "vue";
import moment from "moment/moment";
import tagsManagement from "@/api/archive/tagsManagement";
import PDFViewer from "@/views/archiveReception/common/PDFViewer.vue";
import PdfViewPlus from "@/components/pdfViewPlus/index.vue";

const {proxy} = getCurrentInstance();
const emit = defineEmits(["childEvent"]);
const activeName = ref("first");
const props = defineProps({
	receiveId: {
		type: Object,
	},
});
const pdfRef = ref();
const scrollbar = ref(null);
const loading = ref(true);
const pdfLoading = ref(true);
const pdfError = ref(false);
const pdfErrorType = ref(''); // 错误类型：'download', 'network', 'permission', 'notfound', 'unknown'
const fileBoxStatus = ref(false);
const srcList = ref([]);
const tagsInfoList = ref([]);
const fileBoxDataList = ref([]);
// 管理库基本信息List
const basicInfoList = ref([]);
// 管理库其他信息List
const descriptionsLabel = ref([]);
const tableData = ref([]);
// 档案信息
const archivesData = ref({});
const chooseCollapse = ref([]);
// 左侧面板初始宽度
const leftWidth = ref(300);

// 拖动相关状态
const isResizing = ref(false);
const startPosition = {x: 0};

function startResize(e) {
	isResizing.value = true;
	startPosition.x = e.clientX;

	// 添加全局事件监听
	window.addEventListener("mousemove", handleMouseMove);
	window.addEventListener("mouseup", stopResize);

	// 防止文本选中
	document.body.style.userSelect = "none";
}

function handleMouseMove(e) {
	if (!isResizing.value) return;

	// 计算新的宽度
	const dx = e.clientX - startPosition.x;
	const newWidth = leftWidth.value + dx;

	// 设置最小和最大宽度限制
	if (newWidth > 100 && newWidth < window.innerWidth - 100) {
		leftWidth.value = newWidth;
	}

	// 更新起始位置
	startPosition.x = e.clientX;
}

function stopResize() {
	isResizing.value = false;

	// 移除全局事件监听
	window.removeEventListener("mousemove", handleMouseMove);
	window.removeEventListener("mouseup", stopResize);

	// 恢复文本选中
	document.body.style.userSelect = "";
}

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
	window.removeEventListener("mousemove", handleMouseMove);
	window.removeEventListener("mouseup", stopResize);
});
const customNodeClass = (data, node) => {
	if (node.childNodes.length <= 0) {
		return "nodeCustomClass";
	} else {
		return null;
	}
};

const state = reactive({
	pageNum: 1, //当前页面
	scale: 1, // 缩放比例
	numPages: 0, // 总页数
	loading: false, //加载效果
	rotation: 0, // 旋转角度
});

const scale = computed(() => `transform:scale(${state.scale});transition: all 0.3s;transform-origin: top left;
  						transition: transform 0.5s ease-out;`);

//初始方法
onMounted(() => {
	queryById();
	getInfoByMasterId();
	fileList();
	loading.value = false;
});

function disposeTableData(list, type) {
	let newList = type === 1 ? [] : new Set();

	if (!Array.isArray(list)) {
		return newList;
	}

	list.forEach((data) => {
		if (type === 1) {
			let newObj = [];
			data.forEach((item) => {
				newObj[item.name] = {
					value: item.value,
					config: item.config,
				};
			});
			newList.push(newObj);
		} else if (type === 2) {
			data.forEach((item) => {
				newList.add(item.name);
			});
		}
	});
	return newList;
}

function disposeImg(imgObject) {
	let imgList = [];

	if (typeof imgObject === "string") {
		if (imgObject.includes("[")) {
			let parseArray = JSON.parse(imgObject);
			if (Array.isArray(parseArray)) {
				parseArray.forEach((item) => {
					for (const key in item) {
						if (key.toLowerCase().includes("url")) {
							imgList.push(item[key]);
						}
					}
				});
			}
		} else if (imgObject.includes("{")) {
			let parseObj = JSON.parse(imgObject);
			for (const key in parseObj) {
				if (key.toLowerCase().includes("url")) {
					imgList.push(parseObj[key]);
				}
			}
		} else {
			imgList.push(imgObject);
		}
	} else {
		return null;
	}

	return imgList;
}

//检测字符串内包含的是什么类型的文件, 图片还是pdf
function checkFileType(fileStrUrl) {
	const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
	const pdfExtensions = ['pdf'];

	if (fileStrUrl) {
		let returnData = '';
		imageExtensions.forEach(extension => {
			if (fileStrUrl.toLowerCase().includes(extension)) {
				returnData = 'image';
			}
		});
		pdfExtensions.forEach(extension => {
			if (fileStrUrl.toLowerCase().includes(extension)) {
				returnData = 'pdf';
			}
		});
		return returnData;
	} else {
		return 'other';
	}
}

//转换文件
function openFileBox(jsonString) {
	try {
		const data = JSON.parse(jsonString);
		const dataArray = Array.isArray(data) ? data : [data];

		fileBoxDataList.value = dataArray.map(item => {
			for (const key in item) {
				if (key.toLowerCase().includes('name')) {
					item["fileName"] = item[key];
				}
				if (key.toLowerCase().includes('url')) {
					item["fileUrl"] = item[key];
				}
			}

			if (!item.fileName && item.fileUrl) {
				item["fileName"] = item.fileUrl.substring(item.fileUrl.lastIndexOf('/') + 1);
			}

			return item;
		});
		fileBoxStatus.value = true;
	} catch (e) {
		return [];
	}
}

function onLoaded(pdfApp) {
	pdfLoading.value = false;
	pdfError.value = false;
	pdfErrorType.value = '';
}

function onPdfError(error) {
	pdfLoading.value = false;
	pdfError.value = true;
	console.error('PDF加载失败:', error);

	// 检查错误类型
	const errorMessage = error?.message || error?.toString() || JSON.stringify(error) || '';
	console.log('错误信息详情:', errorMessage);

	if (errorMessage.includes('ERR_BLOCKED_BY_CLIENT') || errorMessage.includes('net::')) {
		pdfErrorType.value = 'network';
		proxy.msgError('PDF文件被浏览器阻止加载，请检查广告拦截器设置或尝试刷新页面');
	} else if (errorMessage.includes('404')) {
		pdfErrorType.value = 'notfound';
		proxy.msgError('PDF文件不存在或已被删除');
	} else if (errorMessage.includes('403')) {
		pdfErrorType.value = 'permission';
		proxy.msgError('没有权限访问该PDF文件');
	} else {
		pdfErrorType.value = 'download';
		// 默认认为是服务器配置问题（下载模式）
		proxy.msgWarning('PDF文件无法在线预览，可能是服务器配置为下载模式。请使用下载功能查看文件。');
	}
}



function downloadPdf() {
	if (pdfRef.value) {
		// 创建一个临时的 a 标签来触发下载
		const link = document.createElement('a');
		link.href = pdfRef.value;
		link.download = pdfRef.value.split('/').pop() || 'document.pdf';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
}



//列表切换
function handleClick(tabInfo) {
	if (tabInfo.props.name === "third") {
		if (tableData.value.length > 0) {
			if (!pdfRef.value) {
				pdfLoading.value = true;
				pdfError.value = false; // 重置错误状态
				pdfErrorType.value = '';
				pdfRef.value = tableData.value[0].fileUrl;
			}
		} else {
			pdfLoading.value = false;
			pdfError.value = false;
			pdfErrorType.value = '';
		}
	}
}

// 查询档案信息
function queryById() {
	view
		.queryById({
			id: props.receiveId.id,
			showDeleteInfo: true,
		})
		.then((res) => {
			if (res.code === 200) {
				loading.value = false;
				archivesData.value = res.data;
				tagsManagement
					.getList({
						current: 1,
						size: -1,
					})
					.then((res) => {
						if (res.code === 200) {
							tagsInfoList.value = res.data?.records || [];
						}
					});
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

function sortTreeNodes(data) {
	// 防御性检查：确保 data 是数组
	if (!Array.isArray(data)) {
		return [];
	}

	// 首先对当前层级的节点进行排序
	data.sort((a, b) => (a.config?.sort || 0) - (b.config?.sort || 0));
	// 过滤不显示的内容
	data = data.filter((item) => item.config?.isView !== "1");

	// 遍历当前层级的每个节点
	for (let node of data) {
		// 如果当前节点有子节点
		if (node.children) {
			if (Array.isArray(node.children) && !Array.isArray(node.children[0])) {
				// 如果子节点是一维数组
				node.children = sortTreeNodes(node.children);
			} else if (
				Array.isArray(node.children) &&
				Array.isArray(node.children[0])
			) {
				// 如果子节点是二维数组
				node.children = node.children.map((childArray) =>
					sortTreeNodes(childArray)
				);
			}
		}
	}

	return data;
}

function getInfoByMasterId() {
	loading.value = true;
	view
		.getDataByMasterId({
			id: props.receiveId.id,
		})
		.then((res) => {
			if (res.code === 200) {
				let dataList = res.data || [];
				dataList = sortTreeNodes(dataList);
				basicInfoList.value = dataList.filter((item) => {
					return !item.children;
				});
				descriptionsLabel.value = dataList.filter((item) => {
					return item.children;
				});
				for (let i = 0; i <= descriptionsLabel.value.length; i++) {
					// 将当前数值添加到结果数组中
					chooseCollapse.value.push(i);
				}
				descriptionsLabel.value.forEach((label) => {
					let tableDataList = [];
					let data = {};
					label.children.forEach((item) => {
						data[item.name] = item.value;
					});
					tableDataList.push(data);
					label.tableDataList = tableDataList;
				});
				//循环去除数据
				basicInfoList.value = basicInfoList.value.filter((label) => {
					return (
                        label.name &&
						label.name !== "操作记录" &&
						label.name !== "供应商审核记录" &&
						!label.name.includes("审核记录") &&
                        label?.config?.type &&
						!isNaN(Number(label.config?.type || 0))
					);
				});
				loading.value = false;
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

// 处理图片数据
function openImg(imgUrl) {
	srcList.value = [];
	srcList.value.push(imgUrl);
}

//文件查询
function fileList() {
	view
		.fileList({
			"recordInfo.id": props.receiveId.id,
			fileType: "pdf",
			current: 1,
			size: -1,
		})
		.then((res) => {
			if (res.code === 200) {
				tableData.value = res.data.records;
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

// 查看pdf
function handleViewFile(url) {
	if (pdfRef.value !== url) {
		console.log('=== PDF文件切换 ===');
		console.log('新URL:', url);
		console.log('URL类型:', typeof url);
		console.log('URL长度:', url?.length);
		console.log('URL编码检查:', encodeURIComponent(url) === url ? '已编码' : '未编码');

		// 检查URL是否包含特殊字符
		const hasSpecialChars = /[^\w\-._~:/?#[\]@!$&'()*+,;=%]/.test(url);
		console.log('包含特殊字符:', hasSpecialChars);

		// 完全重置所有状态
		pdfLoading.value = true;
		pdfError.value = false;
		pdfErrorType.value = '';

		// 先清空pdfRef，确保组件重新渲染
		pdfRef.value = '';

		// 使用nextTick确保DOM更新后再设置新的URL
		nextTick(() => {
			console.log('设置PDF URL:', url);
			pdfRef.value = url;
			state.pageNum = 1;
			state.scale = 1;
		});
	}
}

// 保留年限
function reserve(val) {
	if (val == "Y") {
		return "永久";
	} else if (val == "D5") {
		return "5年";
	} else if (val == "D10") {
		return "10年 ";
	} else if (val == "D20") {
		return "20年";
	} else if (val == "D30") {
		return "30年";
	} else {
		return "无";
	}
}

// 保密等级
function secrecy(val) {
	if (val == "GK") {
		return "公开";
	} else if (val == "KZ") {
		return "限制";
	} else if (val == "MOM") {
		return "秘密 ";
	} else if (val == "JM") {
		return "机密";
	} else if (val == "UM") {
		return "绝密";
	} else {
		return "无";
	}
}

// 控制等级
function control(val) {
	if (val == "1") {
		return "公开";
	} else if (val == "2") {
		return "公司内部开放";
	} else if (val == "3") {
		return "部门内部开放 ";
	} else if (val == "4") {
		return "控制";
	} else {
		return "无";
	}
}

//查询标签数据集合
function choseTageInfo(choseTagsId) {
	let returnDataList = [];
	if (!choseTagsId) {
		return '';
	}
	let split = choseTagsId.split(",");
	returnDataList = tagsInfoList.value
		.filter((record) => {
			return split.includes(record.id);
		})
		.map((record) => record.tagName);
	return returnDataList.join(",");
}

// 取消
function cancellation() {
	emit("childMove");
}
</script>
<style scoped>
.fileUrl {
	cursor: pointer;
	border-top: 1px solid #e4e7ed;
	border-left: 1px solid #e4e7ed;
	border-bottom: 1px solid #e4e7ed;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.resizable-container {
	position: relative;
	display: flex;
	height: 100%;
}

.resize-handle {
	width: 6px;
	height: 100%;
	background-color: #e0e0e0;
	cursor: col-resize;
	position: relative;
	z-index: 10;
}

.resize-handle:hover,
.resize-handle.resizing {
	background-color: #1890ff;
}

.fileUrl {
	cursor: pointer;
	border-top: 1px solid #e4e7ed;
	border-left: 1px solid #e4e7ed;
	border-bottom: 1px solid #e4e7ed;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

p {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.demo-image__error .image-slot {
	font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
	font-size: 30px;
}

.demo-image__error .el-image {
	width: 100%;
	height: 200px;
}

:deep(.nodeCustomClass) {
	margin: 2px 0;
	height: 42px;
}

:deep(.labelCustomClass) {
	width: 12.5%;
	max-width: 12.5%;
}

:deep(.el-tree-node__content:hover) {
	background: none;
}

:deep(.is-current) {
	background: none;
}

:deep(.el-scrollbar__view) {
	width: 100%;
	height: 100%;
}

:deep(.el-table__cell) {
	position: static !important;
}
</style>
