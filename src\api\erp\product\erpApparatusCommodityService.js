import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/product/erpApparatusCommodity/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/product/erpApparatusCommodity/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/product/erpApparatusCommodity/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/product/erpApparatusCommodity/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/product/erpApparatusCommodity/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/product/erpApparatusCommodity/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/product/erpApparatusCommodity/import',
      data
    )
  }
}
