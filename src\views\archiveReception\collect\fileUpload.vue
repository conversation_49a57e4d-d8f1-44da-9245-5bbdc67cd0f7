<template>
	<el-container>
		<el-main v-loading="loadingFile">
			<el-upload v-model:file-list="fileList" :action="uploadUrl" :headers="headers"
					   :on-progress="uploadVideoProcess" :on-success="handleUploadSuccess" drag
					   multiple style="height: 100%;width: 100%">
				<el-icon size="22px">
					<upload-filled/>
				</el-icon>
				<div>
					将文件拖到此处，或<em>点击上传</em>
				</div>
			</el-upload>
		</el-main>
		<el-footer>
			<div style="display: flex;justify-content: flex-end">
				<el-button type="primary" @click="uploadFile">确定</el-button>
			</div>
		</el-footer>
	</el-container>
</template>
<script setup>
import collectList from '@/api/archive/archiveReception/collect';
import {UploadFilled} from '@element-plus/icons-vue'
import {defineEmits, defineProps, getCurrentInstance, ref} from 'vue'
import tool from '@/utils/tool';

const { proxy } = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const props = defineProps({
    handList: {
        type: Array
    }
})
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const fileList = ref([])
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
	clientType: "PC"
}
const videoUploadPercent = ref()
const loadingFile = ref(false)
//文件上传
function uploadVideoProcess(event) {
    videoUploadPercent.value = Math.floor(event.percent);
}
const fileUrl = ref([])
//文件上传成功
const handleUploadSuccess = (res, file, fileList) => {
    if (res.code === 200) {
        fileList.value = [{ ...res.data }];
        fileUrl.value.push(fileList.value[0].url);
    }
}

function uploadFile() {
	loadingFile.value = true;
    let idStr = props.handList.map((v) => v.id);
    //拼接的数组字符串，接口传参
	let ids = idStr.join(",");
	let urlList = fileUrl.value;
	let data = {
        recordInfo: { id: ids},
        // infoId: ids,
        urlList: urlList
    }
    collectList.infoFileUpload(data).then(res => {
        if (res.code === 200) {
            proxy.msgSuccess('上传成功');
			fileUrl.value = [];
			fileList.value = [];
			loadingFile.value = false;
			emit("childEvent");
        }
    }).catch(() => {
        proxy.msgError('上传失败');
		loadingFile.value = false;
    })
}
</script>
