<template>
	<el-container>
		<el-header height="auto" style="background: none;border-bottom: none;padding: 10px 10px 5px 10px">
			<el-card style="height: 100%;width: 100%">
				<el-input v-model="dataNameSearch" class="w-50 m-2" placeholder="请输入需要查询的名称"
						  style="width: 521px;" @keydown.enter="getList"/>
				<el-button icon="Search" style="margin-left: 20px;" type="primary" @click="getList">
					查询
				</el-button>
				<el-button icon="RefreshRight" plain @click="() => {dataNameSearch = '';getList();}">
					重置
				</el-button>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px">
			<el-card :body-style="{height: '100%',width: '100%',padding: '20px 20px 0 20px'}"
					 style="height: 100%;width: 100%">
				<el-container>
					<el-header height="auto" style="background: none;border-bottom: none;padding: 0 0 15px 0">
						<el-button icon="Plus" plain type="primary" @click="handleAdd()">
							新增
						</el-button>
					</el-header>
					<el-main style="padding: 0">
						<el-table v-loading="loading" :data="list" border style="height: 100%;width: 100%">
							<el-table-column align="center" label="全宗号" prop="recordGroupNum"/>
							<el-table-column align="center" label="全宗名称" prop="recordGroupName"/>
							<el-table-column align="center" label="简称" prop="recordGroupSimpleName"/>
							<el-table-column align="center" label="备注" prop="recordGroupRemark"/>
							<el-table-column align="center" label="操作" width="152">
								<template #default="scope">
									<el-button icon="Edit" link type="primary" @click="handleAdd(scope.row)">
										编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="handleDelete(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 0" @pagination="getList"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改全宗对话框 -->
		<el-dialog v-if="open" v-model="open" :title="title" align-center append-to-body width="600px">
			<el-form ref="formRef" v-loading="loading" :model="form" :rules="rules" label-width="100px"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="全宗号" prop="recordGroupNum">
					<el-input v-model="form.recordGroupNum" clearable placeholder="请输入全宗号"/>
				</el-form-item>
				<el-form-item label="全宗名称" prop="recordGroupName">
					<el-input v-model="form.recordGroupName" clearable placeholder="请输入全宗名称"/>
				</el-form-item>
				<el-form-item label="简称" prop="recordGroupSimpleName">
					<el-input v-model="form.recordGroupSimpleName" clearable placeholder="请输入简称"/>
				</el-form-item>
				<el-form-item label="排序号" prop="recordGroupSort">
					<el-input v-model="form.recordGroupSort" clearable maxlength="5" placeholder="请输入排序号"/>
				</el-form-item>
				<el-form-item label="备注" prop="recordGroupRemark">
					<el-input v-model="form.recordGroupRemark" :autosize="{minRows: 6}" clearable maxlength="500"
							  placeholder="请输入备注"
							  show-word-limit type="textarea"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定</el-button>
					<el-button @click="() => open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue'
import completeManagement from '@/api/archive/systemConfiguration/completeManagement'

const {proxy} = getCurrentInstance();
const list = ref([{status: '1'}]);
const open = ref(false);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const dataNameSearch = ref("");
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 20,
	},
	rules: {
		recordGroupNum: [
			{required: true, message: "全宗号不能为空", trigger: "blur"},
			{pattern: /^[a-zA-Z0-9-]*$/, message: '不允许中文和特殊符号', trigger: 'blur'}
		],
		recordGroupName: [
			{required: true, message: "全宗名称不能为空", trigger: "blur"}
		],
		recordGroupSimpleName: [
			{required: true, message: "简称不能为空", trigger: "blur"}
		],
		recordGroupSort: [
			{required: true, message: "排序不能为空", trigger: "blur"},
			{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
		],
	},
});
const {queryParams, form, rules,} = toRefs(data);

onBeforeMount(() => {
	getList();
})

function submitForm() {
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			completeManagement.save(form.value).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess(`${title.value}成功`)
					open.value = false
					getList()
				}
			}).catch(() => {

			})
		}
	})
}

const handleDelete = (row) => {
	proxy.$confirm('是否确认删除该条数据吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		completeManagement.delete({ids: row.id}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('删除成功')
				getList();
			} else {
				proxy.msgError('删除失败')
			}
		})
	})
}

/** 查询角色列表 */
function getList() {
	loading.value = true
	completeManagement.getList({
		...queryParams.value,
		recordGroupName: dataNameSearch.value ? dataNameSearch.value : null
	}).then(res => {
		if (res.code === 200) {
			list.value = res.data.records
			total.value = res.data.total
			loading.value = false
		}
	})
}


function handleAdd(row) {
	row ? (title.value = '修改') : (title.value = '新增')
	if (row) {
		const {recordGroupName, recordGroupNum, recordGroupRemark, recordGroupSimpleName, recordGroupSort, id} = row
		form.value = {
			recordGroupName,
			recordGroupNum,
			recordGroupRemark,
			recordGroupSimpleName,
			recordGroupSort,
			id
		}
	} else {
		form.value = {}
	}
	open.value = true
}
</script>

<style scoped>
</style>
