<!--
 * @Author: saya
 * @Date: 2023-07-24 13:45:13
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-08-09 11:37:24
 * @FilePath: \archive-manage-front\src\views\archiveReception\receive\receive.vue
 * @Description:
-->
<template>
	<el-tabs v-model="activeName" v-loading="loading" class="demo-tabs" @tab-click="handleClick">
		<el-tab-pane label="基本信息" name="first" style="height: 72vh;overflow-y: scroll">
			<el-descriptions
				:column="4"
				border
				class="margin-top"
				style="margin-bottom: 10px;width: 100%;"
			>
				<el-descriptions-item align="center" label="档案名称" label-align="center">
					{{ archivesData.name }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="全宗名称" label-align="center">
					{{ archivesData.controlGroup.recordGroupName }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案年份" label-align="center">
					{{ archivesData.year }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案月份" label-align="center">
					{{ archivesData.month }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="文件日期" label-align="center">
					{{ moment(archivesData.createDate).format('YYYY-MM-DD HH:mm:ss') }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="保留年限" label-align="center">
					{{ reserve(archivesData.retentionPeriod) }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="保密等级" label-align="center">
					{{ secrecy(archivesData.protectLevel) }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="控制等级" label-align="center">
					{{ control(archivesData.controlStatus) }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="所属机构" label-align="center">
					{{ archivesData.org.name }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="所属部门" label-align="center">
					{{ archivesData.office.name ? archivesData.office.name : "暂无" }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案标签" label-align="center">
					{{ archivesData.tagManagerInfo ? choseTageInfo(archivesData.tagManagerInfo) : "暂无" }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="附件数量" label-align="center">
					{{ archivesData.infoOfFileCount }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="备注" label-align="center">
					{{ archivesData.remark }}
				</el-descriptions-item>
				<el-descriptions-item align="center" label="档案摘要" label-align="center">
					{{ archivesData.digestContent ? archivesData.digestContent : "暂无" }}
				</el-descriptions-item>
			</el-descriptions>
		</el-tab-pane>
		<el-tab-pane label="基本信息" name="second" style="height: 63vh;overflow-y: scroll">
			<el-descriptions :column="4" border style="margin-bottom: 20px;width: 100%;">
				<template #title>
					<div class="cell-item" style="font-size: 14px;">
						{{ "基本信息" }}
					</div>
				</template>
				<el-descriptions-item
					v-for="(item, index) in basicInfoList"
					:label="item.dataConfig.recordDataDetailRemark"
					:span="item.dataConfig.recordDataDetailDisplayMode === '1'
				  		? 1
				  		: item.dataConfig.recordDataDetailDisplayMode === '2'
							? 2
							: item.dataConfig.recordDataDetailDisplayMode === '3'
								? 4
								: null"
					align="left"
					label-align="left"
					class-name="labelCustomClass"
					label-class-name="labelCustomClass"
					min-width="12.5%">
					<div v-if="item.dataValue">
						<div v-if="item.dataType === '1'">
							{{ item.dataValue ? item.dataValue : "暂无" }}
						</div>
						<el-image v-if="item.dataType === '2'"
								  v-for="(imgItem, index) in disposeImg(item.dataValue)"
								  :preview-src-list="srcList"
								  :key="index"
								  :src="imgItem"
								  fit="fill"
								  style="width: 100px; height: 100px"
								  @click="openImg(imgItem)">
							<template #error>
								<div class="image-slot">
									<el-icon>
										<Picture/>
									</el-icon>
								</div>
							</template>
						</el-image>
						<div v-if="item.dataType === '3'">
							{{ item.dataValue }}
						</div>
						<div v-if="item.dataType === '4'">
							{{
								item.dataValue
									? moment(item.dataValue)
										.format(item.dataConfig.recordDataDetailTypeValue)
									: "暂无"
							}}
						</div>
						<div v-if="item.dataType === '5'">
							{{ item.dataValue }}
						</div>
					</div>
					<div v-else>
						{{ "无" }}
					</div>
				</el-descriptions-item>
			</el-descriptions>
			<el-collapse v-model="chooseCollapse">
				<el-collapse-item v-for="(item, index) in descriptionsLabel" :name="index">
					<template #title>
						<el-text tag="b" truncated>
							<el-icon>
								<Expand/>
							</el-icon>
							{{ item.name }}
						</el-text>
					</template>
					<el-descriptions
						v-for="(childItem, index) in item.children"
						v-if="item.dataConfig.recordDataDetailTypeMethod === '1'"
						:column="4"
						border
						style="margin-bottom: 20px;width: 100%;">
						<template v-if="item.children.length > 1" #title>
							<el-divider direction="vertical" style="border-width: 4px;border-color: #e6e9f0;"/>
							<el-text>
								{{ "第" + childItem.name + "个" }}
							</el-text>
						</template>
						<el-descriptions-item
							v-for="(deepChildItem, index) in childItem.children"
							:label="deepChildItem.name"
							align="left"
							label-align="left"
							:span="deepChildItem.dataConfig.recordDataDetailDisplayMode === '1'
								? 1
								: deepChildItem.dataConfig.recordDataDetailDisplayMode === '2'
									? 2
									: deepChildItem.dataConfig.recordDataDetailDisplayMode === '3'
										? 4
										: null"
							class-name="labelCustomClass"
							label-class-name="labelCustomClass"
							min-width="12.5%">
							<div v-if="deepChildItem.dataValue" style="width: 100%;height: 100%">
								<div v-if="deepChildItem.dataType === '1'" style="width: 100%;height: 100%">
									<el-popover v-if="deepChildItem.dataValue.length >= 320"
												:content="deepChildItem.dataValue ? deepChildItem.dataValue : '暂无'"
												placement="bottom"
												trigger="hover"
												width="60vw">
										<template #reference>
											<el-text line-clamp="6">
												{{
													deepChildItem.dataValue
														? deepChildItem.dataValue
														: "暂无"
												}}
											</el-text>
										</template>
										<el-scrollbar height="312px">
											{{
												deepChildItem.dataValue
													? deepChildItem.dataValue
													: "暂无"
											}}
										</el-scrollbar>
									</el-popover>
									<el-text v-else>
										{{
											deepChildItem.dataValue
												? deepChildItem.dataValue
												: "暂无"
										}}
									</el-text>
								</div>
								<el-image v-if="deepChildItem.dataType === '2'"
										  v-for="(imgItem, index) in disposeImg(deepChildItem.dataValue)"
										  :preview-src-list="srcList"
										  :key="index"
										  :src="imgItem"
										  fit="fill"
										  style="width: 100px; height: 100px"
										  @click="openImg(imgItem)">
									<template #error>
										<div class="image-slot">
											<el-icon>
												<Picture/>
											</el-icon>
										</div>
									</template>
								</el-image>
								<div v-if="deepChildItem.dataType === '3'">
									{{ deepChildItem.dataValue }}
								</div>
								<div v-if="deepChildItem.dataType === '4'">
									{{
										deepChildItem.dataValue
											? moment(deepChildItem.dataValue)
												.format(deepChildItem.dataConfig.recordDataDetailTypeValue)
											: "暂无"
									}}
								</div>
								<div v-if="deepChildItem.dataType === '5'">
									{{ deepChildItem.dataValue }}
								</div>
							</div>
							<el-text v-else>
								{{ "无" }}
							</el-text>
						</el-descriptions-item>
					</el-descriptions>
					<el-table v-if="item.dataConfig.recordDataDetailTypeMethod === '2'"
							  :data="disposeTableData(item.children, 1)" fit highlight-current-row
							  stripe>
						<el-table-column align="center" label="序号" prop="sort" width="52">
							<template #default="scope">
								{{ scope.$index + 1 }}
							</template>
						</el-table-column>
						<el-table-column v-for="(column, index) in disposeTableData(item.children, 2)"
										 :label="column"
										 :min-width="disposeTableData(item.children, 1)[0][column].dataConfig.recordDataDetailLength" :prop="column" align="center">
							<template #default="scope">
								<div v-if="scope.row[column].config.recordDataDetailType === '1'">
									{{ scope.row[column].value ? scope.row[column].value : "暂无" }}
								</div>
								<div v-if="scope.row[column].config.recordDataDetailType === '2'">
									<el-image v-for="(imgItem, index) in disposeImg(scope.row[column].value)"
											  :key="index"
											  :preview-src-list="srcList"
											  :src="imgItem"
											  fit="fill"
											  style="width: 100px; height: 100px;"
											  @click="openImg(imgItem)">
										<template #error>
											<div class="image-slot">
												<el-icon>
													<Picture/>
												</el-icon>
											</div>
										</template>
									</el-image>
								</div>
								<div v-if="scope.row[column].config.recordDataDetailType === '3'">
									{{ scope.row[column].value }}
								</div>
								<div v-if="scope.row[column].config.recordDataDetailType === '4'">
									{{
										scope.row[column].value
											? moment(scope.row[column].value)
												.format(scope.row[column].config.recordDataDetailTypeValue)
											: "暂无"
									}}
								</div>
								<div v-if="scope.row[column].config.recordDataDetailType === '5'">
									{{ scope.row[column].value }}
								</div>
							</template>
						</el-table-column>
					</el-table>
				</el-collapse-item>
			</el-collapse>
			<div>
				<h6 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px;font-size: 14px;">操作记录</h6>
				<LogQuery ref="logRef3"/>
			</div>
		</el-tab-pane>
		<el-tab-pane label="附件" name="third" style="height: 63vh;">
			<el-container>
				<el-aside width="280px">
					<div v-for="item in tableData" :key="item" class="fileUrl"
						 @click="handleViewFile(item.fileUrl)">
						<div style="display: flex;justify-content: space-between;">
							<el-tooltip :content="item.fileName" class="box-item" effect="dark" placement="right">
								<el-text style="cursor: pointer;" truncated>
									{{ item.fileName }}
								</el-text>
							</el-tooltip>
						</div>
					</div>
				</el-aside>
				<el-main style="background-color: #E4E7ED;padding: 5px">
					<el-scrollbar ref="scrollbar" style="border-radius: 5px">
						<div v-if="tableData.length > 0" ref="main" v-loading="pdfLoading"
							 style="width: 100%;height: 100%">
							<PDFViewer :src="pdfRef" height="100%" pageScale="page-fit"
									   theme="light" width="100%" @loaded="onLoaded"/>
						</div>
						<div v-else style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
							justify-content: center;align-items: center;">
							<el-result icon="info" title="温馨提醒">
								<template #sub-title>
									<p>此档案无相关附件</p>
								</template>
							</el-result>
						</div>
					</el-scrollbar>
				</el-main>
			</el-container>
		</el-tab-pane>
		<el-main>
			<div style="float: right;">
				<el-button plain @click="() => cancellation()">取消</el-button>
			</div>
		</el-main>
	</el-tabs>
</template>
<script setup>
import collectList from '@/api/archive/archiveReception/collect';
import moment from 'moment'
import view from "@/api/archive/managementFile"
import {computed, defineProps, getCurrentInstance, onMounted, reactive, ref} from 'vue'
import LogQuery from "@/components/detailsForm/logQuery.vue";
import tagsManagement from "@/api/archive/tagsManagement";
import PDFViewer from "@/views/archiveReception/common/PDFViewer.vue";

const {proxy} = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const activeName = ref('first')
const props = defineProps({
	receiveId: {
		type: String
	}
})
const pdfRef = ref()
const srcList = ref([])
const tagsInfoList = ref([]);
const loading = ref(true);
const pdfLoading = ref(true);
// 操作流程
const logRef3 = ref(null)
const scrollbar = ref(null)
// 收集库基本信息List
const basicInfoList = ref([]);
// 收集库其他信息List
const descriptionsLabel = ref([]);
// 档案信息
const archivesData = ref({});
// 附件集合
const tableData = ref([]);
const state = reactive({
	pageNum: 1, //当前页面
	scale: 1, // 缩放比例
	numPages: 0, // 总页数
	loading: false,//加载效果
	rotation: 0 // 旋转角度
});
const chooseCollapse = ref([]);
const customNodeClass = (data, node) => {
	if (node.childNodes.length <= 0) {
		return 'nodeCustomClass'
	} else {
		return null
	}
}

const scale = computed(() => `transform:scale(${state.scale});transition: all 0.3s;transform-origin: top left;
  						transition: transform 0.5s ease-out;`);

//页面初始方法
onMounted(() => {
	queryById()
	getInfoByMasterId();
	fileFist();
});

function disposeTableData(list, type) {
	let newList = type === 1 ? [] : new Set();

	list.forEach(data => {
		if (type === 1) {
			let newObj = [];
			data.children.forEach(item => {
				newObj[item.name] = {
					value: item.dataValue,
					config: item.dataConfig
				};
			});
			newList.push(newObj);
		} else if (type === 2) {
			data.children.forEach(item => {
				newList.add(item.name);
			});
		}
	});
	console.log(newList, 'newList');
	return newList;
}

function disposeImg(imgObject) {
	let imgList = [];

	if (imgObject.includes("[")) {
		JSON.parse(imgObject).forEach(item => {
			imgList.push(item.url);
		});
	} else if (imgObject.includes("{")) {
		imgList.push(JSON.parse(imgObject).url);
	} else {
		imgList.push(imgObject);
	}

	return imgList;
}

function onLoaded(pdfApp) {
	pdfLoading.value = false;
	console.log('loaded app:', pdfApp)
}

function lastPage() {
	if (state.pageNum > 1) {
		state.pageNum -= 1;
	}
}

function nextPage() {
	if (state.pageNum < state.numPages) {
		state.pageNum += 1;
	}
}

function rotateLeft() {
	if (state.rotation > 0) {
		state.rotation -= 90;
	} else {
		state.rotation = 270;
	}
}

function rotateRight() {
	if (state.rotation < 270) {
		state.rotation += 90;
	} else {
		state.rotation = 0;
	}
}

function pageZoomOut() {
	if (state.scale < 3) {
		state.scale = parseFloat((state.scale + 0.1).toFixed(1))
		scrollbar.value.update();
		console.log(state.scale);
	}
}

function pageZoomIn() {
	if (state.scale > 1) {
		state.scale = parseFloat((state.scale - 0.1).toFixed(1))
		scrollbar.value.update();
		console.log(state.scale);
	}
}

//列表切换
function handleClick(tabInfo) {
	if (tabInfo.props.name === 'third') {
		if (tableData.value.length > 0) {
			if (!pdfRef.value) {
				pdfLoading.value = true;
				pdfRef.value = tableData.value[0].fileUrl;

				const loadingTask = createLoadingTask(pdfRef.value);
				state.loading = true; // 添加一个loading状态
				loadingTask.promise.then((pdf) => {
					state.numPages = pdf.numPages;
					// 加载完成后将loading状态设置为false
					state.loading = false;
				});
			}
		} else {
			pdfLoading.value = false;
		}
	}
}

//处理图片数据
function openImg(imgUrl) {
	srcList.value = [];
	srcList.value.push(imgUrl);
}

function sortTreeNodes(data) {
	// 首先对当前层级的节点进行排序
	data.sort((a, b) => a.dataConfig.recordDataDetailSort - b.dataConfig.recordDataDetailSort);
	// 过滤不显示的内容
	data = data.filter(item => item.dataConfig.recordDataDetailIsView !== '1');

	// 遍历当前层级的每个节点
	for (let node of data) {
		// 如果当前节点有子节点
		if (node.children && Array.isArray(node.children)) {
			// 递归地对子节点进行排序
			node.children = sortTreeNodes(node.children);
		}
	}

	return data;
}

function getInfoByMasterId() {
	loading.value = true;
	view.getInfoByMasterIdPlus({
		recordId: props.receiveId.id
	}).then(res => {
		if (res.code === 200) {
			let records = [];

			let dataList = res.data;
			dataList = sortTreeNodes(dataList);
			basicInfoList.value = dataList.filter(item => {
				return !item.children
			});
			descriptionsLabel.value = dataList.filter(item => {
				return item.children
			});
			for (let i = 0; i <= descriptionsLabel.value.length; i++) {
				// 将当前数值添加到结果数组中
				chooseCollapse.value.push(i);
			}
			basicInfoList.value.forEach(label => {
				if (label.name === '操作记录' || label.name === '供应商审核记录' || label.name.includes('审核记录')) {
					let recordData = label.dataValue ? JSON.parse(label.dataValue) : [];
					recordData.forEach(record => {
						records.push({
							name: record.reviewer,
							updateDate: record.review_time,
							remark: record.remark,
							reviewIdea: record.review_idea
						});
					});
				}
			});
			descriptionsLabel.value.forEach(label => {
				let tableDataList = [];
				let data = {};
				label.children.forEach(item => {
					data[item.name] = item.recordOriginalDataValue
				});
				tableDataList.push(data);
				label.tableDataList = tableDataList;
			});
			logRef3.value.timeFns(records);
			//循环去除数据
			basicInfoList.value = basicInfoList.value.filter(label => {
				return label.name !== "操作记录" && label.name !== "供应商审核记录" && !label.name.includes('审核记录') && !isNaN(Number(label.dataConfig.recordDataDetailType));
			});
			loading.value = false;
		}
	}).catch((error) => {
		console.log(error);
		proxy.msgError('查询失败');
	})
}

function fileFist() {
	collectList.fileFist({
		'recordInfo.id': props.receiveId.id,
		fileType: 'pdf',
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			tableData.value = res.data.records;
		}
	}).catch((e) => {
		console.log(e);
		proxy.msgError('查询失败');
	})
}

// 查看pdf
function handleViewFile(url) {
	if (pdfRef.value !== url) {
		pdfLoading.value = true;
		pdfRef.value = url;
		state.pageNum = 1;
		state.scale = 1;

		const loadingTask = createLoadingTask(pdfRef.value);
		state.loading = true; // 添加一个loading状态
		loadingTask.promise.then((pdf) => {
			state.numPages = pdf.numPages;
			// 加载完成后将loading状态设置为false
			state.loading = false;
		});
	}
}

// 取消
function cancellation() {
	emit("childMove");
}

//查询标签数据集合
function choseTageInfo(choseTagsId) {
	let returnDataList = [];
	let split = choseTagsId.split(",");
	returnDataList = tagsInfoList.value.filter(record => {
		return split.includes(record.id);
	}).map(record => record.tagName);
	return returnDataList.join(",");
}

// 查询档案信息
function queryById() {
	view.queryById({
		id: props.receiveId.id,
		showDeleteInfo: true
	}).then(res => {
		if (res.code === 200) {
			loading.value = false;
			archivesData.value = res.data;
			tagsManagement.getList({
				current: 1,
				size: -1
			}).then(res => {
				if (res.code === 200) {
					tagsInfoList.value = res.data.records;
				}
			});
		}
	}).catch((e) => {
		console.log(e);
		proxy.msgError('查询失败');
	})
}

// 保留年限
function reserve(val) {
	if (val == 'Y') {
		return '永久'
	} else if (val == 'D5') {
		return '5年'
	} else if (val == 'D10') {
		return '10年 '
	} else if (val == 'D20') {
		return '20年'
	} else if (val == 'D30') {
		return '30年'
	} else {
		return '暂无'
	}
}

// 保密等级
function secrecy(val) {
	if (val == 'GK') {
		return '公开'
	} else if (val == 'KZ') {
		return '限制'
	} else if (val == 'MOM') {
		return '秘密 '
	} else if (val == 'JM') {
		return '机密'
	} else if (val == 'UM') {
		return '绝密'
	} else {
		return '暂无'
	}
}

// 控制等级
function control(val) {
	if (val == '1') {
		return '公开'
	} else if (val == '2') {
		return '公司内部开放'
	} else if (val == '3') {
		return '部门内部开放 '
	} else if (val == '4') {
		return '控制'
	} else {
		return '暂无'
	}
}
</script>
<style setup scoped>
.fileUrl {
	cursor: pointer;
	border-top: 1px solid #E4E7ED;
	border-left: 1px solid #E4E7ED;
	border-bottom: 1px solid #E4E7ED;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

p {
	width: 250px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.demo-image__error .image-slot {
	font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
	font-size: 30px;
}

.demo-image__error .el-image {
	width: 100%;
	height: 200px;
}

:deep(.nodeCustomClass) {
	margin: 2px 0;
	height: 42px;
}

:deep(.labelCustomClass) {
	width: 12.5%;
	max-width: 12.5%;
}

:deep(.el-tree-node__content:hover) {
	background: none;
}

:deep(.is-current) {
	background: none;
}

:deep(.el-scrollbar__view) {
	width: 100%;
	height: 100%;
}
</style>
