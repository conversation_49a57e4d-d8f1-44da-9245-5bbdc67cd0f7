import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/customer/erpCustomersDelegate/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/customer/erpCustomersDelegate/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/customer/erpCustomersDelegate/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/customer/erpCustomersDelegate/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/customer/erpCustomersDelegate/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/customer/erpCustomersDelegate/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/customer/erpCustomersDelegate/import',
      data
    )
  }
}
