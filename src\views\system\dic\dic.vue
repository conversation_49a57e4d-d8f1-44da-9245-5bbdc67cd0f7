<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    :width="330"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="dialogForm"
      label-width="80px"
      label-position="left"
    >
      <el-form-item label="编码" prop="code">
        <el-input v-model="form.code" clearable placeholder="字典编码"></el-input>
      </el-form-item>
      <el-form-item label="字典名称" prop="name">
        <el-input v-model="form.name" clearable placeholder="字典显示名称"></el-input>
      </el-form-item>
      <el-form-item label="父路径" prop="parentId" v-if="parentDisplay">
        <el-cascader
          v-model="form.parent.id"
          :options="dic"
          :props="dicProps"
          :show-all-levels="false"
          clearable
          :disabled="true"
        ></el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  emits: ["success", "closed"],
  data() {
    return {
      mode: "add",
      titleMap: {
        add: "新增字典",
        edit: "编辑字典",
      },
      visible: false,
      isSaveing: false,
      parentDisplay: true,
      form: {
        id: "",
        name: "",
        code: "",
        parent: {
          id: "",
        },
      },
      parentNode: null,
      rules: {
        code: [{ required: true, message: "请输入编码" }],
        name: [{ required: true, message: "请输入字典名称" }],
      },
      dic: [],
      dicProps: {
        value: "id",
        label: "name",
        emitPath: false,
        checkStrictly: true,
      },
    };
  },
  mounted() {
    this.getDic();
  },
  methods: {
    /*
     * 添加视图
     * @author: 路正宁
     * @date: 2023-03-28 15:40:53
     */
    addView(parentNode) {
      this.mode = "add";
      //父节点UI对象
      this.parentNode = parentNode;
      if (parentNode != null) {
        //初始化父节点
        this.form.parent = parentNode.data;
        this.parentDisplay = true;
      } else {
        //初始化父节点
        this.form.parent = { id: "0" };
        this.parentDisplay = false;
      }
      this.visible = true;
      return this;
    },
    /*
     * 编辑视图
     * @author: 路正宁
     * @date: 2023-03-28 16:07:47
     */
    editView(parentNode, node) {
      this.mode = "edit";
      //父节点UI对象
      this.parentNode = parentNode;
      this.form = node;
      this.parentDisplay = true;
      this.visible = true;
      return this;
    },
    /*
     * 初始化父节点
     * @author: 路正宁
     * @date: 2023-03-28 17:49:55
     */
    async getDic() {
      var res = await this.$API.sysDictTypeService.treeData();
      this.dic = res.data;
    },
    /*
     * 表单提交
     * @author: 路正宁
     * @date: 2023-03-28 15:42:52
     */
    async submit() {
      //表单校验
      if ((await this.$refs.dialogForm.validate()) == false) {
        return;
      }
      this.loading = true;
      //保存数据权限接口
      var res = await this.$API.sysDictTypeService.save(this.form);
      if (res.code == 200) {
        this.$emit("success", this.parentNode, res.data, this.mode);
        this.visible = false;
        this.$message.success("保存成功");
      } else {
        this.visible = true;
        this.$Response.errorNotice(res, "保存失败");
      }
      this.loading = false;
    },
  },
};
</script>

<style></style>
