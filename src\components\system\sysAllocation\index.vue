<template>
	<el-dialog
		v-model="visible"
		title="组织机构可见菜单配置"
		top="7vh"
		width="42%"
		@closed="$emit('closed')"
	>
		<el-container v-loading="listLoading" style="cursor: pointer;height: 72vh">
			<el-main style="padding: 0;border: 1px solid var(--el-border-color-light);display: flex;
    			justify-content: space-between;align-items: stretch;flex-direction: column;flex-wrap: nowrap;">
				<el-header>
					<div class="right-panel-search">
						<el-input
							v-model="filterText"
							placeholder="输入关键字进行过滤"
							style="width: 620px"
							@input="input_change">
						</el-input>
					</div>
				</el-header>
				<el-main class="nopadding" style="height: 360px">
					<right-menu ref="right_menu" @onMenuClick.self="onMenuClick"/>
					<el-tree
						ref="tree"
						:data="dataList"
						:default-checked-keys="selects"
						:filter-node-method="filterNode"
						:props="menuProps"
						check-strictly
						class="filter-tree"
						highlight-current
						node-key="id"
						show-checkbox
						@node-contextmenu.stop="right_btn"
						@check.self="menuNodeClick"
					>
					</el-tree>
					<div class="buttons">
						<!-- <el-button @click="resetChecked">清空</el-button> -->
					</div>
				</el-main>
			</el-main>
		</el-container>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<!--			<el-button type="primary" @click="submitComplet()">确 定</el-button>-->
		</template>
	</el-dialog>
</template>

<script>
import sysMenuService from "@/api/model/sys/sysMenuService";
import RightMenu from "@/components/system/sysMenuSelect/rightMenu.vue";

export default {
	components: {RightMenu},
	props: {
		//是否多选
		isMultiple: {type: Boolean, default: false},
		//回调函数
		selectChange: {type: Function},
		//取消选中回调
		cancelSelect: {type: Function},
	},
	data() {
		return {
			//数据列表
			dataList: [],
			filterText: '',
			menuProps: {
				children: "children",
				label: "name",
			},
			//查询表单
			searchForm: {},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
			//已选择的数据
			selectData: [],
			selects: [],
			visible: true,
			//当前角色
			role: {},
			//当前菜单
			menu: {},
			remainingCalls: 0,
			//菜单权限列表
			dataRuleList: [],
			formLabelAlign: []
		};
	},
	mounted() {
		setTimeout(() => {
			//刷新数据列表
			this.getDataList();
		}, 0);
	},
	methods: {
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getDataList() {
			//初始化数据列表
			this.dataList = [];
			//请求接口
			this.listLoading = true;
			let res = await sysMenuService.treeData({
				//查询参数
				...this.searchForm,
			});
			this.listLoading = false;
			if (res.code == 200) {
				//数据列表
				this.dataList = res.data;
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},

		/*
		 * 选中的数据
		 * @author: 路正宁
		 * @date: 2023-03-31 17:26:29
		 */
		selecteds(role, menuIds) {
			//选中指定的系统菜单
			this.selects = menuIds;
			this.role = role;
		},
		/*
		 * 提交选择结果
		 * @author: 路正宁
		 * @date: 2023-04-03 09:55:23
		 */
		submitComplet() {
			this.selectChange(this.role, this.$refs.tree.getCheckedKeys());
			this.visible = false;
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		input_change() {
			this.$refs.tree.filter(this.filterText);
		},
		right_btn(e, data) {
			this.$refs.right_menu.onButtonClick(e, data)
		},
		onMenuClick(index, data) {
			console.log(data, "chooseNode");
			this.$refs.tree.setChecked(data, index === 1, true);

			if (index === 1) {
				this.selectChange(this.role, data);
			} else if (index === 2) {
				this.cancelSelect(this.role, data);
			}

			if (data.children && data.children.length > 0) {
				// 增加计数器，记录将要进行的递归调用次数
				this.remainingCalls += data.children.length;
				data.children.forEach(v => {
					// 递归调用
					this.onMenuClick(index, v);
					// 减少计数器，这里是在递归调用后立即减少
					this.remainingCalls--;
					if (this.remainingCalls <= 0) {
						// 当计数器为0时，显示成功消息
						this.$message.success(index === 1 ? "配置成功" : "取消成功");
					}
				});
			} else {
				if (this.remainingCalls === 0) {
					// 如果没有子节点，且这是第一次调用，则直接显示成功消息
					this.$message.success(index === 1 ? "配置成功" : "取消成功");
				}
			}
		},
		showSuccessMessage(index) {
			this.$message.success(index === 1 ? "配置成功" : "取消成功");
		},
		/*
		 * 系统菜单点击事件
		 */
		async menuNodeClick(row, node, rootNode) {
			console.log(this.role)
			if (node === false) {
				this.cancelSelect(this.role, row);
			} else {
				this.selectChange(this.role, row);
			}
			this.$message.success(node ? "配置成功" : "取消成功");
		},
	},
};
</script>

<style scoped>
.el-dialog__body {
	padding: 10px 20px;
}
</style>
