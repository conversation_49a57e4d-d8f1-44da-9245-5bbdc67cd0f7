import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/product/erpCommodityApproval/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/product/erpCommodityApproval/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/product/erpCommodityApproval/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/product/erpCommodityApproval/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/product/erpCommodityApproval/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/product/erpCommodityApproval/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/product/erpCommodityApproval/import',
      data
    )
  }
}
