import http from "@/utils/request"

export default {
  /**
   * 新增资质信息
   * @param {Object} inputForm 资质表单数据 { name, isPublic, fileInfos }
   * @returns {Promise} 请求结果
   */
  add: function (inputForm) {
    return http.post(
      '/archive/public/qc/add',
      inputForm
    )
  },

  /**
   * 编辑资质信息
   * @param {Object} inputForm 资质表单数据 { id, name, isPublic, fileInfos }
   * @returns {Promise} 请求结果
   */
  edit: function (inputForm) {
    return http.post(
      '/archive/public/qc/edit',
      inputForm
    )
  },

  /**
   * 根据ID查询资质详情
   * @param {String} id 资质主键id
   * @returns {Promise} 请求结果
   */
  getById: function (id) {
    return http.get(
      `/archive/public/qc/getById/${id}`
    )
  },

  /**
   * 更新资质公开状态
   * @param {String} id 资质主键id
   * @returns {Promise} 请求结果
   */
  updatePublicStatus: function (id) {
    return http.get(
      `/archive/public/qc/edit/isPublic/${id}`
    )
  },

  /**
   * 获取公开资质分页列表
   * @param {Object} params 查询参数 { current, size, name }
   * @returns {Promise} 请求结果
   */
  getPublicPage: function (params) {
    return http.get(
      '/archive/public/qc/page',
      params
    )
  },

  /**
   * 下载资质文件
   * @param {Object} params 下载参数
   * @param {Object} config 配置参数
   * @param {String} resDetail 响应详情
   * @param {String} responseType 响应类型
   * @returns {Promise} 请求结果
   */
  download: function (params, config = {}, resDetail, responseType) {
    // 如果指定了responseType，将其添加到config中
    if (responseType) {
      config = { ...config, responseType: responseType }
    }
    return http.get(`/archive/public/qc/download/${params.id}`, {}, config, resDetail)
  },

  /**
   * 批量下载资质文件
   * @param {Object} params 下载参数 { ids: Array }
   * @param {Object} config 配置参数
   * @param {String} resDetail 响应详情
   * @param {String} responseType 响应类型
   * @returns {Promise} 请求结果
   */
  batchDownload: function (params, config = {}, resDetail, responseType) {
    // 如果指定了responseType，将其添加到config中
    if (responseType) {
      config = { ...config, responseType: responseType }
    }
    return http.get('/archive/public/qc/download/all', {}, config, resDetail)
  },
}
