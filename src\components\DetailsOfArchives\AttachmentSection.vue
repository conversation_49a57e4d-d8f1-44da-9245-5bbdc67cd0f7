<template>
	<div v-if="tableData.length > 0">附件总数: {{ tableData.length }}</div>
	<el-container class="resizable-container">
		<el-aside :width="leftWidth + 'px'" style="padding-left: 0px">
			<div
				v-for="item in tableData"
				:key="item"
				class="fileUrl"
                @click="handleViewFile(item.fileUrl)"
			>
				<div style="display: flex; justify-content: space-between">
					<el-tooltip
						:content="item.fileName"
						class="box-item"
						effect="dark"
						placement="right"
					>
						<el-text style="cursor: pointer" truncated>
							{{ item.fileName }}
						</el-text>
					</el-tooltip>
				</div>
			</div>
		</el-aside>

		<!-- 拖动条 -->
		<div
			class="resize-handle"
			@mousedown="$emit('start-resize', $event)"
		></div>

		<el-main style="background-color: #e4e7ed; padding: 5px">
			<el-scrollbar ref="scrollbar" style="border-radius: 5px">
				<div
					v-if="tableData.length > 0 && !pdfError"
					ref="main"
					v-loading="pdfLoading"
					style="width: 100%; height: 100%"
				>
					<PDFViewer
						:src="pdfRef"
						height="100%"
						pageScale="page-fit"
						theme="light"
						width="100%"
						@loaded="onLoaded"
						@error="onPdfError"
					/>
				</div>

				<div
					v-else-if="tableData.length > 0 && pdfError"
					class="error-container"
				>
					<el-result
						:icon="pdfErrorType === 'download' ? 'info' : 'warning'"
						:title="
							pdfErrorType === 'download'
								? 'PDF文件仅支持下载'
								: 'PDF预览失败'
						"
					>
						<template #sub-title>
							<div v-if="pdfErrorType === 'download'">
								<p>
									该PDF文件被服务器配置为下载模式，无法在线预览
								</p>
								<p>请点击下载按钮获取文件后查看</p>
							</div>
							<div v-else-if="pdfErrorType === 'network'">
								<p>PDF文件被浏览器阻止加载，可能的解决方案：</p>
								<ul style="text-align: left; margin: 10px 0">
									<li>检查浏览器的广告拦截器设置</li>
									<li>刷新页面重新加载</li>
									<li>检查网络连接</li>
								</ul>
							</div>
							<div v-else-if="pdfErrorType === 'notfound'">
								<p>PDF文件不存在或已被删除</p>
								<p>请联系管理员确认文件状态</p>
							</div>
							<div v-else-if="pdfErrorType === 'permission'">
								<p>没有权限访问该PDF文件</p>
								<p>请联系管理员获取访问权限</p>
							</div>
							<div v-else>
								<p>PDF文件无法在浏览器中预览，可能的原因：</p>
								<ul style="text-align: left; margin: 10px 0">
									<li>服务器配置问题</li>
									<li>文件格式不兼容</li>
									<li>网络连接异常</li>
								</ul>
							</div>
						</template>
						<template #extra>
							<el-button
								v-if="pdfErrorType !== 'notfound'"
								type="primary"
								@click="downloadPdf"
							>
								下载文件
							</el-button>
						</template>
					</el-result>
				</div>

				<div v-else class="empty-container">
					<el-result icon="info" title="温馨提醒">
						<template #sub-title>
							<p>此档案无相关附件</p>
						</template>
					</el-result>
				</div>
			</el-scrollbar>
		</el-main>
	</el-container>
</template>

<script setup>
import PDFViewer from "@/views/archiveReception/common/PDFViewer.vue";
import { defineProps, ref, getCurrentInstance,nextTick } from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
	tableData: {
		type: Array,
		default: () => [],
	},
	leftWidth: {
		type: Number,
		default: 300,
	},
});

defineEmits([ "download-pdf", "start-resize"]);
const pdfRef = ref();
const pdfLoading = ref(true);
const pdfError = ref(false);
const pdfErrorType = ref(""); // 错误类型：'download', 'network', 'permission', 'notfound', 'unknown'
defineExpose({
    loadFile
});
// PDF相关方法
const onLoaded = () => {
    // 改变pdfLoading的值

	pdfLoading.value = false;
	pdfError.value = false;
	pdfErrorType.value = "";
};
/**
 * 加载文件
 */
function loadFile(){
    if (props.tableData.length > 0) {

        if (!pdfRef.value) {
            pdfLoading.value = true;
            pdfError.value = false;
            pdfErrorType.value = "";
            pdfRef.value = props.tableData[0].fileUrl;
        }
    } else {
        pdfLoading.value = false;
        pdfError.value = false;
        pdfErrorType.value = "";
    }
}
const handleViewFile = (url) => {
    if (pdfRef.value !== url) {

        pdfLoading.value = true;
        pdfError.value = false;
        pdfErrorType.value = "";
        pdfRef.value = "";

        nextTick(() => {
            pdfRef.value = url;
        });
    }
};
const downloadPdf = () => {
    if (pdfRef.value) {
        const link = document.createElement("a");
        link.href = pdfRef.value;
        link.download = pdfRef.value.split("/").pop() || "document.pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
};
/**
 * 加载失败
 * @param error
 */
const onPdfError = (error) => {
	pdfLoading.value = false;
	pdfError.value = true;
	console.error("PDF加载失败:", error);

	const errorMessage =
		error?.message || error?.toString() || JSON.stringify(error) || "";

	if (
		errorMessage.includes("ERR_BLOCKED_BY_CLIENT") ||
		errorMessage.includes("net::")
	) {
		pdfErrorType.value = "network";
		proxy.msgError(
			"PDF文件被浏览器阻止加载，请检查广告拦截器设置或尝试刷新页面"
		);
	} else if (errorMessage.includes("404")) {
		pdfErrorType.value = "notfound";
		proxy.msgError("PDF文件不存在或已被删除");
	} else if (errorMessage.includes("403")) {
		pdfErrorType.value = "permission";
		proxy.msgError("没有权限访问该PDF文件");
	} else {
		pdfErrorType.value = "download";
		proxy.msgWarning(
			"PDF文件无法在线预览，可能是服务器配置为下载模式。请使用下载功能查看文件。"
		);
	}
};
</script>

<style scoped>
.fileUrl {
	cursor: pointer;
	border-top: 1px solid #e4e7ed;
	border-left: 1px solid #e4e7ed;
	border-bottom: 1px solid #e4e7ed;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
	transition: all 0.3s ease;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
	background-color: #f0f9ff;
}

.resizable-container {
	position: relative;
	display: flex;
	height: 100%;
}

.resize-handle {
	width: 6px;
	height: 100%;
	background-color: #e0e0e0;
	cursor: col-resize;
	position: relative;
	z-index: 10;
	transition: background-color 0.3s ease;
}

.resize-handle:hover,
.resize-handle.resizing {
	background-color: #1890ff;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	padding: 20px;
}

.empty-container {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: center;
	align-items: center;
	height: 100%;
}
</style>
