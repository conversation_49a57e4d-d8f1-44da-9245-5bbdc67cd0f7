import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post('/sys/notice/save',inputForm)
  },

  delete: function (ids) {
    return http.delete('/sys/notice/delete',ids)
  },

  queryById: function (id) {
    return http.get('/sys/notice/queryById',id)
  },

  list: function (params) {
    return http.get('/sys/notice/list',params)
  },

  exportTemplate: function () {
    return http.get('/sys/notice/import/template','blob')
  },

  exportExcel: function (params) {
    return http.get('/sys/notice/export',params,'blob')
  },

  importExcel: function (data) {
    return http.post( '/sys/notice/import', data)
  },
  release:function ({id,status}) {
    return http.get( `/sys/notice/updateStatusById?id=${id}&status=${status}`)
  },
}
