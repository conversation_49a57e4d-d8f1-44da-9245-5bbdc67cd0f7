import http from "@/utils/request";
// 系统配置  >  审核流程  api

export const applianceApi = {
	drugList: function (params) {
		return http.get("/erp/product/erpDrugsCommodity/list", params);
	},
	saveDrug: function (data) {
		return http.post("/erp/product/erpDrugsCommodity/save", data);
	},
	delDrug: function (params) {
		return http.delete("/erp/product/erpDrugsCommodity/delete", params);
	},
	typeDrug: function (params) {
		return http.get("/sys/dictValue/list", params);
	},
	natureDrug: function (params) {
		return http.get("/erp/assist/erpBaseCommonValues/listByEnable", params);
	},
	venderDrug: function (params) {
		return http.get("/erp/factory/erpManufacturerInformation/list", params);
	},
	treeDrug: function (params) {
		return http.get("/erp/assist/erpMassRangeSet/list", params);
	},
	siteDrug: function (params) {
		return http.get(
			"/erp/factory/erpManufacturerInformation/queryAddByName",
			params
		);
	},
};
