import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/product/erpFoodCommodity/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/product/erpFoodCommodity/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/product/erpFoodCommodity/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/product/erpFoodCommodity/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/product/erpFoodCommodity/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/product/erpFoodCommodity/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/product/erpFoodCommodity/import',
      data
    )
  }
}
