<template>
	<el-container>
		<el-main ref="main" class="noPadding">
			<div style="height: 600px;">
				<el-form ref="formRef" :inline="true" :model="form" :rules="rules" label-width="120px">
					<el-form-item label="借阅人:" prop="name" style="width: 30%;">
						<el-input v-model="form.name" disabled placeholder="请输入借阅人"/>
					</el-form-item>
					<el-form-item label="借阅部门:" prop="sysOfficeName" style="width: 30%;">
						<el-input v-model="form.sysOfficeName" disabled placeholder="请输入借阅部门"/>
					</el-form-item>
					<el-form-item label="借阅起始日期:" prop="borrowStartTime" style="width: 31%;">
						<el-date-picker v-model="form.borrowStartTime" format="YYYY-MM-DD HH:mm:ss"
										placeholder="请选择借阅起始日期" type="datetime"
										value-format="YYYY-MM-DD HH:mm:ss"/>
					</el-form-item>
					<el-form-item label="借阅归还日期:" prop="borrowEndTime" style="width: 30%;">
						<el-date-picker v-model="form.borrowEndTime" format="YYYY-MM-DD HH:mm:ss"
										placeholder="请选择借阅归还日期" type="datetime"
										value-format="YYYY-MM-DD HH:mm:ss"/>
					</el-form-item>
					<el-form-item label="是否包含水印:" prop="borrowIsWatermark" style="width: 30%;">
						<el-radio-group v-model="form.borrowIsWatermark">
							<el-radio label="是" value="1"/>
							<el-radio label="否" value="2"/>
						</el-radio-group>
					</el-form-item>
					<el-form-item label="借阅理由:" prop="borrowRemark" style="width: 96.2%;">
						<el-input v-model="form.borrowRemark" clearable maxlength="100"
								  placeholder="请输入借阅理由"
								  show-word-limit type="textarea"/>
					</el-form-item>
				</el-form>
				<div style="font-weight: bold;color: black;margin-bottom: 15px;">
					借阅明细
				</div>
				<el-table :data="contentList" border empty-text="暂无">
					<el-table-column align="center" min-width="30" type="selection" width="50"/>
					<el-table-column align="left" label="档案名称" prop="name"/>
					<el-table-column align="left" label="借阅方式" prop="retentionPeriod" width="320">
						<template #default="scope">
							<span>
								<el-checkbox-group v-model="scope.row.checkList">
									<el-checkbox checked label="1">电子</el-checkbox>
									<el-checkbox label="2">纸质</el-checkbox>
									<el-checkbox label="3">下载</el-checkbox>
									<el-checkbox label="4">打印</el-checkbox>
								</el-checkbox-group>
							</span>
						</template>
					</el-table-column>
					<el-table-column align="left" label="档案存址" prop="storageAddress" width="350">
						<template #default="scope">
							{{ scope.row.storageAddress ? scope.row.storageAddress : '暂无' }}
						</template>
					</el-table-column>
					<el-table-column align="left" label="所属机构" prop="org.name" width="150"/>
				</el-table>
			</div>
			<div style="display: flex;justify-content: flex-end;">
				<el-button @click="() => cancellation()">取消</el-button>
				<el-button type="primary" @click="() => determine()">确定</el-button>
			</div>
		</el-main>
	</el-container>
</template>

<script setup>
import {defineEmits, defineProps, getCurrentInstance, onMounted, reactive, ref} from 'vue'
import vitalizationArchiveList from '@/api/archive/vitalizationArchive'
import view from "@/api/archive/managementFile"
import tool from '@/utils/tool';

const emit = defineEmits(["childMove"]);
const props = defineProps({
	handList: {
		type: Array
	},
})
// 表单校验
const rules = reactive({
	name: [{required: true, message: '请输入借阅人', trigger: 'blur'},],
	sysOfficeName: [{required: true, message: '请输入借阅部门', trigger: 'blur'},],
	borrowStartTime: [
		{required: true, message: '请选择借阅归还日期', trigger: 'change'},
		{validator: dataEnter, trigger: 'blur'},
		{validator: dataEnter, trigger: 'change'},
	],
	borrowEndTime: [
		{required: true, message: '请选择门类', trigger: 'change'},
		{validator: dataOut, trigger: 'blur'},
		{validator: dataOut, trigger: 'change'},
	],
	borrowIsWatermark: [{required: true, message: '请选择是否包含水印', trigger: 'change'},],
	borrowRemark: [{required: true, message: '请输入借阅理由', trigger: 'blur'},],
})
const {proxy} = getCurrentInstance();
const form = ref({})

//初始方法
onMounted(() => {
	getList();
});

//时间判断
function dataEnter(rule, data, callback) {
	if (form.value.borrowStartTime > form.value.borrowEndTime) {
		callback(new Error('借阅起始日期不能大于借阅归还日期'));
	} else {
		callback();
	}
}

//时间判断
function dataOut(rule, data, callback) {
	if (form.value.borrowStartTime > form.value.borrowEndTime) {
		callback(new Error('借阅归还日期不能小于借阅起始日期'));
	} else {
		callback();
	}
}

// 处理数据
function getList() {
	form.value.name = tool.data.get('USER_INFO').name;
	form.value.sysOfficeName = tool.data.get('USER_INFO').sysOffice.name;
	queryById();
}

const contentList = ref([])

// 根据id获取档案详情
function queryById() {
	view.queryById({
		id: props.handList.content.id,
		showDeleteInfo: true
	}).then(res => {
		if (res.code == 200) {
			contentList.value.push(res.data);
			contentList.value.forEach(row => {
				row.checkList = [];
			})
		}
	}).catch(() => {

	})
}

// 确定提交新增
function determine() {
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			let tableArray = [];
			contentList.value.forEach(row => {
				tableArray.push({
					infoId: row.id,
					infoType: row.checkList.join(','),
				});
			})
			let data = {
				borrowApply: tool.data.get('USER_INFO').id,
				borrowIsWatermark: form.value.borrowIsWatermark,
				borrowRemark: form.value.borrowRemark,
				borrowStartTime: form.value.borrowStartTime,
				borrowEndTime: form.value.borrowEndTime,
				infoList: tableArray,
			}
			vitalizationArchiveList.recordBorrowApply(data).then(res => {
				if (res.code == 200) {
					tool.data.set('CAR', []);
					proxy.msgSuccess('借阅成功')
					emit("childMove");
				}
			}).catch(() => {
				proxy.msgError('借阅失败');
			})
		}
	})
}

// 取消
function cancellation() {
	emit("childMove");
}
</script>

<style scoped></style>
