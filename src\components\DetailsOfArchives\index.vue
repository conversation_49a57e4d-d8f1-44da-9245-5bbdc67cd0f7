<template>
	<el-tabs
		v-model="activeName"
		v-loading="loading"
		class="demo-tabs"
		@tab-click="handleClick"
	>
		<el-tab-pane label="档案信息" name="first" style="height: 63vh">
			<el-descriptions
				:column="4"
				border
				class="margin-top"
				size="default"
			>
				<el-descriptions-item
					align="center"
					label="档案名称"
					label-align="center"
				>
					{{ archivesData.name }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="全宗名称"
					label-align="center"
				>
					{{ archivesData.controlGroup?.recordGroupName || "暂无" }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="档案年份"
					label-align="center"
				>
					{{ archivesData.year }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="档案月份"
					label-align="center"
				>
					{{ archivesData.month }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="文件日期"
					label-align="center"
				>
					{{
						archivesData.createDate
							? moment(archivesData.createDate).format(
									"YYYY-MM-DD HH:mm:ss"
							  )
							: "暂无"
					}}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="保留年限"
					label-align="center"
				>
					{{ reserve(archivesData.retentionPeriod) }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="保密等级"
					label-align="center"
				>
					{{ secrecy(archivesData.protectLevel) }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="控制等级"
					label-align="center"
				>
					{{ control(archivesData.controlStatus) }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="所属机构"
					label-align="center"
				>
					{{ archivesData.org?.name || "暂无" }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="所属部门"
					label-align="center"
				>
					{{ archivesData.office?.name || "无" }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="档案标签"
					label-align="center"
				>
					<ArchiveTags
						:tag-ids="archivesData.tagManagerInfo"
						display-mode="text"
						:empty-text="'无'"
						:auto-load="false"
						:external-tags="tagsInfoList"
					/>
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="附件数量"
					label-align="center"
				>
					{{ archivesData.infoOfFileCount }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="备注"
					label-align="center"
				>
					{{ archivesData.remark }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="是否全电文档"
					label-align="center"
				>
					{{ archivesData.isElectronic === "1" ? "是" : "否" }}
				</el-descriptions-item>
				<el-descriptions-item
					align="center"
					label="档案摘要"
					label-align="center"
				>
					{{
						archivesData.digestContent
							? archivesData.digestContent
							: "无"
					}}
				</el-descriptions-item>
			</el-descriptions>
		</el-tab-pane>
		<el-tab-pane
			label="基本信息"
			name="second"
			style="height: 63vh; overflow-y: scroll"
		>
			<!-- 基本信息部分 -->
			<BasicInfoSection
				:basic-info-list="basicInfoList"
				:src-list="srcList"
				@open-img="openImg"
			/>

			<!-- 其他信息部分 -->
			<OtherInfoSection
				:descriptions-label="descriptionsLabel"
				:choose-collapse="chooseCollapse"
				:src-list="srcList"
				@open-img="openImg"
				@open-file-box="openFileBox"
			/>
		</el-tab-pane>
		<el-tab-pane label="附件" name="third" style="height: 63vh">
			<AttachmentSection
				ref="attachmentSection"
				:table-data="tableData"
				:left-width="leftWidth"
				@start-resize="startResize"
			/>
		</el-tab-pane>
		<el-main>
			<div style="float: right">
				<el-button plain @click="() => cancellation()">取消</el-button>
			</div>
		</el-main>

		<PdfViewPlus
			:file-data-list="fileBoxDataList"
			:open-status="fileBoxStatus"
			@closeFileBox="
				() => {
					fileBoxStatus = false;
				}
			"
		/>
	</el-tabs>
	<div class="floating-btn" v-if="activeName === 'third'">
		<el-button type="warning" icon="Download" @click="batchDownload"
			>批量下载</el-button
		>
	</div>
</template>

<script setup>
import view from "@/api/archive/managementFile";
import {
	defineProps,
	getCurrentInstance,
	onBeforeUnmount,
	onMounted,
	ref,
} from "vue";
import moment from "moment/moment";
import tagsManagement from "@/api/archive/tagsManagement";
import PdfViewPlus from "@/components/pdfViewPlus/index.vue";
import ArchiveTags from "@/components/DetailsOfArchives/ArchiveTags.vue";
import BasicInfoSection from "./BasicInfoSection.vue";
import OtherInfoSection from "./OtherInfoSection.vue";
import AttachmentSection from "./AttachmentSection.vue";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["childEvent"]);
const activeName = ref("first");
const props = defineProps({
	receiveId: {
		type: Object,
	},
});

// 响应式数据
const loading = ref(true);
const attachmentSection = ref(null);
const fileBoxStatus = ref(false);
const srcList = ref([]);
const tagsInfoList = ref([]);
const fileBoxDataList = ref([]);
const basicInfoList = ref([]);
const descriptionsLabel = ref([]);
const tableData = ref([]);
const archivesData = ref({});
const chooseCollapse = ref([]);
const leftWidth = ref(300);

// 拖动相关状态
const isResizing = ref(false);
const startPosition = { x: 0 };

// 生命周期
onMounted(() => {
	queryById();
	getInfoByMasterId();
	fileList();
	loading.value = false;
});

onBeforeUnmount(() => {
	window.removeEventListener("mousemove", handleMouseMove);
	window.removeEventListener("mouseup", stopResize);
});

// 数据查询方法
const queryById = async () => {
	try {
		const res = await view.queryById({
			id: props.receiveId.id,
			showDeleteInfo: true,
		});

		if (res.code === 200) {
			loading.value = false;
			archivesData.value = res.data;

			// 获取标签信息
			const tagsRes = await tagsManagement.getList({
				current: 1,
				size: -1,
			});

			if (tagsRes.code === 200) {
				tagsInfoList.value = tagsRes.data?.records || [];
			}
		}
	} catch (error) {
		proxy.msgError("查询失败");
	}
};

const getInfoByMasterId = async () => {
	loading.value = true;
	try {
		const res = await view.getDataByMasterId({
			id: props.receiveId.id,
		});

		if (res.code === 200) {
			let dataList = res.data || [];
			dataList = sortTreeNodes(dataList);

			basicInfoList.value = dataList.filter((item) => !item.children);
			descriptionsLabel.value = dataList.filter((item) => item.children);

			// 初始化折叠面板
			chooseCollapse.value = Array.from(
				{ length: descriptionsLabel.value.length + 1 },
				(_, i) => i
			);

			// 处理表格数据
			descriptionsLabel.value.forEach((label) => {
				const tableDataList = [];
				const data = {};
				label.children.forEach((item) => {
					data[item.name] = item.value;
				});
				tableDataList.push(data);
				label.tableDataList = tableDataList;
			});

			// 过滤基本信息
			basicInfoList.value = basicInfoList.value.filter((label) => {
				return (
					label.name &&
					label.name !== "操作记录" &&
					label.name !== "供应商审核记录" &&
					!label.name.includes("审核记录") &&
					label?.config?.type &&
					!isNaN(Number(label.config?.type || 0))
				);
			});

			loading.value = false;
		}
	} catch (error) {
		proxy.msgError("查询失败");
	}
};

const fileList = async () => {
	try {
		const res = await view.fileList({
			"recordInfo.id": props.receiveId.id,
			fileType: "pdf",
			current: 1,
			size: -1,
		});

		if (res.code === 200) {
			tableData.value = res.data.records;
		}
	} catch (error) {
		proxy.msgError("查询失败");
	}
};

// 工具方法
const sortTreeNodes = (data) => {
	if (!Array.isArray(data)) {
		return [];
	}

	data.sort((a, b) => (a.config?.sort || 0) - (b.config?.sort || 0));
	data = data.filter((item) => item.config?.isView !== "1");

	for (let node of data) {
		if (node.children) {
			if (
				Array.isArray(node.children) &&
				!Array.isArray(node.children[0])
			) {
				node.children = sortTreeNodes(node.children);
			} else if (
				Array.isArray(node.children) &&
				Array.isArray(node.children[0])
			) {
				node.children = node.children.map((childArray) =>
					sortTreeNodes(childArray)
				);
			}
		}
	}

	return data;
};

// 事件处理方法
const handleClick = (tabInfo) => {
	if (tabInfo.props.name === "third") {
		attachmentSection.value.loadFile();
	}
};

/**
 * 文件预览
 * @param imgUrl
 */
const openImg = (imgUrl) => {
	srcList.value = [imgUrl];
};

const openFileBox = (jsonString) => {
	try {
		const data = JSON.parse(jsonString);
		const dataArray = Array.isArray(data) ? data : [data];

		fileBoxDataList.value = dataArray.map((item) => {
			for (const key in item) {
				if (key.toLowerCase().includes("name")) {
					item["fileName"] = item[key];
				}
				if (key.toLowerCase().includes("url")) {
					item["fileUrl"] = item[key];
				}
			}

			if (!item.fileName && item.fileUrl) {
				item["fileName"] = item.fileUrl.substring(
					item.fileUrl.lastIndexOf("/") + 1
				);
			}

			return item;
		});
		fileBoxStatus.value = true;
	} catch (e) {
		return [];
	}
};

// 拖动相关方法
const startResize = (e) => {
	isResizing.value = true;
	startPosition.x = e.clientX;

	window.addEventListener("mousemove", handleMouseMove);
	window.addEventListener("mouseup", stopResize);
	document.body.style.userSelect = "none";
};

const handleMouseMove = (e) => {
	if (!isResizing.value) return;

	const dx = e.clientX - startPosition.x;
	const newWidth = leftWidth.value + dx;

	if (newWidth > 100 && newWidth < window.innerWidth - 100) {
		leftWidth.value = newWidth;
	}

	startPosition.x = e.clientX;
};

const stopResize = () => {
	isResizing.value = false;
	window.removeEventListener("mousemove", handleMouseMove);
	window.removeEventListener("mouseup", stopResize);
	document.body.style.userSelect = "";
};

// 格式化方法
const reserve = (val) => {
	const reserveMap = {
		Y: "永久",
		D5: "5年",
		D10: "10年",
		D20: "20年",
		D30: "30年",
	};
	return reserveMap[val] || "无";
};

const secrecy = (val) => {
	const secrecyMap = {
		GK: "公开",
		KZ: "限制",
		MOM: "秘密",
		JM: "机密",
		UM: "绝密",
	};
	return secrecyMap[val] || "无";
};

const control = (val) => {
	const controlMap = {
		1: "公开",
		2: "公司内部开放",
		3: "部门内部开放",
		4: "控制",
	};
	return controlMap[val] || "无";
};

const cancellation = () => {
	emit("childMove");
};
const batchDownload = () => {
	if (tableData.value.length === 0) {
		proxy.msgWarning("没有可下载的附件");
		return;
	}

	const fileUrls = tableData.value
		.map((item) => item.fileUrl)
		.filter(Boolean);
	if (fileUrls.length === 0) {
		proxy.msgWarning("没有可下载的附件");
		return;
	}

	const zip = new JSZip();
	const promises = fileUrls.map((url) =>
		fetch(url)
			.then((response) => response.blob())
			.then((blob) => {
				const fileName = url.substring(url.lastIndexOf("/") + 1);
				zip.file(fileName, blob);
			})
	);

	Promise.all(promises)
		.then(() => zip.generateAsync({ type: "blob" }))
		.then((content) => {
			const link = document.createElement("a");
			link.href = URL.createObjectURL(content);
			link.download = "attachments.zip";
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		})
		.catch((error) => {
			proxy.msgError("下载失败，请稍后重试");
			console.error("Download error:", error);
		});
};
</script>

<style scoped>
.demo-tabs {
	height: 100%;
}

:deep(.el-tabs__content) {
	height: calc(100% - 40px);
}

:deep(.el-tab-pane) {
	height: 100%;
}

:deep(.labelCustomClass) {
	width: 12.5%;
	max-width: 12.5%;
}

:deep(.el-scrollbar__view) {
	width: 100%;
	height: 100%;
}

:deep(.el-table__cell) {
	position: static !important;
}
.floating-btn {
	position: absolute;
	top: 60px;
	right: 10px;
	z-index: 1000;
}
</style>
