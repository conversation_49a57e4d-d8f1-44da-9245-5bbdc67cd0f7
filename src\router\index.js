import {createRouter, createWebHashHistory} from 'vue-router';
import {ElMessage, ElNotification} from 'element-plus';
import config from "@/config"
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import tool from '@/utils/tool';
import systemRouter from './systemRouter';
import userRoutes from '@/config/route';
import homeApi from '@/api/sys/home'
import objectUtils from "@/utils/objectUtils";
import systemApi from '@/api/model/system';
import {afterEach, beforeEach} from './scrollBehavior';

//系统路由
const routes = systemRouter

//系统特殊路由
const routes_404 = {
	path: "/:pathMatch(.*)*",
	hidden: true,
	component: () => import(/* webpackChunkName: "404" */ '@/layout/other/404'),
}
let routes_404_r = () => {
}

const router = createRouter({
	history: createWebHashHistory(),
	routes: routes
})

//设置标题
document.title = config.APP_NAME

//判断是否已加载过动态/静态路由
let isGetRouter = false;

router.beforeEach(async (to, from, next) => {
	NProgress.start()
	//动态标题
	document.title = to.meta.title ? `${to.meta.title} - ${config.APP_NAME}` : `${config.APP_NAME}`
	if (to.path === '/login' && to.query?.code) {
		let res = await userCenterLogin({code: to.query?.code})
		if (res.code === 200) {
			tool.cookie.set("TOKEN", res.data.token);
			tool.data.set("USER_INFO", res.data.userInfo);
			tool.data.set("ROLE_LIST", res.data.roleList);
			tool.data.set("Organization", res.data?.orgList);
		} else {
			ElMessage.error(res.msg)
			router.replace('/login')
		}
		if (res.data?.orgList?.length) {
			let response = await systemApi.menuTreeByOrg({orgId: res.data.orgList[0]?.id})
			if (response.code === 200) {
				let menuTree = sysMenuToUiTree(response.data)
				tool.data.set("MENU", menuTree);
				let permissions = getPermissions(response.data)
				tool.data.set("PERMISSIONS", permissions);
				tool.data.set("orgKey", 0);

				console.log(menuTree);
				let filterAdmin = menuTree.filter(menu => menu.name === 'adminHome');
				let filterUser = menuTree.filter(menu => menu.name === 'userHome');
				if (filterAdmin.length > 0) {
					console.log(filterAdmin, 'filterAdmin');
					router.push({path: filterAdmin[0].path});
				} else if (filterUser.length > 0) {
					console.log(filterUser, 'filterUser');
					router.push({path: filterUser[0].path});
				} else {
					router.replace('/home');
				}
			} else {
				router.replace('/login')
			}
		}
		let token = tool.cookie.get("TOKEN");
		if (to.path === "/login") {
			//删除路由(替换当前layout路由)
			router.addRoute(routes[0])
			//删除路由(404)
			routes_404_r()
			isGetRouter = false;
			next();
			return false;
		}
		if (routes.findIndex(r => r.path === to.path) >= 0) {
			next();
			return false;
		}

		if (!token) {
			next({
				path: '/login'
			});
			return false;
		}

		//整页路由处理
		if (to.meta.fullpage) {
			to.matched = [to.matched[to.matched.length - 1]]
		}
		//加载动态/静态路由
		if (!isGetRouter) {
			let apiMenu = tool.data.get("MENU") || []
			let userInfo = tool.data.get("USER_INFO")
			let userMenu = treeFilter(userRoutes, node => {
				return node.meta.role ? node.meta.role.filter(item => userInfo.role.indexOf(item) > -1).length > 0 : true
			})
			let menu = [...userMenu, ...apiMenu]
			let menuRouter = filterAsyncRouter(menu)
			menuRouter = flatAsyncRoutes(menuRouter)
			menuRouter.forEach(item => {
				router.addRoute("layout", item)
			})
			routes_404_r = router.addRoute(routes_404)
			if (to.matched.length === 0) {
				router.push(to.fullPath);
			}
			isGetRouter = true;
		}
		beforeEach(to, from)
		next();
	} else {
		let token = tool.cookie.get("TOKEN");
		if (to.path === "/login") {
			//删除路由(替换当前layout路由)
			router.addRoute(routes[0])
			//删除路由(404)
			routes_404_r()
			isGetRouter = false;
			next();
			return false;
		}
		if (routes.findIndex(r => r.path === to.path) >= 0) {
			next();
			return false;
		}

		if (!token) {
			next({
				path: '/login'
			});
			return false;
		}

		//整页路由处理
		if (to.meta.fullpage) {
			to.matched = [to.matched[to.matched.length - 1]]
		}
		//加载动态/静态路由
		if (!isGetRouter) {
			let apiMenu = tool.data.get("MENU") || []
			let userInfo = tool.data.get("USER_INFO")
			let userMenu = treeFilter(userRoutes, node => {
				return node.meta.role ? node.meta.role.filter(item => userInfo.role.indexOf(item) > -1).length > 0 : true
			})
			let menu = [...userMenu, ...apiMenu]
			let menuRouter = filterAsyncRouter(menu)
			menuRouter = flatAsyncRoutes(menuRouter)
			menuRouter.forEach(item => {
				router.addRoute("layout", item)
			})
			routes_404_r = router.addRoute(routes_404)
			if (to.matched.length === 0) {
				router.push(to.fullPath);
			}
			isGetRouter = true;
		}
		beforeEach(to, from)
		next();
	}
});

router.afterEach((to, from) => {
	afterEach(to, from)
	NProgress.done()
});

router.onError((error) => {
	const pattern = /Loading chunk (\d)+ failed/g;
	const isChunkLoadFailed = error.message.match(pattern);
	// 修复 Vue Router 4 中 pending 属性访问问题
	const targetPath = router.currentRoute.value?.fullPath || '/';
	if (isChunkLoadFailed) {
		router.replace(targetPath);
	} else {
		NProgress.done();
		ElNotification.error({
			title: '路由错误',
			message: error.message
		});
	}
});

//入侵追加自定义方法、对象
router.sc_getMenu = () => {
	let apiMenu = tool.data.get("MENU") || [];
	let userInfo = tool.data.get("USER_INFO");
	let userMenu = treeFilter(userRoutes, node => {
		return node.meta.role ? node.meta.role.filter(item => userInfo.role.indexOf(item) > -1).length > 0 : true
	});
	return [...userMenu, ...apiMenu]
}

//用户中心登录
function userCenterLogin(params) {
	return homeApi.codeLogin({code: params.code})
}

const getPermissions = (sysMenu) => {
	let permissions = [];
	for (let i = 0; i < sysMenu.length; i++) {
		if (objectUtils.isNotEmpty(sysMenu[i].permission)) {
			permissions.push(sysMenu[i].permission);
		}
		if (objectUtils.isEmpty(sysMenu[i].children) === false) {
			let pe = getPermissions(sysMenu[i].children);
			for (let j = 0; j < pe.length; j++) {
				if (objectUtils.isNotEmpty(pe[j])) {
					permissions.push(pe[j]);
				}
			}
		}
	}
	return permissions;
}

const sysMenuToUiTree = (sysMenu) => {
	let menuTree = [];
	for (let i = 0; i < sysMenu.length; i++) {
		menuTree[i] = {
			name: sysMenu[i].alias,
			path: sysMenu[i].path,
			meta: {
				title: sysMenu[i].name,
				icon: sysMenu[i].logo,
				type: sysMenu[i].type,
				hidden: sysMenu[i].hide,
				color: sysMenu[i].affix,
				fullpage: sysMenu[i].wholePageRoute,
			},
			component: sysMenu[i].view,
		};
		if (objectUtils.isEmpty(sysMenu[i].children) === false) {
			menuTree[i].children = sysMenuToUiTree(sysMenu[i].children);
		}
	}
	return menuTree
}

//转换
function filterAsyncRouter(routerMap) {
	const accessedRouters = []
	routerMap.forEach(item => {
		item.meta = item.meta ? item.meta : {};
		//处理外部链接特殊路由
		if (item.meta.type === 'iframe') {
			item.meta.url = item.path;
			item.path = `/${item.name}`;
		}
		//MAP转路由对象
		let route = {
			path: item.path,
			name: item.name,
			meta: item.meta,
			redirect: item.redirect,
			children: item.children ? filterAsyncRouter(item.children) : null,
			component: loadComponent(item.component)
		}
		accessedRouters.push(route)
	})
	return accessedRouters
}

function loadComponent(component) {
	if (component) {
		return () => import(/* webpackChunkName: "[request]" */ `@/views/${component}`)
			.catch(error => {
				console.error(`Failed to load component: @/views/${component}`, error);
				ElNotification.error({
					title: '组件加载失败',
					message: `无法找到组件: ${component}，请检查菜单配置中的组件路径是否正确`
				});
				// 返回空页面组件作为后备
				return import(`@/layout/other/empty`);
			});
	} else {
		return () => import(`@/layout/other/empty`)
	}
}

//路由扁平化
function flatAsyncRoutes(routes, breadcrumb = []) {
	let res = []
	routes.forEach(route => {
		const tmp = {...route}
		if (tmp.children) {
			let childrenBreadcrumb = [...breadcrumb]
			childrenBreadcrumb.push(route)
			let tmpRoute = {...route}
			tmpRoute.meta.breadcrumb = childrenBreadcrumb
			delete tmpRoute.children
			res.push(tmpRoute)
			let childrenRoutes = flatAsyncRoutes(tmp.children, childrenBreadcrumb)
			childrenRoutes.map(item => {
				res.push(item)
			})
		} else {
			let tmpBreadcrumb = [...breadcrumb]
			tmpBreadcrumb.push(tmp)
			tmp.meta.breadcrumb = tmpBreadcrumb
			res.push(tmp)
		}
	})
	return res
}

//过滤树
function treeFilter(tree, func) {
	return tree.map(node => ({...node})).filter(node => {
		node.children = node.children && treeFilter(node.children, func)
		return func(node) || (node.children && node.children.length)
	})
}

export default router
