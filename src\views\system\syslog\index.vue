<template>
	<el-container>
		<el-header>
			<el-form :inline="true" :model="formInfo">
				<el-form-item label="日志日期" style="margin: 0 21px 0 0">
					<el-date-picker
						v-model="formInfo.timeInterval"
						end-placeholder="结束日期"
						range-separator="至"
						start-placeholder="开始日期"
						type="datetimerange"
						value-format="YYYY-MM-DD HH:mm:ss"
						@change="chooseTime"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="日志类型" style="margin: 0 21px 0 0">
					<el-select v-model="formInfo.logType" clearable placeholder="请选择日志类型" style="width: 200px"
							   @change="chooseType">
						<el-option label="登录日志" value="0"></el-option>
						<el-option label="提示日志" value="1"></el-option>
						<el-option label="警告日志" value="2"></el-option>
						<el-option label="错误日志" value="3"></el-option>
						<el-option label="严重日志" value="4"></el-option>
						<el-option label="新增日志" value="5"></el-option>
						<el-option label="修改日志" value="6"></el-option>
						<el-option label="删除日志" value="7"></el-option>
					</el-select>
				</el-form-item>
			</el-form>
		</el-header>
		<el-main class="noPadding">
			<el-container>
				<el-header style="height: 242px;padding: 0">
					<scEcharts :option="logsChartOption"></scEcharts>
				</el-header>
				<el-main class="noPadding">
					<ytzhTable
						ref="dataTable"
						:data="dataList"
						:pageChangeHandle="getDataList"
						:refreshDataListHandle="getDataList"
						:tablePage="tablePage"
						highlightCurrentRow
						row-key="id"
						stripe
						@selection-change="selectionChange"
					>
						<el-table-column align="center" label="序号" width="80">
							<template #default="scope">
								{{ scope.$index + 1 }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="级别" prop="logGrade" width="92">
							<template #default="scope">
								<el-tag v-if="scope.row.logGrade === 'debug'" effect="dark" round type="primary">
									{{ "调试" }}
								</el-tag>
								<el-tag v-else-if="scope.row.logGrade === 'info'" effect="dark" round type="info">
									{{ "信息" }}
								</el-tag>
								<el-tag v-else-if="scope.row.logGrade === 'warn'" effect="dark" round type="warning">
									{{ "警告" }}
								</el-tag>
								<el-tag v-else-if="scope.row.logGrade === 'error'" effect="dark" round type="danger">
									{{ "错误" }}
								</el-tag>
								<el-tag v-else-if="scope.row.logGrade === 'fatal'" effect="dark" round type="danger">
									{{ "严重" }}
								</el-tag>
								<el-tag v-else effect="dark" round type="info">
									{{ "无信息" }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column align="center" label="日志类型" prop="logType" width="180">
							<template #default="scope">
								<el-text v-if="scope.row.logType === '0'" size="small">登录日志</el-text>
								<el-text v-else-if="scope.row.logType === '1'" size="small">提示日志</el-text>
								<el-text v-else-if="scope.row.logType === '2'" size="small">警告日志</el-text>
								<el-text v-else-if="scope.row.logType === '3'" size="small">错误日志</el-text>
								<el-text v-else-if="scope.row.logType === '4'" size="small">严重日志</el-text>
								<el-text v-else-if="scope.row.logType === '5'" size="small">新增日志</el-text>
								<el-text v-else-if="scope.row.logType === '6'" size="small">修改日志</el-text>
								<el-text v-else-if="scope.row.logType === '7'" size="small">删除日志</el-text>
								<el-text v-else size="small">暂无数据</el-text>
							</template>
						</el-table-column>
						<el-table-column align="center" label="日志名称" prop="name" width="150">
							<template #default="scope">
								{{ scope.row.name ? scope.row.name : "暂无数据" }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="请求地址" min-width="150" prop="url">
							<template #default="scope">
								{{ scope.row.url ? scope.row.url : "暂无数据" }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="目标类" min-width="150" prop="pointClassUrl">
							<template #default="scope">
								{{ scope.row.pointClassUrl ? scope.row.pointClassUrl : "暂无数据" }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="操作用户" prop="controlUser.name" width="150">
							<template #default="scope">
								{{ scope.row.controlUser ? scope.row.controlUser.name : "暂无数据" }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="日志时间" prop="createDate" width="170">
							<template #default="scope">
								{{
									scope.row.createDate
										? moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss')
										: "暂无数据"
								}}
							</template>
						</el-table-column>
						<el-table-column align="center" fixed="right" label="操作" width="120">
							<template #default="scope">
								<el-button icon="View" link type="primary" @click="rowClick(scope.row)">
									详情
								</el-button>
							</template>
						</el-table-column>
					</ytzhTable>
				</el-main>
			</el-container>
		</el-main>
	</el-container>
	<el-drawer v-model="infoDrawer" :size="600" destroy-on-close title="日志详情">
		<info ref="info"></info>
	</el-drawer>
</template>

<script>
import info from "./info";
import scEcharts from "@/components/scEcharts";
import moment from "moment";

export default {
	name: "log",
	computed: {
		moment() {
			return moment
		}
	},
	components: {
		info,
		scEcharts
	},
	data() {
		return {
			infoDrawer: false,
			//柱状图数据
			barGraph: {
				dateList: [],
				valueList: {
					info: [],
					warn: [],
					debug: [],
					error: [],
					fatal: [],
					none: []
				}
			},
			logsChartOption: {},
			date: [],
			//数据列表
			dataList: [],
			//分页参数
			tablePage: {
				//数据总数
				total: 0,
				//当前页码
				currentPage: 1,
				//每页条数
				pageSize: 20,
			},
			formInfo: {
				timeInterval: [],
				logType: ""
			},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
		};
	},
	mounted() {
		//刷新数据列表
		this.getDataList();
	},
	methods: {
		rowClick(row) {
			this.infoDrawer = true;
			this.$nextTick(() => {
				this.$refs.info.setData(row);
			});
		},
		chooseTime(dateValue) {
			//刷新数据列表
			this.getDataList();
		},
		chooseType(typeValue) {
			//刷新数据列表
			this.getDataList();
		},
		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getDataList() {
			//页面加载
			this.listLoading = true;
			this.dataList = null;
			let res = await this.$API.sysLogService.list({
				//当前页码
				current: this.tablePage.currentPage,
				//每页条数
				size: this.tablePage.pageSize,
				//排序查询
				logType: this.formInfo.logType,
				//日期筛选-起始日期
				startDate: this.formInfo.timeInterval ? this.formInfo.timeInterval[0] : null,
				//日期筛选-结束日期
				endDate: this.formInfo.timeInterval ? this.formInfo.timeInterval[1] : null
			});
			if (res.code == 200) {
				//总数据条数
				this.tablePage.total = res.data.total;
				//数据列表
				this.dataList = res.data.records;
				//获取日期柱状图数据
				this.getBarGraphData();
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},
		/*
		 * 获取日期柱状图数据
		 */
		getBarGraphData() {
			this.barGraph.dateList = [];
			this.barGraph.valueList.info = [];
			this.barGraph.valueList.error = [];
			this.barGraph.valueList.warn = [];
			this.barGraph.valueList.fatal = [];
			this.barGraph.valueList.debug = [];
			this.barGraph.valueList.none = [];
			this.$API.sysLogService.selectCountByDate().then(result => {
				if (result.code === 200) {
					let dateSet = new Set();
					let dataList = result.data;
					if (dataList.length > 0) {
						dataList.forEach(item => {
							//添加日期
							dateSet.add(item.logDate);
							//处理日志等级信息
							let logGradeCountList = item.logGradeCounts.split(",");

							logGradeCountList.forEach(logGradeCount => {
								if (logGradeCount.includes("info")) {
									this.barGraph.valueList.info.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("error")) {
									this.barGraph.valueList.error.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("warn")) {
									this.barGraph.valueList.warn.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("fatal")) {
									this.barGraph.valueList.fatal.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("debug")) {
									this.barGraph.valueList.debug.push(logGradeCount.split(":")[1]);
								}
								if (logGradeCount.includes("none")) {
									this.barGraph.valueList.none.push(logGradeCount.split(":")[1]);
								}
							});

							let noneGrade = ["info", "warn", "error", "fatal", "debug", "none"]
							noneGrade.forEach(grade => {
								if (!item.logGradeCounts.includes(grade)) {
									this.barGraph.valueList[grade].push(0);
								}
							})
						});
						this.barGraph.dateList = Array.from(dateSet);
					}
					//赋值数据图数据
					this.logsChartOption = {
						color: ["#007BFF", "#FFC107", "#6C757D", "#DC3545", "#B00020", "#000000"],
						grid: {
							top: 6,
							left: 42,
							right: 82,
							bottom: 52,
						},
						dataZoom: [
							{
								type: 'inside'
							},
							{
								type: 'slider'
							}
						],
						tooltip: {
							trigger: "axis",
							confine: true
						},
						xAxis: {
							type: "category",
							data: this.barGraph.dateList,
						},
						yAxis: {
							show: false,
							type: "value",
						},
						series: [
							{
								name: "信息",
								data: this.barGraph.valueList.info,
								type: "bar",
								stack: "total",
								barWidth: "42%",
							},
							{
								name: "警告",
								data: this.barGraph.valueList.warn,
								type: "bar",
								stack: "total",
								barWidth: "42%",
							},
							{
								name: "调试",
								data: this.barGraph.valueList.debug,
								type: "bar",
								stack: "total",
								barWidth: "42%",
							},
							{
								name: "错误",
								data: this.barGraph.valueList.error,
								type: "bar",
								stack: "total",
								barWidth: "42%",
							},
							{
								name: "致命",
								data: this.barGraph.valueList.fatal,
								type: "bar",
								stack: "total",
								barWidth: "42%",
							},
							{
								name: "其他",
								data: this.barGraph.valueList.none,
								type: "bar",
								stack: "total",
								barWidth: "42%",
							},
						],
					}
					//页面状态恢复
					this.listLoading = false;
				} else {
					//页面状态恢复
					this.listLoading = false;
					this.$Response.errorNotice(null, result.msg);
				}
			}).catch(error => {
				//页面状态恢复
				this.listLoading = false;
				this.$Response.errorNotice(error, "柱状图查询失败");
			});
		},
		/*
		 * 删除数据，行内删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:35:00
		 */
		async deleteData(row, index) {
			this.listLoading = true;
			let res = await this.$API.sysLogService.delete(row.id);
			if (res.code == 200) {
				this.$refs.dataTable.removeIndex(index);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
			this.listLoading = false;
		},
		/*
		 * 批量删除
		 * @author: 路正宁
		 * @date: 2023-03-24 14:36:11
		 */
		async deleteDatas() {
			//确认删除弹框
			let confirmRes = await this.$confirm(
				`确定删除选中的 ${this.selection.length} 项吗？`,
				"提示",
				{
					type: "warning",
					confirmButtonText: "删除",
					confirmButtonClass: "el-button--danger",
				}
			).catch(() => {
			});
			//确认结果处理
			if (!confirmRes) {
				return false;
			}
			//要删除的id数组
			let ids = this.selection.map((v) => v.id);
			//拼接的数组字符串，接口传参
			let idStr = this.selection.map((v) => v.id).join(",");
			//页面加载中
			this.listLoading = true;
			let res = await this.$API.sysLogService.delete(idStr);
			if (res.code == 200) {
				//从列表中移除已删除的数据
				this.$refs.dataTable.removeKeys(ids);
				this.$message.success("删除成功");
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
			//释放页面加载中
			this.listLoading = false;
		},
		/*
		 * 表格选择后回调事件
		 * @author: 路正宁
		 * @date: 2023-03-24 14:51:09
		 */
		selectionChange(selection) {
			this.selection = selection;
		},
	},
};
</script>

<style scoped>

</style>
