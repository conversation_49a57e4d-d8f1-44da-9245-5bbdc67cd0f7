<template>
	<el-container>
		<el-main class="noPadding">
			<el-card body-style="padding-top: 0;padding-bottom: 0;" class="box-card" style="margin:10px;">
				<el-tabs v-model="activeName" v-loading="loading" class="demo-tabs" @tab-change="handleClick">
					<el-tab-pane label="待审批" name="1">
						<div style="margin-bottom: 10px;">
							<el-form ref="formList" :inline="true" :model="form" label-position="right"
								label-width="auto">
								<el-form-item label="鉴定类型" prop="controlType" style="margin: 0;padding-right: 10px;">
									<el-select v-model="form.controlType" clearable filterable placeholder="请选择鉴定类型" style="width: 150px;">
										<el-option label="鉴定延期" value="1" />
										<el-option label="鉴定销毁" value="2" />
										<el-option label="开放鉴定" value="3" />
									</el-select>
								</el-form-item>
								<el-form-item :label-width="0" style="margin: 0;padding-left: 10px;">
									<el-button :icon="Search" type="primary" @click="() => handleQuery()">
										查询
									</el-button>
									<el-button :icon="RefreshRight" plain @click="() => resetQuery()">
										重置
									</el-button>
								</el-form-item>
							</el-form>
						</div>
						<el-divider style="margin: 10px 0" />
						<div class="card-header"
							style="display: flex;justify-content:space-between;align-items:center;margin-bottom: 10px;">
							<el-row :gutter="10">
								<el-col :span="1.5">
									<el-select v-model="batch" placeholder="请选择审核操作" style="width: 220px">
										<el-option label="批量同意" value="1" />
										<el-option label="批量驳回" value="2" />
									</el-select>
									<el-button :disabled="!handList.length || !batch" icon="Check" plain
										style="margin-left: 10px" type="primary" @click="() => handleAllSub()">
										确定
									</el-button>
								</el-col>
							</el-row>
							<div>
								<span style="margin-right: 15px;" @click="getList">
									<el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
										<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
											<Refresh />
										</el-icon>
									</el-tooltip>
								</span>
								<span @click="screen">
									<el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
										<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
											<el-icon-full-screen />
										</el-icon>
									</el-tooltip>
								</span>
							</div>
						</div>
						<el-table :data="receiveData" border @selection-change="handleSelectionChange">
							<el-table-column align="center" min-width="30" type="selection" width="50" />
							<el-table-column align="center" label="鉴定类型" prop="num" width="200">
								<template #default="scope">
									{{ control(scope.row.controlType) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="申请理由" prop="controlRemark">
								<template #default="scope">
									{{ scope.row.controlRemark ? scope.row.controlRemark : '暂无' }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="申请人" prop="controlApply.name" width="180" />
							<el-table-column align="center" label="申请时间" prop="createDate" width="180">
								<template #default="scope">
									{{ moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="需鉴定档案份数" prop="recordInfoIds" width="120">
								<template #default="scope">
									{{ scope.row.recordInfoIds.split(',').length + "份" }}
								</template>
							</el-table-column>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
								width="120">
								<template #default="scope">
									<el-button icon="Finished" link type="primary" @click="collectFile(scope.row, '1')">
										审批
									</el-button>
								</template>
							</el-table-column>
						</el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
								:total="total" style="padding: 22px" @pagination="getList()" />
						</div>
					</el-tab-pane>
					<el-tab-pane label="已审批" name="2">
						<div style="margin-bottom: 10px;">
							<el-form ref="formList" :inline="true" :model="form" label-position="right"
								label-width="auto">
								<el-form-item label="鉴定类型" prop="controlType" style="margin: 0;padding-right: 10px;">
									<el-select v-model="form.controlType" clearable filterable placeholder="请选择鉴定类型" style="width: 150px;">
										<el-option label="鉴定延期" value="1" />
										<el-option label="鉴定销毁" value="2" />
										<el-option label="开放鉴定" value="3" />
									</el-select>
								</el-form-item>
								<el-form-item :label-width="0" style="margin: 0;padding-left: 10px;">
									<el-button :icon="Search" type="primary" @click="() => handleQuery()">
										查询
									</el-button>
									<el-button :icon="RefreshRight" plain @click="() => resetQuery()">
										重置
									</el-button>
								</el-form-item>
							</el-form>
						</div>
						<el-divider style="margin: 10px 0" />
						<el-table :data="receiveStop" border @selection-change="handleSelectionChange">
							<el-table-column align="center" min-width="30" type="selection" width="50" />
							<el-table-column align="center" label="鉴定类型" prop="num" width="200">
								<template #default="scope">
									{{ control(scope.row.controlType) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="鉴定审批意见" prop="controlAuditResult">
								<template #default="scope">
									{{ scope.row.controlAuditResult ? scope.row.controlAuditResult : '暂无' }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="申请理由" prop="controlRemark">
								<template #default="scope">
									{{ scope.row.controlRemark ? scope.row.controlRemark : '暂无' }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="申请时间" prop="createDate" width="180">
								<template #default="scope">
									{{ moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="申请人" prop="controlApply.name" width="180" />
							<el-table-column align="center" label="鉴定档案份数" prop="recordInfoIds" width="120">
								<template #default="scope">
									{{ scope.row.recordInfoIds.split(',').length + "份" }}
								</template>
							</el-table-column>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
								width="150px">
								<template #default="scope">
									<el-button icon="View" link type="primary" @click="collectFile(scope.row, '2')">
										查看审批
									</el-button>
								</template>
							</el-table-column>
						</el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
								:total="total" style="padding: 22px" @pagination="getList()" />
						</div>
					</el-tab-pane>
				</el-tabs>
			</el-card>
		</el-main>
		<!-- 审批 -->
		<el-dialog v-if="open" v-model="open" :title="title" append-to-body width="1300px">
			<examine :receiveId="receiveId" :receiveType="receiveType" @childMove="parentView"></examine>
		</el-dialog>
		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-model="openList" :title="title" append-to-body width="600px">
			<span style="color:#333;line-height:30px;font-size:14px">
				将对以下{{ handList.length }}个审核任务进行批量操作，请确定!
			</span>
			<div v-for="(item, index) in handList" :key="index" style="display: flex;line-height:30px">
				<p style="width:40%">
					<span style="color:#333;font-size:14px;">ID：</span>
					<span style="color:#666">{{ item.id }}</span>
				</p>
				<p>
					<span style="color:#333;font-size:14px;">申请人：</span>
					<span style="color:#666">{{ item.controlApply.name }}</span>
				</p>
			</div>
			<div v-show="batch == '2' ? true : false" style="margin-top: 15px">
				<span>驳回原因: </span>
				<el-input v-model="idea" clearable placeholder="请输入驳回原因" style="width: 240px" />
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">
						确 定
					</el-button>
					<el-button @click="() => (openList = false)">
						取 消
					</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>
<script setup>
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue'
import approveList from '@/api/archive/managementFile/approve';
import sysNoticeService from "@/api/model/approve/sysNoticeService";
import examine from './examine.vue';
import tool from '@/utils/tool';
import moment from 'moment'
import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue'

const { proxy } = getCurrentInstance()
const data = reactive({
	form: {
		controlType: undefined
	},
	queryParams: {
		current: 1,
		size: 10,
	}
})
const total = ref(0)
const { queryParams, form } = toRefs(data)
const activeName = ref('1')

// 是否显示审批
const open = ref(false)
const title = ref('')
const receiveId = ref('')
const receiveType = ref('')

//待审批列表
const receiveData = ref([])
//已审批列表
const receiveStop = ref([])

const loading = ref(true)
const handList = ref([])

const batch = ref("");
const openList = ref(false);
const newFilArr = ref([]);
const idea = ref("");

onMounted(() => {
	getList();
})

// 审批
function collectFile(val, type) {
	receiveType.value = type;
	receiveId.value = val;
	title.value = type === '1' ? '审批' : '审批信息查看';
	open.value = true;
}

// 关闭审批
function parentView() {
	open.value = false;
	getList();
}

// 切换tab时
const handleClick = (tabName) => {
	activeName.value = tabName;
	getList();
}

function handleAllSub() {
	if (batch.value == "2") {
		title.value = "批量驳回";
	} else {
		title.value = "批量同意";
	}
    idea.value = '';
	openList.value = true;
}

function submitForm() {
	let ids = "";
	let status = "";
	if (batch.value == "2") {
		status = "11";
	} else {
		status = "30";
	}
	newFilArr.value = [];
	handList.value.filter((item) => {
		newFilArr.value.push(item.id);
	});
	ids = newFilArr.value.join(",");
	sysNoticeService.handbatch({
		ids: ids,
		status: status,
		idea: idea.value
	}).then((res) => {
		if (res.code === 200) {
			proxy.msgSuccess(batch.value === "2" ? "批量驳回成功" : "批量同意成功");
			openList.value = false;
			getList();
		} else {
			openList.value = false;
			proxy.msgError(res.msg);
			getList();
		}
	});
}


// 回显鉴定类型
function control(val) {
	if (val == '1') {
		return '鉴定延期';
	} else if (val == '2') {
		return '鉴定销毁';
	} else if (val == '3') {
		return '开放鉴定';
	}
}

function handleQuery() {
	loading.value = true;
	approveList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		controlStatus: activeName.value,
		controlType: form.value.controlType
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				receiveData.value = res.data.records;
			} else {
				receiveStop.value = res.data.records;
			}
			total.value = res.data.total;
			loading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 重置
function resetQuery() {
	form.value = {};
	getList();
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 进入时查询全部
function getList() {
	approveList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		controlStatus: activeName.value,
		controlType: form.value.controlType
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				receiveData.value = res.data.records;
			} else {
				receiveStop.value = res.data.records;
			}
			total.value = res.data.total;
			loading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}
</script>
<style scoped>
.fileUrl {
	margin-bottom: 10px;
	cursor: pointer;
}

.fileUrl:hover {
	color: #2a76f8;
}

p {
	width: 250px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
