export default {
	/*
     * 判断对象是否为空
     * @author: 路正宁
     * @date: 2023-03-21 11:24:25
     * @version: V1.0
    */
	isEmpty: function (data) {
		if(typeof data === 'undefined' || data == null || data === '' || data === '{}'){
            return true;
        }
        if(JSON.stringify(data)=='' || JSON.stringify(data)=='{}'){
            return true;
        }
        if(Object.keys(data).length==0){
            return true;
        }
        return false;
	},
    isNotEmpty: function (data) {
		return !(this.isEmpty(data));
	}
}
