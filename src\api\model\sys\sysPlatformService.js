import http from "@/utils/request"

/*
 *@description: 平台管理
 *@author: 路正宁
 *@date: 2023-03-17 11:51:14
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/platform/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/platform/delete',
       {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
       '/sys/platform/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/platform/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/sys/platform/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/platform/export',
       params,
       'blob'
    )
  },
  
  importExcel: function (data) {
    return http.post(
       '/sys/platform/import',
       data
    )
  }
}
