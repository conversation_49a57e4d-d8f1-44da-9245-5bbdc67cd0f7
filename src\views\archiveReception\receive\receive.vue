<template>
	<el-tabs v-model="activeName" @tab-click="handleClick" v-loading="loading">
		<el-tab-pane label="数据信息" name="first" style="height: 72vh;overflow-y: scroll;padding-right: 10px">
			<el-descriptions :column="4" border style="margin-bottom: 20px;width: 100%;">
				<template #title>
					<div class="cell-item" style="font-size: 14px;">
						{{ "基本信息" }}
					</div>
				</template>
				<el-descriptions-item
					v-for="(item, index) in basicInfoList"
                    :key="index"
					:label="item.config.remark"
					:span="item.config.displayMode === '1'
				  		? 1
				  		: item.config.displayMode === '2'
							? 2
							: item.config.displayMode === '3'
								? 4
								: null"
					align="left"
					label-align="left"
					class-name="labelCustomClass"
					label-class-name="labelCustomClass"
					min-width="12.5%">
					<div v-if="item.value" style="width: 100%;height: 100%">
						<div v-if="item.config.type === '1'" style="width: 100%;height: 100%">
							<el-popover v-if="item.value.length >= 320" :content="item.value ? item.value : '无'"
										placement="bottom" trigger="hover" width="60vw">
								<template #reference>
									<el-text line-clamp="6">
										{{ item.value ? item.value : "无" }}
									</el-text>
								</template>
								<el-scrollbar height="312px">
									{{ item.value ? item.value : "无" }}
								</el-scrollbar>
							</el-popover>
							<el-text v-else>
								{{ item.value ? item.value : "无" }}
							</el-text>
						</div>
						<div v-if="item.config.type === '2'" style="width: 100%;height: 100%;display: flex;
							justify-content: flex-start;align-items: center;column-gap: 10px;">
							<el-image v-for="(imgItem, index) in disposeImg(item.value)"
									  v-if="checkFileType(item.value) === 'image' && disposeImg(item.value)"
									  :key="index"
									  :preview-src-list="srcList"
									  :src="imgItem"
									  fit="fill"
									  style="width: 100px;height: 100px;border: 1px solid var(--el-border-color);
									  border-radius: var(--el-border-radius-base);
									  box-shadow: var(--el-box-shadow-lighter)"
									  @click="openImg(imgItem)">
								<template #error>
									<div class="image-slot">
										<el-text>
											{{ "无" }}
										</el-text>
									</div>
								</template>
							</el-image>
							<el-button v-else-if="checkFileType(item.value) === 'pdf'">
								<el-button link type="primary" @click="openFileBox(item.value)">
									查看
								</el-button>
							</el-button>
							<el-text v-else>
								{{ "无" }}
							</el-text>
						</div>
						<div v-if="item.config.type === '3'">
							{{ item.value }}
						</div>
						<div v-if="item.config.type === '4'">
							<el-text>
								{{ item.value ? moment(item.value).format(item.config.dataConfigValue) : "无" }}
							</el-text>
						</div>
						<div v-if="item.config.type === '5'">
							{{ item.value }}
						</div>
					</div>
					<el-text v-else>
						{{ "无" }}
					</el-text>
				</el-descriptions-item>
			</el-descriptions>
			<el-collapse v-model="chooseCollapse">
				<el-collapse-item v-for="(item, index) in descriptionsLabel" :name="index" :key="index">
					<template #title>
						<el-text tag="b" truncated>
							<el-icon>
								<Expand/>
							</el-icon>
							{{ item.config.remark }}
						</el-text>
					</template>
					<el-descriptions v-for="(childItem, index) in item.children" v-if="item.config.showMethod === '1'"
									 :column="4" border style="margin-bottom: 20px;width: 100%;">
						<template v-if="item.children.length > 1" #title>
							<el-divider direction="vertical" style="border-width: 4px;border-color: #e6e9f0;"/>
							<el-text>
								{{ "第" + (index + 1) + "个" }}
							</el-text>
						</template>
						<el-descriptions-item
							v-for="(deepChildItem, index) in childItem"
							:label="deepChildItem.config.remark"
							align="left"
							label-align="left"
							:span="deepChildItem.config.displayMode === '1'
								? 1
								: deepChildItem.config.displayMode === '2'
									? 2
									: deepChildItem.config.displayMode === '3'
										? 4
										: null"
							class-name="labelCustomClass"
							label-class-name="labelCustomClass"
							min-width="12.5%">
							<div v-if="deepChildItem.value" style="width: 100%;height: 100%">
								<div v-if="deepChildItem.config.type === '1'" style="width: 100%;height: 100%">
									<el-popover v-if="deepChildItem.value.length >= 320"
												:content="deepChildItem.value ? deepChildItem.value : '无'"
												placement="bottom"
												trigger="hover"
												width="60vw">
										<template #reference>
											<el-text line-clamp="6">
												{{ deepChildItem.value ? deepChildItem.value : "无" }}
											</el-text>
										</template>
										<el-scrollbar height="312px">
											{{ deepChildItem.value ? deepChildItem.value : "无" }}
										</el-scrollbar>
									</el-popover>
									<el-text v-else>
										{{ deepChildItem.value ? deepChildItem.value : "无" }}
									</el-text>
								</div>
								<div v-if="deepChildItem.config.type === '2'" style="width: 100%;height: 100%;
									display: flex;justify-content: flex-start;align-items: center;column-gap: 10px;">
									<el-image v-for="(imgItem, index) in disposeImg(deepChildItem.value)"
											  v-if="checkFileType(deepChildItem.value) === 'image' && disposeImg(deepChildItem.value)"
											  :key="index"
											  :preview-src-list="srcList" :src="imgItem" fit="fill"
											  style="width: 100px; height: 100px;border: 1px solid var(--el-border-color);
									  		  border-radius: var(--el-border-radius-base);box-shadow: var(--el-box-shadow-lighter)"
											  @click="openImg(imgItem)">
										<template #error>
											<div class="image-slot">
												<el-text>
													{{ "无" }}
												</el-text>
											</div>
										</template>
									</el-image>
									<el-button v-else-if="checkFileType(deepChildItem.value) === 'pdf'">
										<el-button type="primary" @click="openFileBox(deepChildItem.value)">
											<el-text>
												{{ "查看" }}
											</el-text>
										</el-button>
									</el-button>
									<el-text v-else>
										{{ "无" }}
									</el-text>
								</div>
								<div v-if="deepChildItem.config.type === '3'">
									{{ deepChildItem.value }}
								</div>
								<div v-if="deepChildItem.config.type === '4'">
									<el-text>
										{{
											deepChildItem.value
												? moment(deepChildItem.value).format(deepChildItem.config.dataConfigValue)
												: "无"
										}}
									</el-text>
								</div>
								<div v-if="deepChildItem.config.type === '5'">
									{{ deepChildItem.value }}
								</div>
							</div>
							<el-text v-else>
								{{ "无" }}
							</el-text>
						</el-descriptions-item>
					</el-descriptions>
					<el-table v-if="item.config.showMethod === '2'" :data="disposeTableData(item.children, 1)"
							  highlight-current-row stripe>
						<el-table-column align="center" label="序号" prop="sort" width="52">
							<template #default="scope">
								{{ scope.$index + 1 }}
							</template>
						</el-table-column>
						<el-table-column v-for="(column, index) in disposeTableData(item.children, 2)"
										 :key="index"
										 :label="column"
										 :min-width="disposeTableData(item.children, 1)[0][column].config.length"
										 :prop="column" align="center">
							<template #default="scope">
								<div v-if="scope.row[column].config.type === '1'">
									{{ scope.row[column].value ? scope.row[column].value : "无" }}
								</div>
								<div v-if="scope.row[column].config.type === '2'" style="width: 100%;height: 100%;
									display: flex;justify-content: center;align-items: center;column-gap: 10px;">
									<el-image v-for="(imgItem, index) in disposeImg(scope.row[column].value)"
											  v-if="checkFileType(scope.row[column].value) === 'image' && disposeImg(scope.row[column].value)"
											  :key="index" :preview-src-list="srcList" :src="imgItem" fit="fill"
											  style="width: 100px;height: 100px;border: 1px solid var(--el-border-color);
									  		  border-radius: var(--el-border-radius-base);box-shadow: var(--el-box-shadow-lighter)"
											  @click="openImg(imgItem)">
										<template #error>
											<div class="image-slot">
												<el-text>
													{{ "无" }}
												</el-text>
											</div>
										</template>
									</el-image>
									<el-button v-else-if="checkFileType(scope.row[column].value) === 'pdf'">
										<el-button link type="primary" @click="openFileBox(scope.row[column].value)">
											查看
										</el-button>
									</el-button>
									<el-image v-else style="width: 100px;height: 100px;">
										<template #error>
											<div class="image-slot">
												<el-text>
													{{ "无" }}
												</el-text>
											</div>
										</template>
									</el-image>
								</div>
								<div v-if="scope.row[column].config.type === '3'">
									{{ scope.row[column].value }}
								</div>
								<div v-if="scope.row[column].config.type === '4'">
									{{
										scope.row[column].value
											? moment(scope.row[column].value).format(scope.row[column].config.dataConfigValue)
											: "无"
									}}
								</div>
								<div v-if="scope.row[column].config.type === '5'">
									{{ scope.row[column].value }}
								</div>
							</template>
						</el-table-column>
					</el-table>
				</el-collapse-item>
			</el-collapse>
			<!--			<div v-for="(item, index) in descriptionsLabel" :key="index">-->
			<!--				<h6 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px;font-size: 14px;">-->
			<!--					{{ item.name }}-->
			<!--				</h6>-->
			<!--				<el-table :data="item.tableDataList" :table-layout="'auto'" border fit highlight-current-row>-->
			<!--					<el-table-column v-for="label in Object.keys(item.tableDataList[0])" :label="label" :prop="label"-->
			<!--									 align="center"-->
			<!--									 min-width="200">-->
			<!--						<template #default="scope">-->
			<!--							<div-->
			<!--								v-if="label.indexOf('有效期') > 0 || label.includes('日期') > 0 || label.includes('时间') > 0">-->
			<!--								{{ scope.row[label] ? moment(scope.row[label]).format('YYYY-MM-DD HH:mm:ss') : "无" }}-->
			<!--							</div>-->
			<!--							<div v-else-if="label.indexOf('图片') >= 0 || label.indexOf('备注') >= 0">-->
			<!--								<el-image :preview-src-list="srcList" :src="scope.row[label]" fit="fill"-->
			<!--										  preview-teleported style="width: 50px; height: 50px;"-->
			<!--										  @click="openImg(scope.row[label])">-->
			<!--									<template #error>-->
			<!--										<div class="image-slot">-->
			<!--											<el-icon>-->
			<!--												<Picture/>-->
			<!--											</el-icon>-->
			<!--										</div>-->
			<!--									</template>-->
			<!--								</el-image>-->
			<!--							</div>-->
			<!--							<div v-else>-->
			<!--								{{ scope.row[label] ? scope.row[label] : "无" }}-->
			<!--							</div>-->
			<!--						</template>-->
			<!--					</el-table-column>-->
			<!--				</el-table>-->
			<!--			</div>-->
		</el-tab-pane>
		<!--		<el-tab-pane label="数据操作记录" name="second" style="height: 72vh;">-->
		<!--			<div style="width: 100%;height: 100%">-->
		<!--				<h6 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px;font-size: 14px;">操作记录</h6>-->
		<!--				<LogQuery ref="logRef3"/>-->
		<!--			</div>-->
		<!--		</el-tab-pane>-->
		<el-tab-pane label="附件" name="second" style="height: 72vh;">

			<div v-if="tableData.length > 0">
				附件总数: {{ tableData.length }}
			</div>
			<el-container class="resizable-container">
				<el-aside :width="leftWidth + 'px'" style="padding-left: 0px;">
					<div v-for="item in tableData"
						 :key="item"
						 class="fileUrl"
						 @click="handleViewFile(item.fileUrl)">
						<div style="display: flex;justify-content: space-between;">
							<el-tooltip :content="item.fileName"
										class="box-item"
										effect="dark"
										placement="right">
								<el-text style="cursor: pointer;" truncated>
									{{ item.fileName }}
								</el-text>
							</el-tooltip>
						</div>
					</div>
				</el-aside>
				<!-- 拖动条 -->
				<div class="resize-handle" @mousedown="startResize"></div>
				<el-main style="background-color: #E4E7ED;padding: 5px">
					<el-scrollbar ref="scrollbar" style="border-radius: 5px">
						<div v-if="tableData.length > 0" ref="main" v-loading="pdfLoading"
							 style="width: 100%;height: 100%">
							<PDFViewer :src="pdfRef" height="100%" pageScale="page-fit"
									   theme="light" width="100%" @loaded="onLoaded"/>
						</div>
						<div v-else style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
							justify-content: center;align-items: center;">
							<el-result icon="info" title="温馨提醒">
								<template #sub-title>
									<p>此档案无相关附件</p>
								</template>
							</el-result>
						</div>
					</el-scrollbar>
				</el-main>
			</el-container>
		</el-tab-pane>

		<PdfViewPlus :file-data-list="fileBoxDataList" :open-status="fileBoxStatus"
					 @closeFileBox="() => {fileBoxStatus = false}"/>
	</el-tabs>
</template>
<script setup>
import receiveList from "@/api/archive/archiveReception/receive"
import {computed, defineProps, getCurrentInstance, onBeforeUnmount, onMounted, reactive, ref} from 'vue'
import moment from 'moment'
import {ElMessage, ElMessageBox} from "element-plus";
import PDFViewer from "@/views/archiveReception/common/PDFViewer.vue";
import {Expand} from "@element-plus/icons-vue";
import PdfViewPlus from '@/components/pdfViewPlus';

const {proxy} = getCurrentInstance()
const activeName = ref('first')
const props = defineProps({
	receiveId: {
		type: String
	}
})
const pdfRef = ref()
const srcList = ref([]);
const fileBoxDataList = ref([]);
const tableData = ref([]);
const loading = ref(true);
const pdfLoading = ref(false);
const fileBoxStatus = ref(false);
// 操作流程
const logRef3 = ref(null)
// 接收库基本信息List
const basicInfoList = ref([]);
// 接受库其他信息List
const descriptionsLabel = ref([]);
const chooseCollapse = ref([]);
// 左侧面板初始宽度
const leftWidth = ref(300);

// 拖动相关状态
const isResizing = ref(false);
const startPosition = {x: 0};

function startResize(e) {
	isResizing.value = true;
	startPosition.x = e.clientX;

	// 添加全局事件监听
	window.addEventListener('mousemove', handleMouseMove);
	window.addEventListener('mouseup', stopResize);

	// 防止文本选中
	document.body.style.userSelect = 'none';
}

function handleMouseMove(e) {
	if (!isResizing.value) return;

	// 计算新的宽度
	const dx = e.clientX - startPosition.x;
	const newWidth = leftWidth.value + dx;

	// 设置最小和最大宽度限制
	if (newWidth > 100 && newWidth < window.innerWidth - 100) {
		leftWidth.value = newWidth;
	}

	// 更新起始位置
	startPosition.x = e.clientX;
}

function stopResize() {
	isResizing.value = false;

	// 移除全局事件监听
	window.removeEventListener('mousemove', handleMouseMove);
	window.removeEventListener('mouseup', stopResize);

	// 恢复文本选中
	document.body.style.userSelect = '';
}

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
	window.removeEventListener('mousemove', handleMouseMove);
	window.removeEventListener('mouseup', stopResize);
});

// const customNodeClass = (data, node) => {
// 	if (node.childNodes.length <= 0) {
// 		return 'nodeCustomClass'
// 	} else {
// 		return null
// 	}
// }
// const state = reactive({
// 	pageNum: 1, //当前页面
// 	scale: 1, // 缩放比例
// 	numPages: 0, // 总页数
// 	loading: false,//加载效果
// 	rotation: 0 // 旋转角度
// });

// const scale = computed(() => `transform:scale(${state.scale});transition: all 0.3s;transform-origin: top left;
//   						transition: transform 0.5s ease-out;`);

onMounted(() => {
	getOriginalInfoByMasterId();
	fileFist();
});

function disposeTableData(list, type) {
	let newList = type === 1 ? [] : new Set();

	list.forEach(data => {
		if (type === 1) {
			let newObj = [];
			data.forEach(item => {
				newObj[item.name] = {
					value: item.value,
					config: item.config
				};
			});
			newList.push(newObj);
		} else if (type === 2) {
			data.forEach(item => {
				newList.add(item.name);
			});
		}
	});
	return newList;
}

// function showInfoDetail(value) {
// 	ElMessageBox.alert(value, "信息详情", {
// 		confirmButtonText: 'OK',
// 		callback: (action) => {
// 			ElMessage({
// 				type: 'info',
// 				message: `action: ${action}`,
// 			})
// 		},
// 	})
// }
//
// function lastPage() {
// 	if (state.pageNum > 1) {
// 		state.pageNum -= 1;
// 	}
// }
//
// function nextPage() {
// 	if (state.pageNum < state.numPages) {
// 		state.pageNum += 1;
// 	}
// }
//
// function rotateLeft() {
// 	if (state.rotation > 0) {
// 		state.rotation -= 90;
// 	} else {
// 		state.rotation = 270;
// 	}
// }
//
// function rotateRight() {
// 	if (state.rotation < 270) {
// 		state.rotation += 90;
// 	} else {
// 		state.rotation = 0;
// 	}
// }
//
// function pageZoomOut() {
// 	if (state.scale < 3) {
// 		state.scale = parseFloat((state.scale + 0.1).toFixed(1))
// 		scrollbar.value.update();
// 		console.log(state.scale);
// 	}
// }
//
// function pageZoomIn() {
// 	if (state.scale > 1) {
// 		state.scale = parseFloat((state.scale - 0.1).toFixed(1))
// 		scrollbar.value.update();
// 		console.log(state.scale);
// 	}
// }

//打开档案图片
function openImg(imgUrl) {
	srcList.value = [];
	srcList.value.push(imgUrl);
}

//检测字符串内包含的是什么类型的文件, 图片还是pdf
function checkFileType(fileStrUrl) {
	const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
	const pdfExtensions = ['pdf'];

	if (fileStrUrl) {
		let returnData = '';
		imageExtensions.forEach(extension => {
			if (fileStrUrl.toLowerCase().includes(extension)) {
				returnData = 'image';
			}
		});
		pdfExtensions.forEach(extension => {
			if (fileStrUrl.toLowerCase().includes(extension)) {
				returnData = 'pdf';
			}
		});
		return returnData;
	} else {
		return 'other';
	}
}

//转换文件
function openFileBox(jsonString) {
	try {
		const data = JSON.parse(jsonString);
		const dataArray = Array.isArray(data) ? data : [data];

		fileBoxDataList.value = dataArray.map(item => {
			for (const key in item) {
				if (key.toLowerCase().includes('name')) {
					item["fileName"] = item[key];
				}
				if (key.toLowerCase().includes('url')) {
					item["fileUrl"] = item[key];
				}
			}

			if (!item.fileName && item.fileUrl) {
				item["fileName"] = item.fileUrl.substring(item.fileUrl.lastIndexOf('/') + 1);
			}

			return item;
		});
		fileBoxStatus.value = true;
	} catch (e) {
		console.error('Invalid JSON format:', e);
		return [];
	}
}

function disposeImg(imgObject) {
	let imgList = [];

	if (typeof imgObject === 'string') {
		if (imgObject.includes("[")) {
			let parseArray = JSON.parse(imgObject);
			parseArray.forEach(item => {
				for (const key in item) {
					if (key.toLowerCase().includes('url')) {
						imgList.push(item[key]);
					}
				}
			});
		} else if (imgObject.includes("{")) {
			let parseObj = JSON.parse(imgObject);
			for (const key in parseObj) {
				if (key.toLowerCase().includes('url')) {
					imgList.push(parseObj[key]);
				}
			}
		} else {
			imgList.push(imgObject);
		}
	} else {
		return null;
	}
	return imgList;
}

// function successMethod() {
// 	pdfLoading.value = false;
// }

function onLoaded(pdfApp) {
	pdfLoading.value = false;
	console.log('加载完成 app:', pdfApp)
}

function sortTreeNodes(data) {
	// 首先对当前层级的节点进行排序
	data.sort((a, b) => a.config.sort - b.config.sort);
	// 过滤不显示的内容
	data = data.filter(item => item.config.isView !== '1');

	// 遍历当前层级的每个节点
	for (let node of data) {
		// 如果当前节点有子节点
		if (node.children) {
			if (Array.isArray(node.children) && !Array.isArray(node.children[0])) {
				// 如果子节点是一维数组
				node.children = sortTreeNodes(node.children);
			} else if (Array.isArray(node.children) && Array.isArray(node.children[0])) {
				// 如果子节点是二维数组
				node.children = node.children.map(childArray => sortTreeNodes(childArray));
			}
		}
	}

	return data;
}

// 获取原数据信息
function getOriginalInfoByMasterId() {
	loading.value = true;
	receiveList.getDataById({
		id: props.receiveId
	}).then(res => {
		if (res.code === 200) {
			let records = [];
			let dataList = res.data;
			dataList = sortTreeNodes(dataList);
			basicInfoList.value = dataList.filter(item => {
				return !item.children
			});
			descriptionsLabel.value = dataList.filter(item => {
				return item.children
			});
			for (let i = 0; i <= descriptionsLabel.value.length; i++) {
				// 将当前数值添加到结果数组中
				chooseCollapse.value.push(i);
			}
			basicInfoList.value.forEach(label => {
				if (label.name === '操作记录' || label.name === '供应商审核记录' || label.name.includes('审核记录')) {
					let recordData = label.value ? JSON.parse(label.value) : [];
					recordData.forEach(record => {
						records.push({
							name: record.reviewer,
							updateDate: record.review_time,
							remark: record.remark,
							reviewIdea: record.review_idea
						});
					});
				}
			});
			descriptionsLabel.value.forEach(label => {
				let tableDataList = [];
				let data = {};
				label.children.forEach(item => {
					data[item.name] = item.value
				});
				tableDataList.push(data);
				label.tableDataList = tableDataList;
			});
			// logRef3.value.timeFns(records);
			//循环去除数据
			basicInfoList.value = basicInfoList.value.filter(label => {
				return label.name !== "操作记录" && label.name !== "供应商审核记录" && !label.name.includes('审核记录') && !isNaN(Number(label.config.type));
			});
			loading.value = false;
		}
	}).catch(error => {
		console.log(error);
		proxy.msgError('查询失败');
	});
}

//列表切换
function handleClick(tabInfo) {
	if (tabInfo.props.name === 'second') {
		if (tableData.value.length > 0) {
			if (!pdfRef.value) {
				pdfLoading.value = true;
				pdfRef.value = tableData.value[0].fileUrl;

				// const loadingTask = createLoadingTask(pdfRef.value);
				// state.loading = true; // 添加一个loading状态
				// loadingTask.promise.then((pdf) => {
				// 	state.numPages = pdf.numPages;
				// 	// 加载完成后将loading状态设置为false
				// 	state.loading = false;
				// });
			}
		} else {
			pdfLoading.value = false;
		}
	}
}

function fileFist() {
	receiveList.fileFist({
		originalId: props.receiveId,
		fileType: "pdf",
		current: 1,
		size: -1,
	}).then(res => {
		if (res.code === 200) {
			tableData.value = res.data.records;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

function handleViewFile(url) {
	if (pdfRef.value !== url) {
		pdfLoading.value = true;
		pdfRef.value = url;
		// state.pageNum = 1;
		// state.scale = 1;
        //
		// const loadingTask = createLoadingTask(pdfRef.value);
		// state.loading = true; // 添加一个loading状态
		// loadingTask.promise.then((pdf) => {
		// 	state.numPages = pdf.numPages;
		// 	// 加载完成后将loading状态设置为false
		// 	state.loading = false;
		// });
	}
}
</script>
<style scoped>
.fileUrl {
	cursor: pointer;
	border-top: 1px solid #E4E7ED;
	border-left: 1px solid #E4E7ED;
	border-bottom: 1px solid #E4E7ED;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.resizable-container {
	position: relative;
	display: flex;
	height: 100%;
}

.resize-handle {
	width: 6px;
	height: 100%;
	background-color: #e0e0e0;
	cursor: col-resize;
	position: relative;
	z-index: 10;
}

.resize-handle:hover,
.resize-handle.resizing {
	background-color: #1890ff;
}

.fileUrl {
	cursor: pointer;
	border-top: 1px solid #E4E7ED;
	border-left: 1px solid #E4E7ED;
	border-bottom: 1px solid #E4E7ED;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

p {
	width: 250px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

:deep(.nodeCustomClass) {
	margin: 2px 0;
	height: 42px;
}

:deep(.labelCustomClass) {
	width: 12.5%;
	max-width: 12.5%;
}

:deep(.el-tree-node__content:hover) {
	background: none;
}

:deep(.is-current) {
	background: none;
}

:deep(.el-scrollbar__view) {
	width: 100%;
	height: 100%;
}

:deep(.el-table__cell) {
	position: static !important;
}

</style>
