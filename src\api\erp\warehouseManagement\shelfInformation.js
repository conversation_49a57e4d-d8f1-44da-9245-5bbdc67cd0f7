import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/storage/erpGoodsShelves/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/storage/erpGoodsShelves/delete',ids)
  },
  save: function (params) {
    return http.post('/erp/storage/erpGoodsShelves/save',params)
  },
  detail: function (id) {
    return http.get('/erp/storage/erpGoodsShelves/queryById',id)
  },
  logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
}