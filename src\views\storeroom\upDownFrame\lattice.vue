<template>
	<div class="lattice-container">
		<!-- 柜面选择区域 -->
		<el-main class="main-content">
			<el-container>
				<div class="lattice-wrapper">
					<!-- AB面选择 -->
					<div v-if="showCounterTypeSelector" class="counter-type-selector">
						<div class="selector-label">柜面选择:</div>
						<el-select v-model="counterType" class="form_225" @change="handleCounterTypeChange">
							<el-option label="A面" value="1"/>
							<el-option label="B面" value="2"/>
						</el-select>
						<el-button type="primary" @click="handleCounterTypeConfirm">确定</el-button>
					</div>

					<!-- 档案柜展示区域 -->
					<div class="container-display">
						<!-- 节标题行 -->
						<div class="section-headers">
							<div
								v-for="(item, index) in containerList[0]"
								:key="index"
								class="section-header"
							>
								{{ getSectionLabel(index) }}
							</div>
						</div>

						<!-- 层和格子 -->
						<div
							v-for="(floorItem, floorIndex) in containerList"
							:key="floorIndex"
							class="floor-row"
						>
							<!-- 层标签 -->
							<div class="floor-label">
								{{ getFloorLabel(floorIndex) }}
							</div>

							<!-- 格子 -->
							<el-popover
								v-for="(pitchItem, pitchIndex) in floorItem"
								:key="pitchIndex"
								:width="120"
								placement="top-start"
								trigger="hover"
							>
								<template #reference>
									<div
										:class="getLatticeCellClasses(pitchItem)"
										class="lattice-cell"
										@click="handleLatticeClick(pitchItem)"
									></div>
								</template>
								<div class="cell-info">
									<div>额定容量：{{ pitchItem.storageFileMax || 0}} 盒</div>
									<div>剩余容量：{{ getRemainingCapacity(pitchItem) }} 盒</div>
									<div v-if="pitchItem.storageGroup">
										所属全宗: {{ pitchItem.storageGroup.recordGroupName }}
									</div>
									<div v-if="pitchItem.storageCategory">
										所属门类: {{ pitchItem.storageCategory.name }}
									</div>
								</div>
							</el-popover>
						</div>
					</div>
				</div>
			</el-container>
		</el-main>

		<!-- 操作按钮区域 -->
		<el-main class="action-bar">
			<el-container>
				<el-main class="action-content">
					<el-button plain @click="handleCancel">取消</el-button>
					<el-button type="primary" @click="handleConfirm">确定</el-button>
				</el-main>
			</el-container>
		</el-main>

		<!-- 详情弹窗 -->
		<el-dialog
			v-model="open"
			append-to-body
			title="详情"
			width="1000px"
		>
			<el-table
				:data="tableData"
				:load="load"
				:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
				height="650"
				lazy
				row-key="id"
				style="width: 100%"
			>
				<el-table-column label="架位名称" prop="recordStorageName" />
			</el-table>
		</el-dialog>
	</div>
</template>

<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';
import category from '@/api/archive/categoryManagement/category';
import frameList from '@/api/archive/storeroom/upDownFrame';

export default {
	name: 'Shelves',
	props: {
		latticeView: {
			type: [String, Number],
			required: true
		},
		receiveId: {
			type: Object,
			required: true
		}
	},
	emits: ['childMove'],
	data() {
		return {
			pitch: "",
			floor: '',
			counterType: '1',
			openJoint: false,
			form: {},
			fourConfig: {},
			queryParams: {
				current: 1,
				size: 10
			},
			total: 0,
			open: false,
			tableData: [],
			selected: false,
			selectedIds: [],
			load: false,
			containerList: [],
			containerInfo: {},
			containerType: '1',
			counterTypeVal: '',
		};
	},
	computed: {
		showCounterTypeSelector() {
			return this.counterTypeVal === '2';
		}
	},
	mounted() {
		this.initializeData();
	},
	methods: {
		// 初始化数据
		async initializeData() {
			try {
				await Promise.all([
					this.containerById(this.latticeView),
					this.getById()
				]);
			} catch (error) {
				console.error('初始化数据失败:', error);
				this.$Response.errorNotice(error, "初始化失败");
			}
		},

		// 获取节标签
		getSectionLabel(index) {
			if (this.containerInfo.containerPitchStart === '1') {
				return `第${index + 1}节`;
			} else if (this.containerInfo.containerPitchStart === '2') {
				return `第${this.containerInfo.containerFloors - index}节`;
			}
			return '';
		},

		// 获取层标签
		getFloorLabel(floorIndex) {
			if (this.containerInfo.containerFloorsStart === '1') {
				return `第${floorIndex + 1}层`;
			} else if (this.containerInfo.containerFloorsStart === '2') {
				return `第${this.containerInfo.containerFloors - floorIndex}层`;
			}
			return '';
		},

		// 获取格子样式类
		getLatticeCellClasses(pitchItem) {
			return {
				'selected': pitchItem.selected,
				'not-full': this.isNotFull(pitchItem),
				'full': this.isFull(pitchItem)
			};
		},

		// 检查是否未满
		isNotFull(pitchItem) {
			return pitchItem.storageFileCount !== 0 &&
				   pitchItem.storageFileMax > pitchItem.storageFileCount;
		},

		// 检查是否已满
		isFull(pitchItem) {
			return pitchItem.storageFileCount !== 0 &&
				   pitchItem.storageFileMax === pitchItem.storageFileCount;
		},

		// 获取剩余容量
		getRemainingCapacity(pitchItem) {
			return( pitchItem.storageFileMax - pitchItem.storageFileCount ) || 0;
		},

		// 柜面选择变化
		handleCounterTypeChange(value) {
			this.counterType = value;
		},

		// 展示档案柜
		async containerById(id) {
			try {
				const res = await storeroomData.getStoragesByContainerId({
					containerId: id,
					counterType: this.counterType
				});

				if (res.code === 200) {
					this.containerList = res.data;
				} else {
					throw new Error(res.msg || '查询失败');
				}
			} catch (error) {
				console.error('查询档案柜失败:', error);
				this.$Response.errorNotice(error, "查询失败");
			}
		},

		// 确定查询面
		handleCounterTypeConfirm() {
			this.containerById(this.latticeView);
		},

		// 取消弹窗
		handleCancel() {
			this.$emit('childMove');
		},

		// 确定上架
		async handleConfirm() {
			if (this.selectedIds.length === 0) {
				this.$message.warning('请选择要上架的位置');
				return;
			}

			try {
				const idStr = this.selectedIds.join(",");
				const res = await frameList.boxControl({
					storage: { id: idStr },
					controlType: '1',
					id: this.receiveId.id
				});

				if (res.code === 200) {
					this.handleCancel();
					this.$message.success("上架成功");
				} else {
					throw new Error(res.msg || '上架失败');
				}
			} catch (error) {
				console.error('上架失败:', error);
				this.$Response.errorNotice(error, "上架失败");
			}
		},

		// 档案架设置
		storageAdd() {
			this.getManagement();
			this.dataInfo();
			this.openJoint = true;
		},

		// 查询全宗
		async getManagement() {
			try {
				const res = await completeManagement.getList();
				if (res.code === 200) {
					this.fourConfig.group = res.data.records;
				} else {
					throw new Error(res.msg || '查询失败');
				}
			} catch (error) {
				console.error('查询全宗失败:', error);
				this.$Response.errorNotice(error, "查询失败");
			}
		},

		// 查询门类
		async dataInfo() {
			try {
				const res = await category.list();
				if (res.code === 200) {
					this.fourConfig.category = res.data;
				} else {
					throw new Error(res.msg || '查询失败');
				}
			} catch (error) {
				console.error('查询门类失败:', error);
				this.$Response.errorNotice(error, "查询失败");
			}
		},

		// 处理格子点击
		handleLatticeClick(pitch) {
			// 检查是否已满
			if (this.isLatticeFull(pitch)) {
				this.$Response.errorNotice(null, "此格已满!请选择其他位置存放!");
				return;
			}

			// 取消之前选中的格子
			this.clearPreviousSelection();

			// 切换当前格子选中状态
			pitch.selected = !pitch.selected;

			// 更新选中的格子数组
			this.updateSelectedIds(pitch);
		},

		// 检查格子是否已满
		isLatticeFull(pitch) {
			return pitch.storageFileCount !== 0 &&
				   pitch.storageFileMax === pitch.storageFileCount;
		},

		// 清除之前的选中状态
		clearPreviousSelection() {
			this.containerList.forEach(floorItem => {
				floorItem.forEach(pitchItem => {
					pitchItem.selected = false;
				});
			});
		},

		// 更新选中的ID数组
		updateSelectedIds(pitch) {
			this.selectedIds = [];
			if (pitch.selected) {
				this.selectedIds.push(pitch.id);
			}
		},

		// 获取档案柜信息
		async getById() {
			try {
				const res = await storeroomData.containerById({
					id: this.latticeView
				});

				if (res.code === 200) {
					this.counterTypeVal = res.data.containerCounterType;
					this.containerInfo = res.data;
				} else {
					throw new Error(res.msg || '查询失败');
				}
			} catch (error) {
				console.error('查询档案柜信息失败:', error);
				this.$Response.errorNotice(error, "查询失败");
			}
		}
	}
};
</script>

<style scoped>
.lattice-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.main-content {
	padding-top: 0;
	flex: 1;
}

.lattice-wrapper {
	height: 550px;
}

.counter-type-selector {
	padding: 10px;
	display: flex;
	align-items: center;
	gap: 15px;
}

.selector-label {
	text-align: center;
	line-height: 35px;
	flex-shrink: 0;
}

.container-display {
	margin-left: 3%;
	width: fit-content;
}

.section-headers {
	display: flex;
	margin-bottom: 15px;
	margin-left: 80px;
}

.section-header {
	width: 80px;
	text-align: center;
	font-size: 14px;
	font-weight: 600;
	margin-left: 20px;
}

.floor-row {
	display: flex;
	margin-bottom: 10px;
	align-items: center;
}

.floor-label {
	width: 80px;
	margin-top: 0.25rem;
	font-size: 14px;
	font-weight: 600;
	text-align: center;
}

.lattice-cell {
	width: 80px;
	height: 110px;
	border: 1px solid #368cca;
	cursor: pointer;
	margin-left: 20px;
	border-radius: 3px;
	transition: all 0.3s ease;
}

.lattice-cell:hover {
	transform: scale(1.02);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lattice-cell.selected {
	background-color: #ccc !important;
	border-color: #666;
}

.lattice-cell.not-full {
	background-color: #81ee66;
}

.lattice-cell.full {
	background-color: #db1b1b;
}

.cell-info {
	font-size: 12px;
	line-height: 1.5;
}

.action-bar {
	padding: 0;
}

.action-content {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	padding: 10px 0;
}
</style>
