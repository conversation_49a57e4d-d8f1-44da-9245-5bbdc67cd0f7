<template>
	<div>
		<div v-for="(deepChildInfo, index) in childrenInfo.detailChildren">
			<div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 18px">
				<h4 style="font-weight: 600;">{{ '第' + (index + 1) + '个' }}</h4>
				<el-button plain style="margin-right: 30px" type="warning"
						   @click="removeOtherOneInfo(childrenInfo.detailChildren, index)">
					删除
				</el-button>
			</div>
			<div style="width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr;
				justify-items: center;">
				<div v-for="(item, index) in deepChildInfo.filter(item => !item.detailChildren)" :key="index">
					<el-form-item v-if="item.detailType === '1'"
								  :label="item.detailRemark"
								  :prop="item.detailRemark"
								  style="width: 388px;">
						<el-input v-model="item['detailValue']" :placeholder="item.detailRemark" clearable/>
					</el-form-item>
					<el-form-item v-if="item.detailType === '2'"
								  :label="item.detailRemark"
								  :prop="item.detailRemark"
								  style="width: 388px;">
						<el-upload
							class="avatar-uploader"
							:action="uploadUrl"
							:accept="'image/gif, image/jpeg, image/png'"
							:show-file-list="false"
							:on-success="(res, file, fileList) => item.detailValue = res.data.url"
							:headers='headers'>
							<img v-if="item.detailValue" :src="item.detailValue" alt="" class="avatar"
								 style="height: 30px;width: 30px;border: 1px solid ghostwhite">
							<el-icon v-else class="avatar-uploader-icon">
								<Plus/>
							</el-icon>
						</el-upload>
					</el-form-item>
					<el-form-item v-if="item.detailType === '4'"
								  :label="item.detailRemark"
								  :prop="item.detailRemark"
								  style="width: 388px;">
						<el-date-picker type="date"
										v-model="item['detailValue']"
										:placeholder="item.detailRemark"
										format="YYYY-MM-DD"
										style="width: 100%;"
										value-format="YYYY-MM-DD"/>
					</el-form-item>
					<el-form-item v-if="item.detailType === '5'"
								  :label="item.detailRemark"
								  :prop="item.detailRemark"
								  style="width: 388px;">
						<el-radio-group v-model="item['detailValue']">
							<el-radio label="是"/>
							<el-radio label="否"/>
						</el-radio-group>
					</el-form-item>
				</div>
				<div v-for="(item, index) in deepChildInfo.filter(item => item.detailChildren)" :key="index">
					<el-divider style="margin: 14px 0 16px 0;width: 100%"/>
					<div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 18px">
						<h4 style="font-weight: 600;">{{ item.detailRemark }}</h4>
						<div>
							<el-button plain type="primary" @click="addOtherOneInfo(item)">
								添加
							</el-button>
						</div>
					</div>
					<el-divider style="margin: 14px 0 16px 0;width: 100%"/>
					<RecursiveInfoForm :childrenInfo="item"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import {defineEmits, onMounted} from "vue";
import tool from "@/utils/tool";
import {Plus} from "@element-plus/icons-vue";

const props = defineProps({
	childrenInfo: {
		type: Object,
		default: () => {
		},
	},
	dataList: {
		type: Array,
		default: () => [],
	},
});
// 附件上传
const uploadUrl = process.env.VUE_APP_API_UPLOAD
// 接口请求头
const headers = {
	Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
	ContentType: 'multipart/form-data',
	clientType: "PC"
}
const configProps = {
	value: "name",
	label: "name",
	children: "children",
	leaf: "leaf",
	lazy: false,
}

const emit = defineEmits(["completeCallback"]);

onMounted(() => {
});

//添加数据结构
function addOtherOneInfo(data) {
	let newArray = JSON.parse(JSON.stringify(data.detailChildren[0]));
	newArray.forEach(item => {
		item.id = '';
		item.detailValue = '';
	});
	data.detailChildren.push(newArray);
	console.log(data.detailChildren);
	console.log(data);
	proxy.msgSuccess('添加成功');
}

//处理数据
function processData(data) {
	if (data.length > 0) {
		return data.split("-");
	} else {
		return [];
	}
}

//删除数据结构
function removeOtherOneInfo(data, index) {
	if (data.length === 1) {
		proxy.msgError('最后一个数据无法删除');
	} else {
		data.splice(index, 1);
		proxy.msgSuccess('删除成功');
	}
}
</script>

<style scoped>

</style>
