/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:11
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2024-12-23 14:49:45
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {createApp} from 'vue'
// import './rem'
// import 'amfe-flexible'
import ElementPlus, {ElMessage} from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/display.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import scui from './scui'
import i18n from './locales'
import router from './router'
import store from './store'
import App from './App.vue'
import dict from '@/utils/dict'
import VueKonva from 'vue-konva'
import 'default-passive-events'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
// import 'lib-flexible'
// 分页组件
import Pagination from '@/components/Pagination'
import selectDictLabel from '@/utils/dictLabel'
import {debounce, deepClone, download, resetForm} from '@/utils'
import areas from "@/assets/areas/areas.json";
import areas_third from "@/assets/areas/areas_third.json";
import vue3TreeOrg from 'vue3-tree-org';
import "vue3-tree-org/lib/vue3-tree-org.css";

const app = createApp(App);
const _ResizeObserver = window.ResizeObserver;
window.ResizeObserver = class ResizeObserver extends _ResizeObserver {
	constructor(callback) {
		callback = debounce(callback, 16);
		super(callback);
	}
}
app.directive('click', {
	mounted(el, binding, vnode) {
		let execFunc
		if (binding.value instanceof Array) {
			const [func, time = 1000] = binding.value
			execFunc = debounce(func, time, true)   // true为第一次点击立即执行 避免给用户卡顿的感觉
		} else {
			execFunc = debounce(binding.value, 1000, true)
		}
		el.addEventListener('click', execFunc)
	}
})
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
	app.component(key, component)
}
app.config.globalProperties.getDictList = dict
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.deepClone = deepClone
app.config.globalProperties.download = download
app.config.globalProperties.getSysAreas = areas
app.config.globalProperties.getSysAreasThird = areas_third
app.component('Pagination', Pagination)
app.use(vue3TreeOrg)
app.use(store);
app.use(router);
// 使用element-plus 并且设置全局的大小
app.config.globalProperties.msgSuccess = function (msg) {
	ElMessage({
		message: msg,
		grouping: true,
		type: 'success',
	})
}
app.config.globalProperties.msgError = function (msg) {
	ElMessage({
		message: msg,
		grouping: true,
		type: 'error',
	})
}
app.config.globalProperties.msgInfo = function (msg) {
	ElMessage({
		message: msg,
		grouping: true
	})
}
app.config.globalProperties.msgWarning = function (msg) {
	ElMessage({
		message: msg,
		grouping: true,
		type: 'warning',
	})
}
app.use(ElementPlus, {
	size: 'small',
	locale: zhCn,
})
app.use(i18n);
app.use(scui);
app.use(VueKonva);

// 添加全局错误处理
app.config.errorHandler = (err, vm, info) => {
	console.error('Vue Error:', err)
	console.error('Component:', vm)
	console.error('Info:', info)
}

// 添加全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
	console.warn('Vue Warning:', msg)
	console.warn('Component:', vm)
	console.warn('Trace:', trace)
}

//挂载app
app.mount('#app');
