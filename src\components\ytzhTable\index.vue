<template>
	<div ref="ytzhTableMain" v-loading="loading" :style="{ height: _height }" class="ytzhTable">
		<div :style="{ height: _table_height }" class="ytzhTable-table">
			<el-table
				:key="toggleIndex"
				:border="config.border"
				ref="ytzhTable"
				:data="tableData"
				:row-key="rowKey"
				:height="height === 'auto' ? null : '100%'"
				:size="config.size"
				:load="treeLoadHandle"
				:summary-method="remoteSummary ? remoteSummaryMethod : summaryMethod"
				:tree-props="{ children: 'children', hasChildren: 'children' }"
				:stripe="config.stripe"
				@sort-change="sortChange"
				@filter-change="filterChange"
				lazy
				show-overflow-tooltip
				v-bind="$attrs"
			>
				<slot></slot>
				<template v-for="(item, index) in userColumn" :key="index">
					<el-table-column
						v-if="!item.hide"
						:filter-method="remoteFilter || !item.filters ? null : filterHandler"
						:prop="item.prop"
						:fixed="item.fixed"
						:label="item.label"
						:width="item.width"
						:column-key="item.prop"
						:filters="item.filters"
						show-overflow-tooltip
					>
						<template #default="scope">
							<slot :name="item.prop" v-bind="scope">
								{{ scope.row[item.prop] }}
							</slot>
						</template>
					</el-table-column>
				</template>
				<el-table-column min-width="1"></el-table-column>
				<template #empty>
					<el-empty :description="emptyText" :image-size="100"></el-empty>
				</template>
			</el-table>
		</div>
		<div v-if="!hidePagination || !hideDo" class="ytzhTable-page">
			<div class="ytzhTable-pagination">
				<el-pagination
					v-if="!hidePagination"
					v-model:currentPage="tablePage.currentPage"
					:layout="paginationLayout"
					:page-size="tablePage.pageSize"
					:page-sizes="pageSizes"
					:small="true"
					:total="tablePage.total"
					background
					@current-change="paginationChange"
					@update:page-size="pageSizeChange"
				></el-pagination>
			</div>
			<div v-if="!hideDo" class="ytzhTable-do">
				<el-button
					v-if="!hideRefresh"
					circle
					icon="el-icon-refresh"
					style="margin-left: 15px"
					@click="refresh"
				></el-button>
				<el-popover
					v-if="column"
					:hide-after="0"
					:width="500"
					placement="top"
					title="列设置"
					trigger="click"
					@show="customColumnShow = true"
					@after-leave="customColumnShow = false"
				>
					<template #reference>
						<el-button circle icon="el-icon-set-up" style="margin-left: 15px"></el-button>
					</template>
					<columnSetting
						v-if="customColumnShow"
						ref="columnSetting"
						:column="userColumn"
						@back="columnSettingBack"
						@save="columnSettingSave"
						@userChange="columnSettingChange"
					></columnSetting>
				</el-popover>
				<el-popover
					v-if="!hideSetting"
					:hide-after="0"
					:width="400"
					placement="top"
					title="表格设置"
					trigger="click"
				>
					<template #reference>
						<el-button
							circle
							icon="el-icon-setting"
							style="margin-left: 15px"
						></el-button>
					</template>
					<el-form label-position="left" label-width="80px">
						<el-form-item label="表格尺寸">
							<el-radio-group
								v-model="config.size"
								size="small"
								@change="configSizeChange"
							>
								<el-radio-button label="large">大</el-radio-button>
								<el-radio-button label="default">正常</el-radio-button>
								<el-radio-button label="small">小</el-radio-button>
							</el-radio-group>
						</el-form-item>
						<el-form-item label="样式">
							<el-checkbox v-model="config.border" label="纵向边框"></el-checkbox>
							<el-checkbox v-model="config.stripe" label="斑马纹"></el-checkbox>
						</el-form-item>
					</el-form>
				</el-popover>
			</div>
		</div>
	</div>
</template>

<script>
import config from "@/config/table";
import columnSetting from "./columnSetting";

export default {
	name: "ytzhTable",
	components: {
		columnSetting,
	},
	props: {
		tableName: {
			type: String,
			default: ""
		},
		params: {
			type: Object,
			default: () => {
			}
		},
		data: {
			type: Array,
			default: () => []
		},
		height: {
			type: [String, Number],
			default: "100%"
		},
		size: {
			type: String,
			default: "default"
		},
		border: {
			type: Boolean,
			default: false
		},
		stripe: {
			type: Boolean,
			default: false
		},
		pageSize: {
			type: Number,
			default: config.pageSize
		},
		pageSizes: {
			type: Array,
			default: config.pageSizes
		},
		rowKey: {
			type: String,
			default: ""
		},
		summaryMethod: {
			type: Function,
			default: null
		},
		column: {
			type: Array,
			default: () => []
		},
		remoteSort: {
			type: Boolean,
			default: false
		},
		remoteFilter: {
			type: Boolean,
			default: false
		},
		remoteSummary: {
			type: Boolean,
			default: false
		},
		hidePagination: {
			type: Boolean,
			default: false
		},
		hideDo: {
			type: Boolean,
			default: false
		},
		hideRefresh: {
			type: Boolean,
			default: false
		},
		hideSetting: {
			type: Boolean,
			default: false
		},
		paginationLayout: {
			type: String,
			default: config.paginationLayout
		},
		pageChangeHandle: {
			type: Function,
			default: null
		},
		refreshDataListHandle: {
			type: Function,
			default: null
		},
		treeLoadHandle: {
			type: Function,
			default: null
		},
		tablePage: {
			type: Object,
			default: () => {
			}
		},
	},
	watch: {
		//监听从props里拿到值了
		data() {
			this.tableData = this.data;
		},
		column() {
			this.userColumn = this.column;
		},
	},
	computed: {
		_height() {
			return Number(this.height) ? Number(this.height) + "px" : this.height;
		},
		_table_height() {
			return this.hidePagination && this.hideDo ? "100%" : "calc(100% - 50px)";
		},
	},
	data() {
		return {
			isActivate: true,
			emptyText: "暂无数据",
			toggleIndex: 0,
			tableData: [],
			prop: null,
			order: null,
			loading: false,
			tableHeight: "100%",
			tableParams: this.params,
			userColumn: [],
			customColumnShow: false,
			summary: {},
			config: {
				size: this.size,
				border: this.border,
				stripe: this.stripe,
			},
		};
	},
	mounted() {
		//判断是否开启自定义列
		if (this.column) {
			this.getCustomColumn();
		} else {
			this.userColumn = this.column;
		}
		this.tableData = this.data;
	},
	activated() {
		if (!this.isActivate) {
			this.$refs.ytzhTable.doLayout();
		}
	},
	deactivated() {
		this.isActivate = false;
	},
	methods: {
		//获取列
		async getCustomColumn() {
			this.userColumn = await config.columnSettingGet(this.tableName, this.column);
		},
		//分页点击
		paginationChange() {
			this.pageChangeHandle(this.tablePage);
		},
		//条数变化
		pageSizeChange(size) {
			this.tablePage.pageSize = size;
			this.scPageSize = size;
			this.pageChangeHandle(this.tablePage);
		},
		//刷新数据
		refresh() {
			this.refreshDataListHandle();
		},
		//更新数据 合并上一次params
		upData(params, page = 1) {
			this.tablePage.currentPage = page;
			this.$refs.ytzhTable.clearSelection();
			Object.assign(this.tableParams, params || {});
		},
		//重载数据 替换params
		reload(params, page = 1) {
			this.tablePage.currentPage = page;
			this.tableParams = params || {};
			this.$refs.ytzhTable.clearSelection();
			this.$refs.ytzhTable.clearSort();
			this.$refs.ytzhTable.clearFilter();
		},
		//自定义变化事件
		columnSettingChange(userColumn) {
			this.userColumn = userColumn;
			this.toggleIndex += 1;
		},
		//自定义列保存
		async columnSettingSave(userColumn) {
			this.$refs.columnSetting.isSave = true;
			try {
				await config.columnSettingSave(this.tableName, userColumn);
			} catch (error) {
				this.$message.error("保存失败");
				this.$refs.columnSetting.isSave = false;
			}
			this.$message.success("保存成功");
			this.$refs.columnSetting.isSave = false;
		},
		//自定义列重置
		async columnSettingBack() {
			this.$refs.columnSetting.isSave = true;
			try {
				const column = await config.columnSettingReset(this.tableName, this.column);
				this.userColumn = column;
				this.$refs.columnSetting.usercolumn = JSON.parse(
					JSON.stringify(this.userColumn || [])
				);
			} catch (error) {
				this.$message.error("重置失败");
				this.$refs.columnSetting.isSave = false;
			}
			this.$refs.columnSetting.isSave = false;
		},
		//排序事件
		sortChange(obj) {
			if (!this.remoteSort) {
				return false;
			}
			if (obj.column && obj.prop) {
				this.prop = obj.prop;
				this.order = obj.order;
			} else {
				this.prop = null;
				this.order = null;
			}
		},
		//本地过滤
		filterHandler(value, row, column) {
			const property = column.property;
			return row[property] === value;
		},
		//过滤事件
		filterChange(filters) {
			if (!this.remoteFilter) {
				return false;
			}
			Object.keys(filters).forEach((key) => {
				filters[key] = filters[key].join(",");
			});
			this.upData(filters);
		},
		//远程合计行处理
		remoteSummaryMethod(param) {
			const {columns} = param;
			const sums = [];
			columns.forEach((column, index) => {
				if (index === 0) {
					sums[index] = "合计";
					return;
				}
				const values = this.summary[column.property];
				if (values) {
					sums[index] = values;
				} else {
					sums[index] = "";
				}
			});
			return sums;
		},
		configSizeChange() {
			this.$refs.ytzhTable.doLayout();
		},
		//插入行 unshiftRow
		unshiftRow(row) {
			this.tableData.unshift(row);
		},
		//插入行 pushRow
		pushRow(row) {
			this.tableData.push(row);
		},
		//根据key覆盖数据
		updateKey(row, rowKey = this.rowKey) {
			this.tableData
				.filter((item) => item[rowKey] === row[rowKey])
				.forEach((item) => {
					Object.assign(item, row);
				});
		},
		//根据index覆盖数据
		updateIndex(row, index) {
			Object.assign(this.tableData[index], row);
		},
		//根据index删除
		removeIndex(index) {
			this.tableData.splice(index, 1);
		},
		//根据index批量删除
		removeIndexes(indexes = []) {
			indexes.forEach((index) => {
				this.tableData.splice(index, 1);
			});
		},
		//根据key删除
		removeKey(key, rowKey = this.rowKey) {
			this.tableData.splice(
				this.tableData.findIndex((item) => item[rowKey] === key),
				1
			);
		},
		//根据keys批量删除
		removeKeys(keys = [], rowKey = this.rowKey) {
			keys.forEach((key) => {
				this.tableData.splice(
					this.tableData.findIndex((item) => item[rowKey] === key),
					1
				);
			});
		},
		//原生方法转发
		clearSelection() {
			this.$refs.ytzhTable.clearSelection();
		},
		toggleRowSelection(row, selected) {
			this.$refs.ytzhTable.toggleRowSelection(row, selected);
		},
		toggleAllSelection() {
			this.$refs.ytzhTable.toggleAllSelection();
		},
		toggleRowExpansion(row, expanded) {
			this.$refs.ytzhTable.toggleRowExpansion(row, expanded);
		},
		setCurrentRow(row) {
			this.$refs.ytzhTable.setCurrentRow(row);
		},
		clearSort() {
			this.$refs.ytzhTable.clearSort();
		},
		clearFilter(columnKey) {
			this.$refs.ytzhTable.clearFilter(columnKey);
		},
		doLayout() {
			this.$refs.ytzhTable.doLayout();
		},
		sort(prop, order) {
			this.$refs.ytzhTable.sort(prop, order);
		},
	},
};
</script>

<style scoped>
.ytzhTable {
}

.ytzhTable-table {
	height: calc(100% - 50px);
}

.ytzhTable-page {
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
}

.ytzhTable-do {
	white-space: nowrap;
}

.ytzhTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}

.ytzhTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
	height: 12px;
	border-radius: 12px;
}

.ytzhTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
	width: 12px;
	border-radius: 12px;
}
</style>
