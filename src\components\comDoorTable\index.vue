<template>
	<el-container v-loading="loading" class="comDoorTable">
		<el-header style="padding-top: 22px;padding-bottom: 22px">
			<el-input v-model="menuFilterText" clearable placeholder="输入关键字进行过滤"/>
		</el-header>
		<el-main class="noPadding">
			<el-tree ref="treeRef"
					 :data="menuList"
					 :filter-node-method="menuFilterNode"
					 :props="menuProps"
					 class="mainBox"
					 default-expand-all
					 highlight-current
					 @node-click="leftHandleCurrentChange">
				<template #default="{ node, data }">
						<span class="custom-tree-node">
							<span class="label">
								{{ data.name || '暂无' }}
							</span>
						</span>
				</template>
			</el-tree>
		</el-main>
	</el-container>
</template>
<script setup>
import completeManagement from '@/api/archive/systemConfiguration/completeManagement'
import category from '@/api/archive/categoryManagement/category';
import {defineEmits, getCurrentInstance, onBeforeMount, reactive, ref, toRefs, watch} from 'vue'

const {proxy} = getCurrentInstance()
const treeRef = ref()
const menuList = ref();
const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
})
const menuFilterText = ref('')
const menuFilterNode = (value, data) => {
	if (!value) return true
	return data.name && data.name.includes(value)
}

const menuProps = ref({});
const loading = ref(true)
const total = ref(0)
const {queryParams} = toRefs(data)
const emit = defineEmits(['clickChild'])

watch(menuFilterText, (val) => {
	if (treeRef.value && treeRef.value.filter) {
		treeRef.value.filter(val);
	}
})

onBeforeMount(() => {
	getList();
});

/** 查询全宗列表 */
async function getList() {
	let treeData = [];
	await completeManagement.getList(queryParams.value).then(res => {
		if (res.code === 200) {
			treeData = res.data;
		}
	});

	if (treeData && treeData.records && Array.isArray(treeData.records)) {
		for (let i = 0; i < treeData.records.length; i++) {
			await dataInfo(treeData.records[i]);
			treeData.records[i].name = treeData.records[i].recordGroupName;
		}
		menuList.value = treeData.records;
		total.value = treeData.total;
	} else {
		menuList.value = [];
		total.value = 0;
	}
	loading.value = false;
}

async function dataInfo(treeData) {
	if (!treeData || !treeData.id) {
		return treeData;
	}

	await category.groupList({
		current: queryParams.value.current,
		size: queryParams.value.size,
		groupId: treeData.id
	}).then(res => {
		if (res.code === 200) {
			if (res.data && res.data.length > 0) {
				treeData.children = res.data;
			}
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
	return treeData;
}

function leftHandleCurrentChange(val) {
	emit('clickChild', val)
}
</script>

<style scoped>
.comDoorTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}

.comDoorTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
	height: 12px;
	border-radius: 12px;
}

.comDoorTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
	width: 12px;
	border-radius: 12px;
}

.comDoorTable {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}

.mainBox {
	height: 100%;
	width: 100%;
}
</style>
