import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/storage/erpStorehouseArea/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/storage/erpStorehouseArea/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/storage/erpStorehouseArea/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/storage/erpStorehouseArea/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/storage/erpStorehouseArea/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/storage/erpStorehouseArea/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/storage/erpStorehouseArea/import',
      data
    )
  }
}
