import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/assist/diagnosisTreatmentScopeConfig/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/assist/diagnosisTreatmentScopeConfig/delete',ids)
  },
  save: function (params) {
    return http.post('/erp/assist/diagnosisTreatmentScopeConfig/save',params)
  },
  treeData: function () {
    return http.get('/erp/assist/erpTreatmentScopeSet/treeData')
  },
}