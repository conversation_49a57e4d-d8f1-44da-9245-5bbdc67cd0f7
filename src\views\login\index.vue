<template>
	<div class="login_bg">
		<div class="login_container">
			<div class="login_card">
				<div class="login_content">
					<div class="login-left">
						<div class="login-branding">
							<h1 class="system-title">档案管理系统（ADMS）</h1>
							<p class="system-description">一个综合的医药生产和质量管理平台，旨在通过数字化和智能化技术，提高医药生产和质量管理的效率和精确度。</p>
						</div>
						<div class="login-image">
							<img alt="智联" src="../../../public/img/zhilian.png"/>
						</div>
					</div>
					<div class="login-right">
						<div class="login-form-container">
							<el-tabs v-model="activeName" class="login-tabs">
								<el-tab-pane label="账号登录" name="first">
									<password-form :handlerReset="handlerReset" />
								</el-tab-pane>
								<el-tab-pane label="手机号登录" name="second">
									<phone-form :handlerReset="handlerReset" />
								</el-tab-pane>
							</el-tabs>
							<div class="terms-agreement">
								<el-text size="small">
									登录即视为同意
									<el-text
										class="terms-link"
										size="small"
										type="primary"
										@click="openKnow(1)">《用户协议》
									</el-text>
									和
									<el-text
										class="terms-link"
										size="small"
										type="primary"
										@click="openKnow(2)">《隐私政策》
									</el-text>
								</el-text>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer class="login-footer">
			<el-text size="small" type="info">Copyright © 2022-2024 甘肃天囤医药有限公司 版权所有</el-text>
			<el-text size="small" type="info">陇ICP备2022001113-1号 甘公网安备62010302001535号</el-text>
		</footer>
	</div>
</template>

<script>
import passwordForm from "./components/passwordForm";
import phoneForm from "./components/phoneForm";
import resetPassword from "./components/resetPassword.vue";
import {ElMessageBox} from "element-plus";

export default {
	components: {
		passwordForm,
		phoneForm,
		resetPassword,
	},
	data() {
		return {
			config: {
				lang: this.$TOOL.data.get("APP_LANG") || this.$CONFIG.LANG,
				dark: this.$TOOL.data.get("APP_DARK") || false,
			},
			lang: [
				{
					name: "简体中文",
					value: "zh-cn",
				},
				{
					name: "English",
					value: "en",
				},
			],
			WechatLoginCode: "",
			showWechatLogin: false,
			isWechatLoginResult: false,
			formFlag: true,
			activeName: 'first',
			formFlag2: false,
		};
	},
	watch: {
		"config.dark"(val) {
			if (val) {
				document.documentElement.classList.add("dark");
				this.$TOOL.data.set("APP_DARK", val);
			} else {
				document.documentElement.classList.remove("dark");
				this.$TOOL.data.remove("APP_DARK");
			}
		},
		"config.lang"(val) {
			this.$i18n.locale = val;
			this.$TOOL.data.set("APP_LANG", val);
		},
	},
	created: function () {
		this.$TOOL.cookie.remove("TOKEN");
		this.$TOOL.data.remove("USER_INFO");
		this.$TOOL.data.remove("ROLE_LIST");
		this.$TOOL.data.remove("Organization");
		this.$TOOL.data.remove("MENU");
		this.$TOOL.data.remove("PERMISSIONS");
		this.$TOOL.data.remove("orgKey");
		this.$TOOL.data.remove("DASHBOARDGRID");
		this.$TOOL.data.remove("grid");
		this.$store.commit("clearViewTags");
		this.$store.commit("clearKeepLive");
		this.$store.commit("clearIframeList");
		console.log(
			"%c SCUI %c Gitee: https://gitee.com/lolicode/scui",
			"background:#666;color:#fff;border-radius:3px;",
			""
		);
	},
	methods: {
		configDark() {
			this.config.dark = this.config.dark ? false : true;
		},
		configLang(command) {
			this.config.lang = command.value;
		},
		wechatLogin() {
			this.showWechatLogin = true;
			this.WechatLoginCode =
				"SCUI-823677237287236-" + new Date().getTime();
			this.isWechatLoginResult = false;
			setTimeout(() => {
				this.isWechatLoginResult = true;
			}, 3000);
		},
		handlerPhone() {
			console.log(111);
			this.formFlag = false;
			this.formFlag2 = true;
		},
		handlerUser() {
			this.formFlag = true;
			this.formFlag2 = false;
		},
		handlerReset() {
			this.formFlag = false;
			this.formFlag2 = false;
		},
		openKnow(type) {
			let title = "";
			let message = "";
			if (type === 1) {
				title = "用户协议";
				message = "用户协议";
			}
			if (type === 2) {
				title = "隐私政策";
				message = "隐私政策";
			}
			ElMessageBox.alert(message, title, {
				confirmButtonText: "确定",
				type: "info",
				showClose: false,
				callback: action => {
					//暂定
				}
			});
		}
	},
};
</script>

<style scoped>
.login_bg {
	width: 100%;
	height: 100vh;
	background-image: url(../../../public/img/background.png);
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.login_container {
	width: 100%;
	max-width: 1200px;
	padding: 0 20px;
	z-index: 1;
}

.login_card {
	background: transparent;
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.login_content {
	display: flex;
	min-height: 560px;
}

.login-left {
	flex: 1;
	background: #fff;
	padding: 48px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.login-branding {
	height: auto;
}

.system-title {
	font-size: 28px;
	font-weight: 600;
	color: #1664FF;
	margin: 0 0 24px 0;
	line-height: 1.2;
}

.system-description {
	font-size: 16px;
	font-weight: 400;
	color: #666666;
	line-height: 1.6;
	margin: 0;
}

.login-image {
	margin-top: 40px;
	display: flex;
	justify-content: flex-end;
	align-items: flex-end;
}

.login-image img {
	max-width: 70%;
	height: auto;
	object-fit: contain;
}

.login-right {
	width: 440px;
	background-color: #FFFFFF;
	padding: 48px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.login-form-container {
	width: 100%;
}

.login-tabs {
	margin-bottom: 24px;
}

.terms-agreement {
	margin-top: 24px;
	text-align: center;
}

.terms-link {
	cursor: pointer;
	transition: color 0.2s;
}

.terms-link:hover {
	text-decoration: underline;
}

.login-footer {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	text-align: center;
	padding: 16px 0;
	display: flex;
	flex-direction: column;
	gap: 4px;
	z-index: 10;
}

/* Element UI Overrides */
:deep(.el-tabs__item) {
	font-size: 16px;
	padding: 0 16px 16px;
	height: 48px;
	line-height: 48px;
}

:deep(.el-tabs__item.is-active) {
	font-weight: 500;
	color: #252B3A;
}

:deep(.el-tabs__active-bar) {
	height: 3px;
	background: #252B3A;
	border-radius: 3px;
}

:deep(.el-tabs__nav-wrap::after) {
	display: none;
}

/* Responsive Design */
@media (max-width: 992px) {
	.login_content {
		flex-direction: column;
	}
	
	.login-left {
		padding: 32px;
	}
	
	.login-right {
		width: 100%;
		padding: 32px;
	}
	
	.system-title {
		font-size: 24px;
	}
	
	.login-image {
		display: none;
	}
}

@media (max-width: 576px) {
	.login_container {
		padding: 0 16px;
	}
	
	.login-left, .login-right {
		padding: 24px;
	}
	
	.system-title {
		font-size: 22px;
	}
	
	.system-description {
		font-size: 14px;
	}
}

:deep(.el-footer) {
	background: none;
	border: none;
}

:deep(.el-card__body) {
	height: 100%;
	width: 100%;
	padding: 0;
}
</style>
