import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/assist/massRangeConfig/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/assist/massRangeConfig/delete',ids)
  },
  save: function (data) {
    return http.post('/erp/assist/massRangeConfig/save',data)
  },
  value: function (params) {
    return http.get('/sys/dictValue/list',params)
  },
}
