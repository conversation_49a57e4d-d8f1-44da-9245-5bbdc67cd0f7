<template>
	<el-container>
		<el-header
			height="auto"
			style="
				background: none;
				border-bottom: none;
				padding: 10px 10px 5px 10px;
			"
		>
			<el-card style="height: 100%; width: 100%">
				<el-input
					v-model="queryParams.content"
					class="w-50 m-2"
					placeholder="请输入消息内容关键字快速查询"
					style="width: 521px"
					@keydown.enter="handleSearch"
				/>
				<el-button
					icon="Search"
					style="margin-left: 20px"
					type="primary"
					@click="handleSearch"
				>
					查询
				</el-button>
				<el-button
					icon="RefreshRight"
					plain
					@click="
						() => {
							queryParams.content = '';
							handleSearch();
						}
					"
				>
					重置
				</el-button>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px">
			<el-card
				:body-style="{
					height: '100%',
					width: '100%',
					padding: '20px 20px 0 20px',
				}"
				style="height: 100%; width: 100%"
			>
				<el-container>
					<el-main style="padding: 0">
						<el-table
							v-loading="loading"
							:data="messageList"
							border
							style="height: 100%; width: 100%"
						>
							<el-table-column
								align="center"
								label="序号"
								prop="sort"
								width="80"
							>
								<template #default="scope">
									{{ scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column
								align="center"
								label="消息类型"
								prop="type"
							>
								<template #default="scope">
									{{ formDict(msgList, scope.row.type) }}
								</template>
							</el-table-column>
							<el-table-column
								:show-overflow-tooltip="true"
								align="center"
								label="消息内容"
								prop="content"
							/>
							<el-table-column
								align="center"
								label="发送时间"
								prop="sendTime"
							/>
							<el-table-column
								align="center"
								label="状态"
								prop="status"
							>
								<template #default="scope">
									{{
										scope.row.status === "1"
											? "已读"
											: "未读"
									}}
								</template>
							</el-table-column>
							<el-table-column
								align="center"
								label="读取人"
								prop="receiver"
							/>
							<el-table-column
								align="center"
								label="读取时间"
								prop="receiveTime"
							>
								<template #default="scope">
									{{
										scope.row.receiveTime
											? scope.row.receiveTime
											: "未读"
									}}
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex; justify-content: flex-end">
							<pagination
								v-model:limit="queryParams.size"
								v-model:page="queryParams.current"
								:total="total"
								style="padding: 0"
								@pagination="getList"
							/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>
	</el-container>
</template>

<script setup>
import { getCurrentInstance, onBeforeMount, reactive, ref } from "vue";
import system from "@/api/model/system";

const { proxy } = getCurrentInstance();
const total = ref(0);
const loading = ref(false);
const messageList = ref([]);
const msgList = ref([]);
const queryParams = reactive({
	current: 1,
	size: 10,
});

onBeforeMount(() => {
	dict();
	getList();
});

/**
 * 获取数据
 */
const getList = () => {
	let Organization = JSON.parse(localStorage.getItem("Organization"));
	let orgKey = JSON.parse(localStorage.getItem("orgKey"));
	let num = orgKey.content;
	let userList = Organization.content;
	let userId = userList[num].id;
	loading.value = true;
	system
		.getMessageList({
			orgId: userId,
			...queryParams,
		})
		.then((res) => {
			if (res.code === 200) {
				messageList.value = res.data.records;
				total.value = res.data.total;
			}
		})
		.finally(() => {
			loading.value = false;
		});
};

// 字典
async function dict() {
	msgList.value = await proxy.getDictList("message_type");
}

const formDict = (data, val) => {
	return data && val ? proxy.selectDictLabel(data, val) : "--";
};

//搜索
const handleSearch = () => {
	getList();
};
</script>

<style scoped></style>
