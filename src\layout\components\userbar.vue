<template>
	<div class="user-bar" @click="handleContainerClick">
		<div class="panel-item hidden-sm-and-down" @click="search">
			<el-icon><el-icon-search /></el-icon>
		</div>
		<div
			style="
				margin-right: 30px;
				color: #fff;
				cursor: pointer;
				display: flex;
				align-items: center;
			"
		>
			<!-- 右上角消息提醒 -->
			<el-badge
				v-if="listLength !== 0"
				id="el_badge"
				:offset="10"
				:value="listLength"
				style="margin-right: 12px"
				@click="showList"
			>
				<el-icon :size="25">
					<Bell />
				</el-icon>
			</el-badge>
			<div style="width: 15px" v-if="this.badgeElement"></div>
			<el-dropdown trigger="click" v-if="Organization?.length > 1">
				<div>
					<span
						style="
							color: #fff;
							cursor: pointer;
							font-size: 14px;
							position: relative;
						"
						>{{ Organization[current].name }}</span
					>
					<el-icon
						class="el-icon--right"
						color="white"
						style="position: absolute"
					>
						<arrow-down />
					</el-icon>
				</div>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item
							v-for="(item, index) in Organization"
							:key="index"
							:style="[
								{
									color:
										index === current
											? '#409eff !important'
											: '',
								},
								{
									fontWeight:
										index === current
											? 'bold !important'
											: 'normal',
								},
							]"
							@click="handleTab(item, index)"
						>
							{{ item.name }}
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<span
				v-if="Organization?.length === 1"
				style="color: #fff; cursor: pointer; text-align: center"
				>{{ Organization[0].name }}</span
			>
		</div>
		<div class="screen panel-item hidden-sm-and-down" @click="screen">
			<el-icon>
				<el-icon-full-screen />
			</el-icon>
		</div>
		<el-dropdown
			class="user panel-item"
			trigger="click"
			@command="handleUser"
		>
			<div class="user-avatar">
				<el-avatar :size="25" :src="nameImg()"></el-avatar>
				<label>{{ userName }}</label>
				<el-icon class="el-icon--right" style="color: #fff">
					<el-icon-arrow-down />
				</el-icon>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="uc">帐号信息</el-dropdown-item>
					<el-dropdown-item command="clearCache"
						>清除缓存</el-dropdown-item
					>
					<el-dropdown-item divided command="outLogin"
						>退出登录</el-dropdown-item
					>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<el-drawer
			id="dropdown-item"
			ref="drawerRef"
			v-model="drawer"
			:append-to-body="false"
			:show-close="false"
			:size="400"
			destroy-on-close
			style="position: fixed; top: 40px"
			@mousewheel="scrollChange"
		>
			<template #header>
				<div
					style="
						display: flex;
						align-items: center;
						margin-bottom: 5px;
						margin-top: -5px;
						justify-content: space-between;
					"
				>
					<!-- 消息提醒 -->
					<el-dropdown trigger="click" @command="handlerMsg">
						<div style="display: flex; align-items: center">
							<h3 style="margin-right: 5px">{{ title }}</h3>
							<el-icon>
								<arrow-down />
							</el-icon>
						</div>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item
									id="dropdown-item"
									command="all"
									style="padding: 8px 10px"
								>
									<h4 id="dropdown-item">全部通知</h4>
									<el-icon
										v-if="allMsg"
										style="margin-left: 10px"
									>
										<el-icon-check id="dropdown-item" />
									</el-icon>
								</el-dropdown-item>
								<el-dropdown-item
									id="dropdown-item"
									command="read"
									style="padding: 8px"
								>
									<h4 id="dropdown-item">未读通知</h4>
									<el-icon
										v-if="readMsg"
										style="margin-left: 10px"
									>
										<el-icon-check id="dropdown-item" />
									</el-icon>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<el-dropdown trigger="click" @command="handler">
						<el-icon style="margin-left: 200px">
							<el-icon-MoreFilled />
						</el-icon>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item
									id="dropdown-item"
									command="uc"
								>
									<h4 id="dropdown-item">标记所有为已读</h4>
									<el-icon>
										<check id="dropdown-item" />
									</el-icon>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</template>
			<el-container ref="listContainer">
				<el-main class="noPadding">
					<el-scrollbar>
						<ul class="msg-list">
							<li
								v-for="item in msgList"
								v-bind:key="item.id"
								@click="handlerRead(item)"
							>
								<a :href="item.link" target="_blank">
									<div class="msg-list__main">
										<h2 :style="{ color: item.color }">
											{{ item.title }}
										</h2>
										<p :style="{ color: item.color }">
											【{{
												formDict(typeList, item.type)
											}}】{{ item.content }}
										</p>
										<p :style="{ color: item.color }">
											{{ item.sendTime }}
										</p>
									</div>
									<!-- 对号 -->
									<div
										class="msg-list__time"
										@click.stop="handlerSvg(item)"
									>
										<el-icon><Select /></el-icon>
									</div>
								</a>
							</li>
							<div v-if="loading">Loading...</div>
							<el-empty
								v-if="msgList.length === 0"
								:image-size="100"
								description="暂无新消息"
							></el-empty>
						</ul>
					</el-scrollbar>
				</el-main>
			</el-container>
		</el-drawer>
		<el-dialog
			v-model="searchVisible"
			:width="700"
			title="搜索"
			center
			destroy-on-close
		>
			<search @success="searchVisible = false"></search>
		</el-dialog>
	</div>
</template>

<script>
import search from "./search.vue";
import tasks from "./tasks.vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
// import { getCurrentInstance } from "vue";
// const { proxy } = getCurrentInstance();
export default {
	components: {
		search,
		tasks,
		ArrowDown,
	},
	data() {
		return {
			userName: "",
			userNameF: "",
			searchVisible: false,
			tasksVisible: false,
			drawer: false,
			icon_svg: true,
			msg: false,
			allMsg: true,
			readMsg: false,
			Organization: [],
			current: 0,
			msgList: [],
			listLength: 0,
			title: "全部通知",
			readList: [],
			ids: [],
			idsList: [],
			typeList: [],
			currentPage: 1, // 当前页码
			pageSize: 10, // 每页数量
			loading: false,
			unread: "",
			threshold: 100, // 距离底部多少像素时触发加载
			scrollTop: 0,
			pages: 0,
		};
	},
	created() {
		// const proxy = this.$options.proxy;
		this.current = this.$TOOL.data.get("orgKey");
		this.Organization = this.$TOOL.data.get("Organization");
		var userInfo = this.$TOOL.data.get("USER_INFO");
		this.userName = userInfo.userName;
		this.userNameF = this.userName?.substring(0, 1);
		this.spanElement = document.getElementById("icon_cnt");
		this.badgeElement = document.getElementsByClassName("item");
		this.getDict();
		this.getList(); // 首次加载数据
		// this.handlerCss()
	},
	mounted() {
		// 监听滚动事件
		// 获取指定元素
		// document.addEventListener('click', this.handleOutsideClick);
		// const scrollview = this.$refs['drawerRef']
		// scrollview.addEventListener('scroll', this.scrollChange, true)
	},
	methods: {
		async handleTab(row, key) {
			const _this = this;
			if (this.$TOOL.data.get("orgKey") == key) return;
			ElMessageBox.confirm(`是否确认切换到${row.name}?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(async () => {
					let res = await this.$API.system.menuTreeByOrg({
						orgId: row.id,
					});
					if (res.code == 200) {
						_this.$TOOL.data.set("orgKey", key);
						var menuTree = _this.sysMenuToUiTree(res.data);
						var permissions = _this.getPermissions(res.data);
						//菜单数据写入本地
						_this.$TOOL.data.set("MENU", menuTree);
						//权限标识写入本地
						_this.$TOOL.data.set("PERMISSIONS", permissions);
						// _this.$router.replace({
						// 	path: "/home",
						// });
						location.href = "#/";
						_this.$router.go(0);
					}
				})
				.catch(() => {});
		},
		handleOutsideClick(e) {
			if (
				e.target.id === "notice_icon" ||
				e.target.id === "icon_cnt" ||
				e.target.id === "dropdown-item"
			)
				return;
			this.drawer = false;
		},
		//消息列表数据
		async getList(type) {
			this.loading = true;
			let res = await this.$API.system.getMessageList({
				current: type == "all" ? 1 : this.currentPage,
				size: type == "all" ? 10 : this.pageSize,
			});
			
			// 添加空值检查
			if (!res.data) {
				this.loading = false;
				this.msgList = [];
				this.listLength = 0;
				return;
			}
			
			this.pages = res.data?.pages || 0;
			
			// 确保 records 存在且是数组
			const records = res.data?.records || [];
			
			if (type === "all") {
				this.currentPage = 1;
				this.msgList = [...records];
			}
			if (type === "pager") {
				this.msgList = [...this.msgList, ...records];
			} else {
				this.msgList = [...records];
			}
			this.loading = false;
			
			this.msgList.forEach((item) => {
				if (item.status == "1") {
					item.color = "#3333336b";
				}
			});
			
			this.readList = [];
			this.listLength = 0;
			
			// 确保 records 存在再遍历
			if (records && records.length > 0) {
				records.forEach((item) => {
					if (item && item.noReadCount !== undefined) {
						this.listLength = item.noReadCount;
					}
				});
			}
		},
		scrollChange() {
			if (this.msgList.length != 0) {
				// if(this.unread == 'unread')return
				//获取网页的总高度
				var htmlHeight = document.documentElement.scrollHeight;
				//clientHeight网页在浏览器中的可视高度
				var clientHeight =
					document.body.clientHeight ||
					document.documentElement.clientHeight;
				//scrollTop浏览器滚动条的top位置
				var scrollTop =
					document.body.scrollTop ||
					document.documentElement.scrollTop;
				if (scrollTop + clientHeight > htmlHeight - 50) {
					if (this.currentPage < this.pages) {
						this.currentPage += 1;
						this.getList("pager");
					}
				}
			}
		},
		showList() {
			this.getList();
			this.drawer = true;
			this.unread = "read";
		},
		close() {
			this.drawer = false;
		},
		handlerDrawer() {
			this.drawer = true;
			this.unread = "read";
		},
		handlerBadge() {
			this.drawer = true;
		},
		nameImg() {
			return JSON.parse(window.localStorage.getItem("USER_INFO")).content
				.photo;
		},
		sysMenuToUiTree(sysMenu) {
			var menuTree = [];
			for (var i = 0; i < sysMenu.length; i++) {
				menuTree[i] = {
					name: sysMenu[i].alias,
					path: sysMenu[i].path,
					meta: {
						title: sysMenu[i].name,
						icon: sysMenu[i].logo,
						type: sysMenu[i].type,
						hidden: sysMenu[i].hide,
						color: sysMenu[i].affix,
						fullpage: sysMenu[i].wholePageRoute,
					},
					component: sysMenu[i].view,
				};
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					menuTree[i].children = this.sysMenuToUiTree(
						sysMenu[i].children
					);
				}
			}
			return menuTree;
		},
		getPermissions(sysMenu) {
			var permissions = [];
			for (var i = 0; i < sysMenu.length; i++) {
				if (this.$ObjectUtils.isNotEmpty(sysMenu[i].permission)) {
					permissions.push(sysMenu[i].permission);
				}
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					var pe = this.getPermissions(sysMenu[i].children);
					for (var j = 0; j < pe.length; j++) {
						if (this.$ObjectUtils.isNotEmpty(pe[j])) {
							permissions.push(pe[j]);
						}
					}
				}
			}
			return permissions;
		},
		//消息点击事件
		async handlerRead(item) {
			this.drawer = false;
			if (item.color != "#3333336b") {
				let res = await this.$API.system.updateStatus({ id: item.id });
				if (res.code == "200") {
					if (this.listLength > 0) {
						this.listLength -= 1;
						item.color = "#3333336b";
					}
				}
			}
			if (item.title.includes("借阅")) {
				this.$router.push("/borrowingApproval");
			}
			if (item.title.includes("鉴定")) {
				this.$router.push("/approve");
			}
		},
		//对号点击事件
		async handlerSvg(item) {
			if (item.color != "#3333336b") {
				let res = await this.$API.system.updateStatus({ id: item.id });
				if (res.code == "200") {
					if (this.listLength > 0) {
						this.listLength -= 1;
						item.color = "#3333336b";
					}
				}
			}
		},
		//标记所有为已读
		async handler(command) {
			if (command == "uc") {
				let res = await this.$API.system.updateStatus();
				if (res.code == "200") {
					this.listLength = 0;
					if (this.unread == "unread") {
						this.msgList = [];
					} else {
						this.getList("all");
					}
				}
			}
		},
		//全部通知和未读通知
		handlerMsg(command) {
			if (command == "read") {
				this.title = "未读通知";
				this.allMsg = false;
				this.readMsg = true;
				this.unread = "unread";
				let filteredOptions = this.msgList.filter(
					(item) => item.color !== "#3333336b"
				);
				this.msgList = filteredOptions;
			}
			if (command == "all") {
				this.title = "全部通知";
				this.unread = "read";
				this.allMsg = true;
				this.readMsg = false;
				this.getList("all");
			}
		},
		// 字典请求
		getDict() {
			this.getDictList("message_type").then((res) => {
				this.typeList = res;
			});
		},
		formDict(typeList, value) {
			return this.selectDictLabel(typeList, value);
		},
		//个人信息
		handleUser(command) {
			if (command == "uc") {
				this.$router.push({ path: "/system/usercenter" });
			}
			if (command == "cmd") {
				this.$router.push({ path: "/cmd" });
			}
			if (command == "clearCache") {
				this.$confirm(
					"清除缓存会将系统初始化，包括登录状态、主题、语言设置等，是否继续？",
					"提示",
					{
						type: "info",
						confirmButtonText: "确定",
						cancelButtonText: "取消",
					}
				)
					.then(() => {
						const loading = this.$loading();
						this.$TOOL.data.clear();
						this.$router.replace({ path: "/login" });
						setTimeout(() => {
							loading.close();
							location.reload();
						}, 1000);
					})
					.catch(() => {
						//取消
					});
			}
			if (command == "outLogin") {
				this.$confirm("确认是否退出当前用户？", "提示", {
					type: "warning",
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					cancelButtonClass: "el-button--danger",
				})
					.then(() => {
						this.$router.replace({ path: "/login" });
					})
					.catch(() => {
						//取消退出
					});
			}
		},
		//全屏
		screen() {
			var element = document.documentElement;
			this.$TOOL.screen(element);
		},
		//显示短消息
		showMsg() {
			this.msg = true;
		},
		//标记已读
		markRead() {
			this.msgList = [];
		},
		//搜索
		search() {
			this.searchVisible = true;
		},
		//任务
		tasks() {
			this.tasksVisible = true;
		},
	},
	beforeUnmount() {
		// document.removeEventListener('click', this.handleOutsideClick);
		// 获取指定元素
		const scrollview = this.$refs["drawerRef"];
		// 移除监听
		scrollview.removeEventListener("scroll", this.scrollChange, true);
	},
};
</script>

<style scoped>
.item {
	/* width: 25px; */
	margin-right: 0;
	position: relative;
	right: 13px;
	top: -3px;
}

::v-deep .el-badge__content {
	margin-top: 5px !important;
	font-size: 10px !important;
	padding: 0 6px !important;
}

::v-deep .el-overlay {
	background-color: transparent;
}

::v-deep #el-id-5241-17 {
	margin-left: 215px;
}

.item2 {
	width: 25px;
	position: relative;
	right: 12px;
}

::v-deep .el-drawer__header {
	margin-bottom: 10px;
}

.user-bar {
	display: flex;
	align-items: center;
	height: 100%;
}

.user-bar .panel-item {
	padding: 0 10px;
	cursor: pointer;
	height: 100%;
	display: flex;
	align-items: center;
}

.user-bar .panel-item i {
	font-size: 16px;
}

.user-bar .panel-item:hover {
	background: rgba(0, 0, 0, 0.1);
}

.user-bar .user-avatar {
	height: 49px;
	display: flex;
	align-items: center;
}

.user-bar .user-avatar label {
	display: inline-block;
	margin-left: 5px;
	font-size: 12px;
	cursor: pointer;
}

.msg-list li {
	border-top: 1px solid #eee;
}

.msg-list li a {
	display: flex;
	padding: 20px;
}

.msg-list li a:hover {
	background: #ecf5ff;
}

.msg-list__icon {
	width: 40px;
	margin-right: 15px;
}

.msg-list__main {
	flex: 1;
}

.msg-list__main h2 {
	font-size: 15px;
	font-weight: normal;
	color: #333;
}

.msg-list__main p {
	font-size: 12px;
	color: #333;
	line-height: 1.8;
	margin-top: 5px;
}

.msg-list__time {
	width: 50px;
	height: 50px;
	/* background-color: aqua; */
	text-align: right;
	/* color: #d41313; */
}

.dark .msg-list__main h2 {
	color: #d0d0d0;
}

.dark .msg-list li {
	border-top: 1px solid #363636;
}

.dark .msg-list li a:hover {
	background: #383838;
}

#icon_cnt {
	padding: 0;
	/* width:30px;
	height:30px; */
	/* margin-right: 15px; */
}
</style>
