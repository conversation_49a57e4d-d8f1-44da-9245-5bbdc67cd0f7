import http from "@/utils/request"

export default {
    //提交档案鉴定申请
    list: function (params) {
        return http.get(
            '/archive/info/control/list',
            params
        )
    },
    //根据Id获取档案操作数据
    queryById: function (params) {
        return http.get(
            '/archive/info/control/queryById',
			params
		)
	},
	//根据Ids获取档案信息数据
	queryByIds: function (params) {
		return http.get(
			'/archive/info/main/queryByIds',
			params
		)
	},
	// 档案操作审核信息
	auditList: function (params) {
		return http.get(
			'/archive/info/control/audit/list',
			params
		)
	},
}
