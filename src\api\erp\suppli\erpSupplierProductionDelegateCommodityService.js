import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/supplier/erpSupplierProductionDelegateCommodity/import',
      data
    )
  }
}
