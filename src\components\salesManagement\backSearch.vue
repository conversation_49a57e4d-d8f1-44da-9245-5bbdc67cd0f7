<template>
	<el-card body-style="padding-bottom:2px" class="box-card">
		<el-form
			ref="queryRef"
			:inline="true"
			:model="searchForm"
			class="form_130"
			label-width="130px"
		>
			<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
				<el-form-item label="客户">
					<el-input
						v-model="searchForm.n1"
						class="form_225"
						clearable
						placeholder="请选择客户"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item label="商品名称">
					<el-input
						v-model="searchForm.n2"
						class="form_225"
						clearable
						placeholder="请输入商品名称"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item
					label="商品自编码"
				>
					<el-input
						v-model="searchForm.n3"
						class="form_225"
						clearable
						placeholder="请输入商品自编码"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item
					v-show="showSearch"
					label="单据编号"
				>
					<el-input
						v-model="searchForm.n4"
						class="form_225"
						clearable
						placeholder="请输入单据编号"
						style="width: 220px"
					/>
				</el-form-item>
				<el-form-item v-show="showSearch" label="经手人">
					<el-input
						v-model="searchForm.n5"
						class="form_225"
						clearable
						placeholder="请输入经手人"
						style="width: 220px"
					/>
				</el-form-item>

				<el-form-item
					v-show="showSearch"

					label="制单人"
				>
					<el-input
						v-model="searchForm.n6"
						class="form_225"
						clearable
						placeholder="请输入制单人"
						style="width: 220px"
					/>
				</el-form-item>

				<el-form-item
					v-show="showSearch"

					label="审核状态"
				>
					<el-select
						v-model="searchForm.n7"
						class="form_225"
						placeholder="请选择审核状态"
						style="width: 220px"
					>
						<el-option label="未提交" value="0"/>
						<el-option label="待审核" value="1"/>
						<el-option label="审核中" value="2"/>
						<el-option label="审核通过" value="3"/>
						<el-option label="驳回" value="4"/>
						<el-option label="撤销" value="5"/>
					</el-select>
				</el-form-item>
				<el-form-item
					v-show="showSearch"
					label="入库状态"
				>
					<el-select
						v-model="searchForm.n8"
						class="form_225"
						placeholder="请选择入库状态"
						style="width: 220px"
					>
						<el-option label="未入库" value="0"/>
						<el-option label="入库中" value="1"/>
						<el-option label="已入库" value="2"/>
					</el-select>
				</el-form-item>
				<el-form-item v-show="showSearch" label="申请日期">
					<div class="xBox">
						<el-date-picker
							v-model="searchForm.n9"
							class="form_225"
							end-placeholder=""
							format="YYYY/MM/DD HH:mm:ss"
							range-separator="至"
							size="default"
							start-placeholder=""
							style="width: 220px"
							type="datetimerange"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</div>
				</el-form-item>
			</TopTitle>
		</el-form>
	</el-card>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import TopTitle from "@/components/topTitle/index.vue";

const {proxy} = getCurrentInstance();
/**
 * 数据部分
 */
const searchForm = ref({
	n9: [],
});
const showSearch = ref(false);
const data = reactive({
	discount: [],
	clearingFormType: [],
	business: [],
});
const emit = defineEmits(["handleQuery"]);
const props = defineProps({});
const handleQuery = () => {
	emit("handleQuery", searchForm.value);
};
const resetQuery = () => {
	for (let i in searchForm.value) {
		searchForm.value[i] = "";
	}
	emit("handleQuery", searchForm.value);
};
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	// data.discount = await proxy.getDictList("erp_discount");
	// data.clearingFormType = await proxy.getDictList("erp_clearingForm");
	// data.business = await proxy.getDictList("erp_business");
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	searchForm
});
</script>
<style lang="scss" scoped>
.xBox {
	width: 220px
}
</style>
