import http from "@/utils/request"

/*
 *@description: 系统字典值
 *@author: 路正宁
 *@date: 2023-03-17 11:26:48
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/dictValue/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/dictValue/delete',
       {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/dictValue/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/sys/dictValue/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/sys/dictValue/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/sys/dictValue/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/sys/dictValue/import',
      data
    )
  }
}
