import http from "@/utils/request";
// 系统配置  >  审核流程  api

export const drugApi = {
	drugList: function (params) {
		return http.get("/erp/product/erpCommodity/list", params);
	},
	drugLog: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
	journalList: function (params) {
		return http.get("/erp/product/erpCommodityApproval/list", params);
	},
	numberDrug: function (params) {
		return http.get("/erp/assist/erpApprovalNumberSet/list", params);
	},
	saveDrug: function (data) {
		return http.post("/erp/product/erpDrugsCommodity/save", data);
	},
	delDrug: function (params) {
		return http.delete("/erp/product/erpCommodity/delete", params);
	},
	TreesDrug: function (params) {
		return http.get("/erp/assist/erpMassRangeSet/treeData", params);
	},
	searchDrug: function (params) {
		return http.get("/erp/product/erpDrugsCommodity/queryById", params);
	},
	offDrug: function (params) {
		return http.get("/erp/product/erpCommodity/updateEnable", params);
	},
	delFiles: function (params) {
		return http.delete("/erp/common/erpCommonFile/delete", params);
	},
	typeDrug: function (params) {
		return http.get("/sys/dictValue/list", params);
	},
	natureDrug: function (params) {
		return http.get("/erp/assist/erpBaseCommonValues/listByEnable", params);
	},
	venderDrug: function (params) {
		return http.get("/erp/factory/erpManufacturerInformation/list", params);
	},
	treeDrug: function (params) {
		return http.get("/erp/assist/erpMassRangeSet/list", params);
	},
	siteDrug: function (params) {
		return http.get(
			"/erp/factory/erpManufacturerInformation/queryAddByName",
			params
		);
	},
	getPinyin: function (params) {
		return http.get("/erp/product/erpCommodity/getPinyin", params);
	},
	SelfCode: function (params) {
		return http.get(
			"/erp/product/erpCommodity/getCommoditySelfCode",
			params
		);
	},
};
