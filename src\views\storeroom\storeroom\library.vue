<!--
 * @Author: saya
 * @Date: 2023-07-20 09:18:51
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-07-20 09:20:05
 * @FilePath: \archive-manage-front\src\views\storeroom\storeroom\shelves.vue
 * @Description:
-->
<template>
	<div style="padding: 0 20px;">
		<div style="display: grid;grid-template-columns: repeat(5, 1fr);grid-gap: 10px;padding: 20px 0;">
			<el-card :body-style="{ padding: '0px' }" v-for="(item, index) in storeroomList" :key="index" shadow="hover"
					 style="height: 352px;">
				<img :src="item.houseCoverUrl"
					 style="min-height: 172px;max-height: 172px;object-fit: contain;width: 100%;" alt="">
				<div style="padding: 14px 14px 0 14px;">
					<el-divider style="margin: 5px 0"></el-divider>
					<div class="information">
						<span>档案库名称&nbsp;:</span>
						<span>
							{{ item.houseName ? item.houseName : '暂无' }}
						</span>
					</div>
					<el-divider style="margin: 5px 0"></el-divider>
					<div class="information">
						<span>档案库编号&nbsp;:</span>
						<span>
							{{ item.houseNum ? item.houseNum : '暂无' }}
						</span>
					</div>
					<el-divider style="margin: 5px 0"></el-divider>
					<!-- <div class="information">档案库所属仓库: {{ item.roomHouseId ? item.roomHouseId : '暂无' }}</div> -->
					<div class="information">
						<span>档案库面积&nbsp;:</span>
						<span>
							{{ item.houseArea ? item.houseArea : '暂无' }} 平米
						</span>
					</div>
					<el-divider style="margin: 5px 0"></el-divider>
					<div class="information">
						<span>档案库管理员&nbsp;:</span>
						<span>
							{{ item.houseManager ? item.houseManager.name : '暂无' }}
						</span>
					</div>
					<el-divider style="margin: 5px 0"></el-divider>
					<div class="bottom clearfix">
						<el-button type="text" icon="View" class="button" @click="addShelves(item)">查看</el-button>
						<!-- <el-button type="text" icon="Monitor" class="button" @click="openVisualBox(item)" style="margin-right: 10px">可视化</el-button> -->
					</div>
				</div>
			</el-card>
		</div>
		<div style="position: fixed;bottom: 0;right: 0;">
			<pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
						@pagination="selectList"/>
		</div>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog :title="titleDialog" v-model="openDialog" fullscreen append-to-body destroy-on-close>
			<VisualWarehouseDiagram :warehouseId="warehouseId" style="width: 98vw; height: 90vh;"></VisualWarehouseDiagram>
		</el-dialog>
	</div>
</template>
<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
import VisualWarehouseDiagram from "@/views/storeroom/storeroom/VisualWarehouseDiagram.vue";

export default {
	name: 'library',
	components: {VisualWarehouseDiagram},
	props: {
		parentDataInfo: {}
	},
	data() {
		return {
			activeName: 'first',
			total: 0,
			storeroomList: [],
			houseInfoUrl: '',
			queryParams: {
				current: 1,
				size: 10
			},
			titleDialog: '可视化查看',
			openDialog: false,
			warehouseId: ''
		}
	},
	mounted() {
		this.selectList();
	},
	methods: {
		addShelves(data) {
			this.$emit('libraryView', data);
		},
		openVisualBox(data) {
			this.warehouseId = data.id;
			this.titleDialog = "可视化查看";
			this.openDialog = true;

		},
		selectList() {
			this.storeroomList = [];
			this.houseInfoUrl = '';
			storeroomData.detailsList({
				...this.queryParams,
			}).then(result => {
				if (result.code === 200) {
					this.storeroomList = result.data.records;
					this.total = result.data.total;
				}
			}).catch(error => {
				console.log(error);
			});
		}
	}
}
</script>

<style scoped>
.information {
	margin-top: 5px;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: space-between;
	align-items: center;
}

.bottom {
	margin-top: 10px;
	line-height: 12px;
}

.button {
	padding: 0;
	float: right;
}

.image {
	width: 100%;
	height: 100%;
	max-height: 82vh;
	object-fit: contain;
	display: block;
}

.pagination-container {
	padding: 15px 0.08333rem;
}

::v-deep .el-dialog__body {
	width: 100%;
	height: 100%;
}
</style>
