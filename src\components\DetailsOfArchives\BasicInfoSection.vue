<template>
  <el-descriptions
    :column="4"
    border
    style="margin-bottom: 20px; width: 100%"
  >
    <template #title>
      <div class="cell-item" style="font-size: 14px">
        {{ "基本信息" }}
      </div>
    </template>
    <el-descriptions-item
      v-for="item in basicInfoList"
      :key="item.name"
      :label="item.config?.remark || '暂无'"
      :span="getSpan(item.config?.displayMode)"
      align="left"
      label-align="left"
      class-name="labelCustomClass"
      label-class-name="labelCustomClass"
      min-width="12.5%"
    >
      <div v-if="item.value">
        <div v-if="item.config?.type === '1'">
          {{ item.value ? item.value : "无" }}
        </div>
        <div
          v-if="item.config?.type === '2'"
          class="image-container"
        >
          <el-image
            v-for="(imgItem, index) in disposeImg(item.value)"
            v-if="disposeImg(item.value)"
            :key="index"
            :preview-src-list="srcList"
            :src="imgItem"
            fit="fill"
            class="info-image"
            @click="$emit('open-img', imgItem)"
          >
            <template #error>
              <div class="image-slot">
                <el-text>{{ "无" }}</el-text>
              </div>
            </template>
          </el-image>
          <el-text v-else>{{ "无" }}</el-text>
        </div>
        <div v-if="item.config?.type === '3'">
          {{ item.value }}
        </div>
        <div v-if="item.config?.type === '4'">
          {{
            item.value
              ? moment(item.value).format(
                  item.config?.dataConfigValue || "YYYY-MM-DD"
                )
              : "无"
          }}
        </div>
        <div v-if="item.config?.type === '5'">
          {{ item.value }}
        </div>
      </div>
      <div v-else>
        {{ "无" }}
      </div>
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup>
import moment from "moment/moment";

 defineProps({
  basicInfoList: {
    type: Array,
    default: () => []
  },
  srcList: {
    type: Array,
    default: () => []
  }
});

 defineEmits(['open-img']);

// 获取列跨度
const getSpan = (displayMode) => {
  switch (displayMode) {
    case '1': return 1;
    case '2': return 2;
    case '3': return 4;
    default: return null;
  }
};

// 处理图片数据
const disposeImg = (imgObject) => {
  let imgList = [];

  if (typeof imgObject === "string") {
    if (imgObject.includes("[")) {
      let parseArray = JSON.parse(imgObject);
      if (Array.isArray(parseArray)) {
        parseArray.forEach((item) => {
          for (const key in item) {
            if (key.toLowerCase().includes("url")) {
              imgList.push(item[key]);
            }
          }
        });
      }
    } else if (imgObject.includes("{")) {
      let parseObj = JSON.parse(imgObject);
      for (const key in parseObj) {
        if (key.toLowerCase().includes("url")) {
          imgList.push(parseObj[key]);
        }
      }
    } else {
      imgList.push(imgObject);
    }
  } else {
    return null;
  }

  return imgList;
};
</script>

<style scoped>
.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  column-gap: 10px;
}

.info-image {
  width: 100px;
  height: 100px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-lighter);
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
