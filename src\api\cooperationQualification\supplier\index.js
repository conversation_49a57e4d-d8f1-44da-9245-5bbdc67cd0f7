import http from "@/utils/request"

export default {
  list: function () {
    return http.get('/erp/supplier/erpSupplierProduction/list')
  },
  search: function (params) {
    return http.get('/erp/supplier/erpSupplierProduction/list',params)
  },
  // 委托书搜索商品
  searchCommodity: function (params) {
    return http.get('/erp/product/erpCommodity/list',params)
  },
  daleteCommodity: function (params) {
    return http.get('/erp/supplier/erpSupplierProduction/delete',params)
  },
}
