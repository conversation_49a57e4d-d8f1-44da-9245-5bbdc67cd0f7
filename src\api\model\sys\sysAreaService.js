import http from "@/utils/request"

/*
 *@description: 区域管理
 *@author: 路正宁
 *@date: 2023-03-17 11:23:41
 *@version: V1.0
*/
export default {
  treeData: function () {
    return http.get(
      '/sys/area/treeData'
    )
  },
  list: function (params) {
    return http.get(
       '/sys/area/list',
       params
    )
  },
  treeDataByParent: function (params) {
    return http.get(
       '/sys/area/treeDataByParent',
       params
    )
  },
  save: function (inputForm) {
    return http.post(
      '/sys/area/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/area/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/area/queryById',
      {id: id}
    )
  }


}
