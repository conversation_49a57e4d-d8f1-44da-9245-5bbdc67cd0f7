import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/procure/erpPurchaseOrderApproval/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/procure/erpPurchaseOrderApproval/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/procure/erpPurchaseOrderApproval/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseOrderApproval/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/procure/erpPurchaseOrderApproval/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseOrderApproval/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/procure/erpPurchaseOrderApproval/import',
      data
    )
  }
}
