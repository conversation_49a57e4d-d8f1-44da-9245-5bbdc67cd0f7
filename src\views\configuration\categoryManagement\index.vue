<template>
	<el-container>
		<el-aside v-loading="menuLoading" width="32%">
			<el-container>
				<el-main class="noPadding">
					<el-tree
						ref="menu"
						:data="menuList"
						:expand-on-click-node="false"
						:props="menuProps"
						check-strictly
						class="menu"
						default-expand-all
						draggable
						highlight-current
						node-key="id"
						show-checkbox
						@node-click="menuClick"
					>
						<template #default="{ node, data }">
              <span class="custom-tree-node">
                <span class="label">{{ node.label }}</span>
                <span v-if="data.isGroup" class="icon">
                  <el-tag>全宗</el-tag>
                </span>
                <span v-if="!data.isGroup" class="do">
                  <el-button
					  icon="Plus"
					  size="small"
					  @click.stop="add(node, data)"
				  ></el-button>
                  <el-button
					  icon="Edit"
					  size="small"
					  @click.stop="queryById(node, data)"
				  ></el-button>
                </span>
              </span>
						</template>
					</el-tree>
				</el-main>
				<el-footer style="height: 51px">
					<el-button
						icon="el-icon-plus"
						size="small"
						type="primary"
						@click="add()"
					></el-button>
					<el-button
						icon="el-icon-delete"
						plain
						size="small"
						type="danger"
						@click="delMenu"
					></el-button>
				</el-footer>
			</el-container>
		</el-aside>
		<el-container>
			<el-main
				v-if="!details"
				style="
				  	display: flex;
				  	flex-direction: row;
				  	flex-wrap: nowrap;
				  	align-content: center;
				  	justify-content: center;
				  	align-items: center;
				"
			>
				<el-result icon="info" title="温馨提醒">
					<template #sub-title>
						<p>请选择左侧非全宗门类进行操作</p>
					</template>
				</el-result>
			</el-main>
			<el-main
				v-if="details"
				ref="main"
				class="noPadding"
				style="padding: 0 20px"
			>
				<el-tabs v-model="activeName" class="demo-tabs">
					<el-tab-pane label="详情" name="first">
						<el-form
							ref="formRef"
							v-loading="loading"
							:model="form"
							:rules="rules"
							label-width="auto"
							style="margin-top: 0; padding-right: 20px"
						>
							<el-form-item label="上级" prop="parentName">
								<el-input
									v-model="form.parentName"
									disabled
									placeholder="请输入名称"
									style="width: 50%"
								/>
							</el-form-item>
							<el-form-item label="所属全宗" prop="recordGroupId">
								<el-select
									v-model="form.recordGroupId"
									disabled
									placeholder="请选择全宗ID"
									style="width: 50%"
								>
									<el-option
										v-for="item in list"
										:key="item.id"
										:label="item.recordGroupName"
										:value="item.id"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="门类名称" prop="name">
								<el-input
									v-model="form.name"
									disabled
									placeholder="请输入名称"
									style="width: 50%"
								/>
							</el-form-item>
							<el-form-item label="门类编号" prop="num">
								<el-input
									v-model="form.num"
									disabled
									placeholder="请输入门类编号"
									style="width: 50%"
								/>
							</el-form-item>
							<el-form-item label="门类整理方式" prop="recordCategoryType">
								<el-input
									v-model="form.recordCategoryType"
									disabled
									placeholder="请输入门类整理方式"
									style="width: 50%"
								/>
							</el-form-item>
							<el-form-item label="排序" prop="sort">
								<el-input
									v-model="form.sort"
									disabled
									placeholder="请输入排序号"
									style="width: 50%"
								/>
							</el-form-item>
							<el-form-item label="数据结构" prop="dataPlatform">
								<el-tree-select
									v-model="form.dataPlatform"
									:data="platformList"
									:props="{ value: 'id', label: 'name' }"
									check-strictly
									disabled
									highlight-current
									placeholder="请选择数据结构"
									style="width: 50%"
									@change="dataPlatformChange"
								>
								</el-tree-select>
							</el-form-item>

							<el-form-item
								label="筛选字段"
								prop="tableField"
								style="width: 55%"
							>
								<el-table
									:data="tableFields"
									border
									@selection-change="handleTableSelect"
								>
									<el-table-column
										type="selection"
										width="55"
									></el-table-column>
									<el-table-column
										align="center"
										label="字段名称"
										prop="tableField"
									>
										<template #default="scope">
											<el-select
												v-model="scope.row.tableField"
												disabled
												placeholder="请选择字段名称"
												@change="handleFieldChange(scope.row, scope.$index)"
											>
												<el-option
													v-for="item in fieldOptions"
													:key="item.id"
													:disabled="isFieldUsed(item.id)"
													:label="item.remark"
													:value="item.id"
												></el-option>
											</el-select>
										</template>
									</el-table-column>
									<el-table-column align="center" label="字段类型" prop="type">
										<template #default="scope">
											<el-select
												v-model="scope.row.type"
												disabled
												@change="handleParamChange(scope.row)"
											>
												<el-option label="文本" value="text"></el-option>
												<el-option label="数字" value="number"></el-option>
												<el-option label="时间" value="date"></el-option>
											</el-select>
										</template>
									</el-table-column>
									<el-table-column align="center" label="查询参数" prop="param">
										<template #default="scope">
											<el-select v-model="scope.row.param" disabled>
												<template v-if="scope.row.type === 'text'">
													<!-- 文本类型不显示选项，可添加默认提示 -->
													<el-option label="无" value="无"/>
												</template>
												<template v-else-if="scope.row.type === 'number'">
													<el-option label="大于" value=">"/>
													<el-option label="等于" value="="/>
													<el-option label="小于" value="<"/>
													<el-option label="大于等于" value=">="/>
													<el-option label="小于等于" value="<="/>
												</template>
												<template v-else-if="scope.row.type === 'date'">
													<el-option label="精确时间" value="1"/>
													<el-option label="范围时间" value="2"/>
												</template>
											</el-select>
										</template>
									</el-table-column>
								</el-table>
							</el-form-item>
							<el-form-item v-if="userInfo?.sysOrg?.id ==='753188220495929344'" label="是否对外开放" prop="isOpen">
								<el-switch
									v-model="isOpen"
									active-color="#13ce66"
									disabled
									inactive-color="#ff4949"
								>
								</el-switch>
							</el-form-item>
						</el-form>
					</el-tab-pane>
					<el-tab-pane v-if="children" label="下级门类" name="second">
						<el-table :data="DataList" border>
							<el-table-column
								align="center"
								label="所属全宗"
								prop="recordGroupId"
							>
								<template #default="scope">
									{{ findGroupName(scope.row.recordGroupId) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="门类名称" prop="name"/>
							<el-table-column align="center" label="门类编号" prop="num"/>
							<el-table-column
								align="center"
								label="门类整理方式"
								prop="recordCategoryType"
							/>
							<el-table-column
								align="center"
								class-name="small-padding fixed-width"
								label="操作"
								min-width="100px"
							>
								<template #default="scope">
									<el-button link type="primary" @click="handleAdd(scope.row)"
									><img
										src="@/assets/icons/update.png"
										style="margin-right: 5px"
									/>编辑
									</el-button>
									<el-button link type="danger" @click="handleDelete(scope.row)"
									><img
										src="@/assets/icons/delete.png"
										style="margin-right: 5px"
									/>删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-main>
			<!-- 添加或修改角色配置对话框 -->
			<el-dialog
				v-if="open"
				v-model="open"
				:title="title"
				append-to-body
				width="800px"
			>
				<el-form
					ref="formRef"
					v-loading="loading"
					:model="form"
					:rules="rules"
					label-width="auto"
					style="margin-top: 0px; padding-right: 20px"
				>
					<el-form-item label="所属全宗" prop="recordGroupId">
						<el-select v-model="form.recordGroupId" clearable placeholder="请选择全宗ID" style="width: 100%">
							<el-option
								v-for="item in list"
								:key="item.id"
								:label="item.recordGroupName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="门类名称" prop="name">
						<el-input v-model="form.name" clearable placeholder="请输入名称"/>
					</el-form-item>
					<el-form-item label="门类编号" prop="num">
						<el-input v-model="form.num" clearable placeholder="请输入门类编号"/>
					</el-form-item>
					<el-form-item label="门类整理方式" prop="recordCategoryType">
						<el-input v-model="form.recordCategoryType" clearable placeholder="请输入门类整理方式"
						/>
					</el-form-item>
					<el-form-item label="排序" prop="sort">
						<el-input v-model="form.sort" clearable placeholder="请输入排序号"/>
					</el-form-item>
					<el-form-item label="数据结构" prop="dataPlatform">
						<el-tree-select
							v-model="form.dataPlatform"
							:data="platformList"
							:props="{ value: 'id', label: 'name' }"
							check-strictly
                            clearable
							highlight-current
							placeholder="请选择数据结构"
							@change="dataPlatformChange"
						>
						</el-tree-select>
					</el-form-item>
					<el-form-item label="筛选字段" prop="tableField">
						<el-button type="primary" @click="addTableField">添加</el-button>
						<el-button type="danger" @click="deleteTableFields">删除</el-button>
						<el-table
							:data="tableFields"
							border
							@selection-change="handleTableSelect"
						>
							<el-table-column type="selection" width="55"></el-table-column>
							<el-table-column
								align="center"
								label="字段名称"
								prop="tableField"
							>
								<template #default="scope">
									<el-select
										v-model="scope.row.tableField"
										placeholder="请选择字段名称"
										@change="handleFieldChange(scope.row, scope.$index)"
									>
										<el-option
											v-for="item in fieldOptions"
											:key="item.id"
											:disabled="isFieldUsed(item.id)"
											:label="item.remark"
											:value="item.id"
										></el-option>
									</el-select>
								</template>
							</el-table-column>
							<el-table-column align="center" label="字段类型" prop="type">
								<template #default="scope">
									<el-select
										v-model="scope.row.type"
										@change="handleParamChange(scope.row)"
									>
										<el-option label="文本" value="text"></el-option>
										<el-option label="数字" value="number"></el-option>
										<el-option label="时间" value="date"></el-option>
									</el-select>
								</template>
							</el-table-column>
							<el-table-column align="center" label="查询参数" prop="param">
								<template #default="scope">
									<el-select v-model="scope.row.param">
										<template v-if="scope.row.type === 'text'">
											<!-- 文本类型不显示选项，可添加默认提示 -->
											<el-option label="无" value="无"/>
										</template>
										<template v-else-if="scope.row.type === 'number'">
											<el-option label="大于" value=">"/>
											<el-option label="等于" value="="/>
											<el-option label="小于" value="<"/>
											<el-option label="大于等于" value=">="/>
											<el-option label="小于等于" value="<="/>
										</template>
										<template v-else-if="scope.row.type === 'date'">
											<el-option label="精确时间" value="1"/>
											<el-option label="范围时间" value="2"/>
										</template>
									</el-select>
								</template>
							</el-table-column>
						</el-table>
					</el-form-item>
					<el-form-item label="是否对外开放" prop="isOpen">
						<el-switch
							v-model="isOpen"
							active-color="#13ce66"
							inactive-color="#ff4949"
						>
						</el-switch>
					</el-form-item>
				</el-form>
				<template #footer>
					<div class="dialog-footer">
						<el-button type="primary" @click="sava(form)">确 定</el-button>
						<el-button @click="() => (open = false)">取 消</el-button>
					</div>
				</template>
			</el-dialog>
		</el-container>
	</el-container>
</template>

<script>
import category from "@/api/archive/categoryManagement/category";
import completeManagement from "@/api/archive/systemConfiguration/completeManagement";
import dataManager from "@/api/archive/dataManager";

// import save from "./save";

export default {
	computed: {
		showParamSelector() {
			return this.currentField.type !== "text";
		},
	},
	name: "settingMenu",
	components: {
		// save,
	},
	data() {
		return {
			currentGroupId: null,
			isOpen: false,
			usedFields: [],
			argId: [],
			paramDialogVisible: false,
			tableFields: [],
			selectedTableFields: [],
			currentField: {
				id: null,
				type: "text",
				param: "",
				name: "",
			},
			fieldParams: [], // 存储字段参数 { [fieldId]: { type, param } }
			data: [],
			value: [],
			fieldOptions: [], // 表字段选项列表
			fieldLoading: false,
			platformList: [],
			//菜单加载中
			menuLoading: false,
			//菜单列表
			menuList: [],
			menuProps: {
				label: (data) => {
					return data.name;
				},
			},
			//菜单筛选属性
			menuFilterText: "",
			// 唤起弹窗
			open: false,
			// 弹窗
			loading: false,
			// 新增表单
			form: {},
			// 全宗ID选择集合
			list: [],
			// 右侧详情信息
			DataList: [],
			// 右边是否显示
			details: false,
			// tabs
			activeName: "first",
			// 是否有下级分类
			children: false,
			rules: {
				recordGroupId: [
					{required: true, message: "全宗ID不能为空", trigger: "blur"},
				],
				name: [{required: true, message: "名称不能为空", trigger: "blur"}],
				num: [{required: true, message: "编号不能为空", trigger: "blur"}],
				recordCategoryType: [
					{required: true, message: "整理方式不能为空", trigger: "blur"},
				],
				sort: [
					{required: true, message: "排序不能为空", trigger: "blur"},
					{pattern: /^[0-9]*$/, message: "只能为数字", trigger: "blur"},
				],
				//dataPlatform: [{required: true, message: "数据结构不能为空", trigger: "blur"}],
				//tableField:[{required: true, message: "表字段不能为空", trigger: "blur"}],
				//isOpen: [{required: true, message: "", trigger: "blur"}],
			},
            userInfo:[],
		};
	},
	watch: {
		"currentField.type"(newVal) {
			// 当数据类型变化时重置比较参数
			if (newVal === "text") {
				this.currentField.param = null;
			} else {
				// 设置默认比较参数
				this.currentField.param = newVal === "date" ? "<" : ">";
			}
		},
		//菜单过滤，顶部搜索框
		menuFilterText(val) {
			this.$refs.menu.filter(val);
		},
	},
	mounted() {
        this.userInfo = this.$TOOL.data.get("USER_INFO");
		this.getMenu();
		this.getTree();
	},
	methods: {
		// 检查字段是否被使用
		isFieldUsed(id) {
			return this.usedFields.some((item) => item === id);
		},
		handleFieldChange(row) {
			const selectedField = this.fieldOptions.find(
				(f) => f.id === row.tableField
			);
			if (!selectedField) return;
			// 根据字段类型设置默认值
			switch (selectedField.type.toString()) {
				case "1":
					row.type = "text";
					break;
				case "4":
					row.type = "date";
					break;
				default:
					row.type = "text"; // 默认类型
			}
			if (row) {
				this.argId.push(row.tableField);
			}
			this.handleParamChange(row);
		},
		handleParamChange(row) {
			// 根据字段类型设置默认值
			switch (row.type.toString()) {
				case "number":
					row.param = "=";
					break;
				case "date":
					row.param = "2";
					break;
				default:
					row.param = "无"; // 默认类型
			}
		},
		handleTableSelect(selection) {
			this.selectedTableFields = selection.map((item) =>
				this.tableFields.indexOf(item)
			);
		},
		// 原有的方法...
		addTableField() {
			const newField = {
				tableField: `列名称${this.tableFields.length + 1}`,
				type: "选择字段类型",
				param: "",
			};
			this.tableFields.push(newField);
			this.usedFields = this.usedFields.concat(
				this.argId[this.argId.length - 1]
			);
			this.argId = [];
		},
		deleteTableFields() {
			this.selectedTableFields.sort((a, b) => b - a); // 倒序排序，防止删除时索引混乱
			this.selectedTableFields.forEach((index) => {
				this.usedFields = this.usedFields.filter(
					(item) => item != this.tableFields[index].tableField
				);
				this.tableFields.splice(index, 1);
			});
			this.argId = [];
			this.selectedTableFields = []; // 清空已选数组
		},

		async confirmParam() {
			const res = await dataManager.getFieldsById({
				id: this.currentField.id,
			});
			const params = {
				id: this.currentField?.id,
				type: this.currentField?.type,
				param: this.currentField?.param,
				name: res?.data?.name,
				remark: res?.data?.remark,
			};
			this.fieldParams.push(params);
			this.paramDialogVisible = false;
		},
		cancelParam() {
			// 移除未确认的字段
			this.value = this.value.filter((id) => id !== this.currentField.id);
			this.paramDialogVisible = false;
		},

		//数据分类切换
		async dataPlatformChange(platformId) {
			// 清空已选字段
			this.value = [];
			// 保留原有的版本列表请求（如果仍需要）
			const re = await dataManager.versionList({
				platformId: platformId,
				current: 1,
				size: -1,
			});
			const reArry = re.data.records.map((item) => ({
				structureId: item?.structureId,
			}));
			try {
				this.fieldLoading = true;
				// 清空已选字段
				this.form.tableField = "";

				//const children = await dataManager.dataConfigQueryById({
				//id:reArry[0].structureId
				//});
				//const childrenList = children.data.children;

				// 获取字段数据
				const res = await dataManager.getFieldsByPlatform({
					configId: reArry[0]?.structureId,
				});
				if (res.code === 200) {
					this.fieldOptions = res.data.map((item) => ({
						id: item.id,
						name: item.name,
						type: item.type,
						remark: item.remark || "无备注",
						length: item.length,
						dataConfigValue: item.dataConfigValue,
					}));
				} else {
					this.$message.error("字段加载失败");
					this.fieldOptions = [];
				}
				//const childrenRes = await dataManager.getFieldsByPlatform({
				//configId: childrenList[0].id
				// });
				//this.fieldOptions = this.fieldOptions.concat(childrenRes);
			} catch (error) {
				this.fieldOptions = [];
			} finally {
				this.fieldLoading = false;
			}
		},
		/*
		 * 判断全宗名称
		 */
		findGroupName(groupId) {
			return this.list
				.filter((group) => group.id === groupId)
				.map((item) => item.recordGroupName)[0];
		},
		/*
		 * 菜单点击
		 * @author: saya
		 * @date: 2023-03-23 11:23:10
		 */
		async menuClick(data) {
			if (!data.isGroup) {
				this.details = true;
				this.DataList = data.children ? data.children : [];
				// 详情查询
				this.getList();
				this.menuLoading = true;
				let res = await category.queryById({
					id: data.id,
				});
				if (res.code === 200) {
					if (res?.data?.children?.length !== 0) {
						this.children = true;
					}
					this.form.parentName = res?.data?.parent?.name
						? res?.data?.parent?.name
						: res?.data?.name;
					this.form.name = res?.data?.name;
					this.form.num = res?.data?.num;
					this.form.recordGroupId = res?.data?.recordGroupId;
					this.form.recordCategoryType = res?.data?.recordCategoryType;
					this.form.sort = res?.data?.sort;
					this.form.parentId = res?.data?.parent?.id;
					this.form.id = res?.data?.id;
					this.form.dataPlatform = res?.data?.dataPlatform;
					this.isOpen = res?.data?.isOpen === "1";
					const re = await dataManager.versionList({
						platformId: res?.data?.dataPlatform,
						current: 1,
						size: -1,
					});
					const reArry = re.data.records.map((item) => ({
						structureId: item?.structureId,
					}));
					// 获取字段数据
					const ress = await dataManager.getFieldsByPlatform({
						configId: reArry[0]?.structureId,
					});
					if (ress.code === 200) {
						this.fieldOptions = ress.data.map((item) => ({
							id: item.id,
							name: item.name,
							type: item.type,
							remark: item.remark || "无备注",
							length: item.length,
							dataConfigValue: item.dataConfigValue,
						}));
					}
					this.tableFields = JSON.parse(res?.data?.tableField || "[]").map(
						(field) => ({
							tableField: field.id,
							param: field.param,
							type: field.type,
						})
					);
				} else {
					this.$Response.errorNotice(null, "查询失败");
				}
				//释放页面加载
				this.menuLoading = false;
			} else {
                this.currentGroupId = data.id;
                this.details = false
            }
		},
		getTree() {
			dataManager.classifyList().then((res) => {
				this.platformList = res.data;
			});
		},

		/**
		 * @brief: 查询左边列表
		 * @return {Promise}
		 * @note: 获取全宗列表并查询每个全宗下的门类信息
		 */
		async getMenu() {
			this.menuLoading = true;

			try {
				const result = await completeManagement.getList({
					current: 1,
					size: -1,
				});

				if (result.code !== 200) {
					this.$Response.errorNotice(null, result.msg || "获取全宗列表失败");
					return;
				}

				const treeData = result.data;
				if (!treeData?.records?.length) {
					this.menuList = [];
					return;
				}

				// 并发查询每个全宗下的门类信息，限制并发数
				const batchSize = 3; // 限制并发数，避免过多请求
				const processedRecords = [];

				for (let i = 0; i < treeData.records.length; i += batchSize) {
					const batch = treeData.records.slice(i, i + batchSize);

					const batchPromises = batch.map(async (record, batchIndex) => {
						const index = i + batchIndex;
						try {
							const res = await category.groupList({
								current: 1,
								size: -1,
								groupId: record.id,
								isAut: '0'  // 是否门类权限查询，1：是，0：否
							});

							if (res.code === 200) {
								// 处理门类数据
								const processedRecord = {
									...record,
									children: res.data || [],
									name: record.recordGroupName,
									isGroup: true,
									disabled: true
								};

								// 设置当前组ID（保留第一个有效的）
								if (index === 0) {
									this.currentGroupId = record.id;
								}

								return processedRecord;
							} else {
								console.error(`全宗${index + 1}门类查询失败:`, res.msg);
								this.$Response.errorNotice(null, `全宗${index + 1}下门类数据查询失败`);
								return {
									...record,
									children: [],
									name: record.recordGroupName,
									isGroup: true,
									disabled: true
								};
							}
						} catch (error) {
							console.error(`全宗${index + 1}门类查询异常:`, error);
							this.msgError(`全宗${index + 1}下的门类数据查询失败`);
							return {
								...record,
								children: [],
								name: record.recordGroupName,
								isGroup: true,
								disabled: true
							};
						}
					});

					const batchResults = await Promise.all(batchPromises);
					processedRecords.push(...batchResults);
				}

				// 排序处理
				this.menuList = this.recursiveSort(processedRecords, "sort");

			} catch (error) {
				console.error("获取菜单数据失败:", error);
				this.$Response.errorNotice(null, "系统菜单数据查询失败");
				this.menuList = [];
			} finally {
				this.menuLoading = false;
			}
		},
		/*
		 * 查询全宗下的门类信息
		 */
		dataInfo(treeData) {
			category
				.groupList({
					current: 1,
					size: -1,
					groupId: treeData.id
				})
				.then((res) => {
					if (res.code === 200) {
						if (res.data.length > 0) {
							treeData.children = res.data;
							treeData.name = treeData.recordGroupName;
						}
					} else {
						this.$Response.errorNotice(null, "全宗下门类数据查询失败");
					}
				})
				.catch(() => {
					this.msgError("查询失败");
				});
			return treeData;
		},
		recursiveSort(list, sortBy) {
			// 首先对顶级列表进行排序
			list.sort((a, b) => a[sortBy] - b[sortBy]);

			// 遍历列表，对每个有子级的对象递归调用排序
			list.forEach((item) => {
				if (item.children && item?.children?.length > 0) {
					this.recursiveSort(item.children, sortBy);
				}
			});
			return list;
		},
		/*
		 * 添加菜单
		 * @author: saya
		 */
		async add(node,data=null) {
            if(!node && !data && this.currentGroupId && this.details){
                return this.msgError("只能给全宗新增子门类");
            }
			const groupId = data ? data.recordGroupId : this.currentGroupId;
			//组装基本菜单数据
			let newMenuName =
				"未命名" +
				(data
					? data.children
						? data.children.length + 1
						: "1"
					: this.menuList.length + 1);
			let newMenuData = {
				//父级ID
				parent: {
					id: data ? data.id : "0",
				},
				// 名称
				name: newMenuName,
				// 档案门类编号
				num: "",
				// 全宗ID
				recordGroupId: groupId,
				// 整理方式
				recordCategoryType: "未填写",
				// 排序
				sort: data
					? data.children
						? data.children.length + 1
						: "1"
					: this.menuList.length + 1,
			};
			//页面加载中
			this.menuLoading = true;
			// 保存菜单方法
			let res = await category.save(newMenuData);
			if (res.code === 200) {
				// this.getMenu();
				this.$message.success("添加成功");
			} else {
				this.$Response.errorNotice(res, "添加失败");
			}
			//释放页面加载
			this.menuLoading = false;
			newMenuData.id = res.data.id;
			//动态追加添加后的菜单节点
			this.$refs.menu.append(newMenuData, node);
			//设置菜单列表当前节点的焦点
			this.$refs.menu.setCurrentKey(newMenuData.id);
			//设置表单数据
			this.getMenu()
		},
		/*
		 * 根据Id获取门类配置数据
		 * @author: saya
		 */
		async queryById(node, data) {
			this.getList();
			this.open = true;
			this.menuLoading = true;
			let res = await category.queryById({
				id: data.id,
			});
			if (res.code === 200) {
				this.form.name = res.data.name;
				this.form.num = res.data.num;
				this.form.recordGroupId = res.data.recordGroupId;
				this.form.recordCategoryType = res.data.recordCategoryType;
				this.form.sort = res.data.sort;
				this.form.parentId = res?.data?.parent?.id;
				this.form.id = res.data.id;
				this.form.dataPlatform = res.data.dataPlatform;
				this.isOpen = res.data.isOpen === "1";
				const re = await dataManager.versionList({
					platformId: res.data.dataPlatform,
					current: 1,
					size: -1,
				});
				const reArry = re.data.records.map((item) => ({
					structureId: item?.structureId,
				}));
				// 获取字段数据
				const ress = await dataManager.getFieldsByPlatform({
					configId: reArry[0]?.structureId,
				});
				if (ress.code === 200) {
					this.fieldOptions = ress.data.map((item) => ({
						id: item.id,
						name: item.name,
						type: item.type,
						remark: item.remark || "无备注",
						length: item.length,
						dataConfigValue: item.dataConfigValue,
					}));
				}
				this.tableFields = JSON.parse(res?.data?.tableField || "[]").map(
					(field) => ({
						tableField: field.id,
						param: field.param,
						type: field.type,
					})
				);
				this.tableFields.forEach((item) => {
					this.usedFields.push(item.tableField);
				});
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
			//释放页面加载
			this.menuLoading = false;
		},
		/*
		 * 更新门类配置
		 * @author: saya
		 */
		async sava(data) {
            this.$refs["formRef"].validate(valid => {
                if (valid) {
                    const tableField = JSON.stringify(
                        this.tableFields.map((field) => {
                            const fieldInfo = this.fieldOptions.find(
                                (option) => option.id === field.tableField
                            );
                            return {
                                type: field.type,
                                param: field.param,
                                id: field.tableField,
                                remark: fieldInfo ? fieldInfo?.remark : "",
                                name: fieldInfo ? fieldInfo?.name : "",
                            };
                        })
                    );
                    // 构建字段数据
                    //const tableField = JSON.stringify(this.fieldParams);
                    let newMenuData = {
                        //父级ID
                        parent: {
                            id: data.parentId,
                        },
                        //  id
                        id: data?.id,
                        //  档案门类编号
                        num: data?.num,
                        //  名称
                        name: data?.name,
                        //  全宗ID
                        recordGroupId: data?.recordGroupId,
                        //  整理方式
                        recordCategoryType: data?.recordCategoryType,
                        //  排序
                        sort: data?.sort,
                        //数据结构
                        dataPlatform: data?.dataPlatform,
                        tableField: tableField,
                        isOpen: this.isOpen ? "1" : "0",
                    };
                    //页面加载中
                    this.menuLoading = true;
                    category
                        .save(newMenuData)
                        .then((res) => {
                            if (res.code === 200) {
                                this.open = false;
                                for (let i = 0, len = this.DataList.length; i < len; i++) {
                                    if (this.DataList[i].id === res.data.id) {
                                        this.DataList[i].id = res?.data?.id;
                                        this.DataList[i].name = res?.data?.name;
                                        this.DataList[i].num = res?.data?.num;
                                        this.DataList[i].recordGroupId = res?.data?.recordGroupId;
                                        this.DataList[i].recordCategoryType =
                                            res?.data?.recordCategoryType;
                                        this.DataList[i].sort = res?.data?.sort;
                                        this.DataList[i].dataPlatform = res?.data?.dataPlatform;
                                        this.DataList[i].tableField = res?.data?.tableField;
                                        this.DataList[i].isOpen = res?.data?.isOpen === "1";
                                    }
                                }
                                this.getMenu();
                                this.$message.success("修改成功");
                            }
                        })
                        .catch((err) => {
                            this.$Response.errorNotice(err, "修改失败");
                        });
                    //释放页面加载
                    this.menuLoading = false;
                }
            })
		},

		/**
		 * @: 查询全宗ID
		 * @return {*}
		 */
		getList() {
			completeManagement
				.getList({
					current: 1,
					size: -1,
				})
				.then((res) => {
					if (res.code === 200) {
						this.list = res.data.records;
					}
				});
		},

		/*
		 * 删除菜单
		 * @author: saya
		 * @date: 2023-03-23 17:46:37
		 */
		async delMenu() {
			//获取选中的节点
			let CheckedNodes = this.$refs.menu.getCheckedNodes();
			if (CheckedNodes.length === 0) {
				this.$message.warning("请选择需要删除的项");
				return false;
			}
			//删除操作确认
			let confirm = await this.$confirm("确认删除已选择的菜单吗？", "提示", {
				type: "warning",
				confirmButtonText: "删除",
				confirmButtonClass: "el-button--danger",
				cancelButtonText: "取消",
				cancelButtonClass: "el-button--primary el-button--large",
			}).catch(() => {
			});
			if (confirm !== "confirm") {
				return false;
			}
			// //页面加载
			this.menuLoading = true;
			//请求参数处理删除id参数
			let reqData = CheckedNodes.map((item) => item.id).join(",");
			//调用删除接口
			let res = await category.delete({ids: reqData});
			this.menuLoading = false;
			if (res.code === 200) {
				//在列表中移除已删除的菜单项
				CheckedNodes.forEach((item) => {
					let node = this.$refs.menu.getNode(item);
					//移除菜单项
					this.$refs.menu.remove(item);
                    this.$message.success("删除成功！");
					if (node.isCurrent) {
						//当前删除的是当前编辑的菜单，则清空编辑表单页面
						this.getMenu();
					}
				});
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
		},
		/**
		 * 右边下级门类编辑
		 * @return {*}
		 */
		async handleAdd(data) {
			this.getList();
			this.open = true;
			this.menuLoading = true;
			let res = await category.queryById({
				id: data.id,
			});
			if (res.code === 200) {
				this.form.name = res?.data?.name;
				this.form.num = res?.data?.num;
				this.form.recordGroupId = res?.data?.recordGroupId;
				this.form.recordCategoryType = res?.data?.recordCategoryType;
				this.form.sort = res?.data?.sort;
				this.form.parentId = res?.data?.parent?.id;
				this.form.id = res?.data?.id;
				this.form.dataPlatform = res?.data?.dataPlatform;
				this.form.tableField = res?.data?.tableField;
			} else {
				this.$Response.errorNotice(res, "修改失败");
			}
			//释放页面加载
			this.menuLoading = false;
		},
		/**
		 * 右侧下级门类删除
		 * @return {*}
		 */
		async handleDelete(data) {
			let confirm = await this.$confirm("确认删除菜单吗？", "提示", {
				type: "warning",
				confirmButtonText: "删除",
				confirmButtonClass: "el-button--danger",
			}).catch(() => {
			});
			if (confirm !== "confirm") {
				return false;
			}
			// //页面加载
			this.menuLoading = true;
			//请求参数处理删除id参数
			let reqData = data.id;
			//调用删除接口
			let res = await category.delete({ids: reqData});
			this.menuLoading = false;
			if (res.code === 200) {
				//在列表中移除已删除的菜单项
				this.DataList.forEach((item) => {
					// //移除菜单项
					if (reqData === item.id) {
						this.$refs.menu.remove(item);
					}
				});
			} else {
				this.$Response.errorNotice(res, "删除失败");
			}
		},
	},
};
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
	display: flex;
	flex: 1;
	height: 100%;
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	height: 100%;
	padding-right: 24px;
}

.custom-tree-node .label {
	display: flex;
	align-items: center;
	height: 100%;
}

.custom-tree-node .label .el-tag {
	margin-left: 5px;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .do {
	display: inline-block;
}

.demo-tabs > .el-tabs__content {
	padding: 32px;
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}
</style>
