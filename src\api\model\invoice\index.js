import http from "@/utils/request";

export const inputInvoice = {
	saveList: function (data) {
		return http.post("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/create", data);
	},
	delList: function (params) {
		return http.delete("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/delete", params);
	},
	checkList: function (params) {
		return http.get("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/detailById", params);
	},
	getList: function (params) {
		return http.get("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/list", params);
	},
	delFile: function (params) {
		return http.delete("/erp/common/erpCommonFile/delete", params);
	},
};
export const relevanceInput = {
	getReceipts: function (params) {
		return http.get("/erp/procure/erpPurchaseWarehousingRecord/list", params);
	},
	supplierList: function (params) {
		return http.get("/erp/supplier/erpSupplierProduction/list", params);
	},
	detailsInput: function (params) {
		return http.get("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/detailByinvoiceNo", params);
	},
	saveInput: function (data) {
		return http.post("/erp/invoice/purchase/erpPurchaseInvoice/create", data);
	},
	getList: function (params) {
		return http.get("/erp/invoice/purchase/erpPurchaseInvoice/list", params);
	},
	delList: function (params) {
		return http.delete("/erp/invoice/purchase/erpPurchaseInvoice/delete", params);
	},
	detailList: function (params) {
		return http.get("/erp/invoice/purchase/erpPurchaseInvoice/detailById", params);
	},
	delGoods: function (params) {
		return http.delete("/erp/invoice/purchase/erpPurchaseInvoiceForm/delete", params);
	},
	delInvoice: function (params) {
		return http.delete("/erp/invoice/purchase/erpPurchaseInvoiceUnion/delete", params);
	},
	auditLists: function (params) {
		return http.get("/erp/invoice/purchase/erpPurchaseInvoiceApproval/list", params);
	},
	logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	}
}

