import request from '@/utils/httpRequest'

export default {
  save: function (inputForm) {
    return request({
      url: '/erp/assist/erpMassRangeSet/save',
      method: 'post',
      data: inputForm
    })
  },

  delete: function (ids) {
    return request({
      url: '/erp/assist/erpMassRangeSet/delete',
      method: 'delete',
      params: {ids: ids}
    })
  },

  queryById: function (id) {
    return request({
      url: '/erp/assist/erpMassRangeSet/queryById',
      method: 'get',
      params: {id: id}
    })
  },

  treeData: function () {
    return request({
      url: '/erp/assist/erpMassRangeSet/treeData',
      method: 'get'
    })
  }
}
