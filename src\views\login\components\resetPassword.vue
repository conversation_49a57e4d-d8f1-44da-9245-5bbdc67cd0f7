<template>
	<el-form ref="loginForm" :model="form" :rules="rules" label-width="0" size="large" @keyup.enter="login"
		label-position="top" hide-required-asterisk=“true” class="form">
		<el-form-item prop="user" label="手机号" class="el-form-item__label">
			<el-input v-model="form.user" clearable placeholder="请输入手机号" class="input"></el-input>
		</el-form-item>
		<el-form-item prop="password" label="手机验证码" class="el-form-item__label2">
			<el-input v-model="form.password" clearable placeholder="请输入验证码" class="input2"></el-input>
			<el-button @click="getYzm" :disabled="disabled">{{ this.$t('login.smsGet') }}<span v-if="disabled">
					({{ time }})</span></el-button>
		</el-form-item>
		<el-form-item>
			<el-button type="primary" :loading="islogin" @click="submitLogin" class="login">下一步</el-button>
		</el-form-item>
		<div class="login-reg">
			<a style="color: #2878FF;cursor:pointer" @click="returnLogin" >返回登录</a>
		</div>
	</el-form>
</template>

<script>

export default {
	props:{
	handlerUser:Function,
  },
	data() {
		return {
			form: {
				user: "",
				password: "",
				autologin: false,
			},
			rules: {
				user: [
					{
						required: true,
						message: '请输入手机号',
						trigger: "blur",
					},
				],
				password: [
					{
						required: true,
						message: '请输入验证码',
						trigger: "blur",
					},
				],
			},
			//登录按钮锁
			islogin: false,
			orgList: [],
			disabled: false,
		};
	},
	watch: {},
	mounted() { },
	methods: {
		async getYzm(){
				var validate = await this.$refs.loginForm.validateField("phone").catch(()=>{})
				if(!validate){ return false }

				this.$message.success(this.$t('login.smsSent'))
				this.disabled = true
				this.time = 60
				var t = setInterval(() => {
					this.time -= 1
					if(this.time < 1){
						clearInterval(t)
						this.disabled = false
						this.time = 0
					}
				},1000)
			},
		/*
		 * 提交登录事件
		 * @author: 路正宁
		 * @date: 2023-03-21 11:17:03
		 */
		async submitLogin() {
			//表单校验
			var validate = this.$refs.loginForm.validate().catch(() => { });
			if (!validate) {
				return false;
			}
			//登录请求体
			var loginBody = {
				username: this.form.user,
				//password: this.$TOOL.crypto.MD5(this.form.password)
				password: this.form.password,
			};
			//锁定登录按钮
			this.islogin = true;
			//执行登录
			var loginUser = await this.login(loginBody);
			if (this.$ObjectUtils.isEmpty(loginUser)) {
				//释放登录按钮
				this.islogin = false;
				return false;
			}

			//获取系统菜单
			await this.getMenuTree();
			this.$message.success("登录成功");
			//释放登录按钮
			this.islogin = false;
			//获取菜单
			this.$router.replace({
				path: "/home",
			});
		},
		/*
		 *@functionName: 登录接口
		 *@params1: loginBody:登录参数
		 *@author: 路正宁
		 *@date: 2023-03-21 15:14:51
		 */
		async login(loginBody) {
			//执行登录
			var loginUser = await this.$API.auth.login(loginBody);
			//校验对象是否为空
			if (this.$ObjectUtils.isEmpty(loginUser)) {
				this.$MessageBox.errorNotie("登录失败，请重试");
				return null;
			}
			//校验状态码是否成功
			if (loginUser.code == 200) {
				//登录成功处理
				//保存token到cookie中
				this.$TOOL.cookie.set("TOKEN", loginUser.data.token, {
					expires: this.form.autologin ? 24 * 60 * 60 : 0,
				});
				//保存用户信息到cookie中
				this.$TOOL.data.set("USER_INFO", loginUser.data.userInfo);
				this.$TOOL.data.set("Organization", loginUser.data.orgList)
				this.$TOOL.data.set("ROLE_LIST", loginUser.data.roleList);
				// if (loginUser?.data?.orgList.length) {
				//   let res = await this.$API.system.menuTreeByOrg({ orgId: loginUser?.data?.orgList[0]?.id })
				//   this.$TOOL.data.set("USER_INFO", res);
				//   this.$TOOL.data.set("Organization", loginUser.data.orgList)
				// }

				return loginUser;
			} else {
				//登录失败处理
				this.$Response.errorNotice(loginUser, "登录失败，请重试");
				return null;
			}
		},
		/*
		 * 获取系统菜单
		 * @author: 路正宁
		 * @date: 2023-04-07 10:51:24
		 */
		async getMenuTree() {
			const orgList = this.$TOOL.data.get("Organization")
			if (orgList?.length) {
				let response = await this.$API.system.menuTreeByOrg({ orgId: orgList[0]?.id })
				var menuTree = this.sysMenuToUiTree(response.data);
				var permissions = this.getPermissions(response.data);
				//菜单数据写入本地
				this.$TOOL.data.set("MENU", menuTree);
				//权限标识写入本地
				this.$TOOL.data.set("PERMISSIONS", permissions);
				this.$TOOL.data.set("orgKey", 0);
				return
			}
			var res = await this.$API.system.menuTree();
			if (res.code != 200) {
				this.$Response.errorNotice(res, "当前用户无任何菜单权限，请联系系统管理员");
				return false;
			}
			var menuTrees = this.sysMenuToUiTree(res.data);
			var permissionss = this.getPermissions(res.data);
			//菜单数据写入本地
			this.$TOOL.data.set("MENU", menuTrees);
			//权限标识写入本地
			this.$TOOL.data.set("PERMISSIONS", permissionss);

			return true;
		},

		/*
		 * 菜单数据转树UI数据结构
		 * @author: 路正宁
		 * @date: 2023-04-07 11:20:31
		 */
		sysMenuToUiTree(sysMenu) {
			var menuTree = [];
			for (var i = 0; i < sysMenu.length; i++) {
				menuTree[i] = {
					name: sysMenu[i].alias,
					path: sysMenu[i].path,
					meta: {
						title: sysMenu[i].name,
						icon: sysMenu[i].logo,
						type: sysMenu[i].type,
						hidden: sysMenu[i].hide,
						color: sysMenu[i].affix,
						fullpage: sysMenu[i].wholePageRoute,
					},
					component: sysMenu[i].view,
				};
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					menuTree[i].children = this.sysMenuToUiTree(sysMenu[i].children);
				}
			}
			return menuTree;
		},
		/*
		 *提取权限标识
		 * @author: 路正宁
		 * @date: 2023-04-07 15:21:52
		 */
		getPermissions(sysMenu) {
			var permissions = [];
			for (var i = 0; i < sysMenu.length; i++) {
				if (this.$ObjectUtils.isNotEmpty(sysMenu[i].permission)) {
					permissions.push(sysMenu[i].permission);
				}
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					var pe = this.getPermissions(sysMenu[i].children);
					for (var j = 0; j < pe.length; j++) {
						if (this.$ObjectUtils.isNotEmpty(pe[j])) {
							permissions.push(pe[j]);
						}
					}
				}
			}
			return permissions;
		},
		returnLogin(){
			this.handlerUser()
		}
	},
};
</script>

<style scoped>
.el-form--large.el-form--label-top .el-form-item .el-form-item__label {
	width: 120px;
	height: 16px;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #666666;
	line-height: 24px;
	margin-bottom: 15px;
	line-height: 22px;
}

.el-form-item__label2 {
	margin-top: 65px;
	position: relative;
}

.el-form-item__label2 button {
	position: absolute;
	/* top: 7px;
        right: 0; */
	left: 275px;
	background: #F5F5F5;
	width: 130px;
	height: 28px;
	background: #F5F5F5;
	border-radius: 4px;
	border: none;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #CACACA;
	line-height: 24px;
}

.login {
	margin-top: 10px;
	width: 417px;
	height: 47px;
	background: linear-gradient(-5deg, #6394FF, #2878FF);
	border-radius: 4px;
}

.form {
	margin-top: 35px;
}

.input {
	width: 418px;
	height: 48px;
	background: #FFFFFF;
	border: 1px solid #D5D5D5;
	border-radius: 4px;
}

.input2 {
	width: 418px;
	height: 48px;
	/* margin-top: 20px; */
	background: #FFFFFF;
	border: 1px solid #D5D5D5;
	border-radius: 4px;
}

.login-reg {
	margin-top: -10px;
}
</style>
