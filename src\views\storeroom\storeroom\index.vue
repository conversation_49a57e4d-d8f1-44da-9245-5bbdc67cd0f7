<template>
	<el-container>
		<el-aside width="280px" v-loading="menuloading">
			<el-container>
				<el-main class="noPadding">
					<el-tree ref="menu" class="menu" node-key="id" :data="menuList" :props="menuProps" draggable
							 :expand-on-click-node="false" check-strictly default-expand-all highlight-current
							 show-checkbox
							 @node-click="menuClick">
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									{{ node.label }}
								</span>
								<span class="do">
									<el-button icon="el-icon-plus"
											   size="small"
											   v-if="node.level < 3"
											   @click.stop="addLibrary('0', node, data)"></el-button>
									<el-button size="small" @click.stop="queryById(node, data)">编辑</el-button>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
				<el-footer style="height: 51px">
					<el-button type="primary" size="small" icon="el-icon-plus"
						@click="addLibrary('1', '1', '1')"></el-button>
					<el-button type="danger" size="small" plain icon="el-icon-delete" @click="delMenu"></el-button>
				</el-footer>
			</el-container>
		</el-aside>
		<el-container>
			<el-main ref="main" class="noPadding">
				<el-button v-if="detailsOpen === false" icon="Back" plain size="default" style="margin: 10px 0 0 20px"
						   type="primary" @click="returnWarehouseView">返回仓库总览
				</el-button>
				<!-- 档案库展示 -->
				<div v-if="detailsOpen">
					<library :key="componentKey" @libraryView="libraryView" :parentDataInfo=parentDataInfo></library>
				</div>
				<!-- 档案室展示 -->
				<div v-if="openShelves">
					<shelves :key="componentKey" @shelvesView="shelvesView" :parentDataInfo=parentDataInfo></shelves>
				</div>
				<!-- 柜展示 -->
				<div v-if="openContainer">
					<container :key="componentKey" @handleView="handleView" :parameters="parameters"></container>
				</div>
				<!-- 格子展示 -->
				<div v-if="openStorey">
					<lattice :key="componentKey" :latticeView="latticeView"></lattice>
				</div>
			</el-main>
			<!-- 新增 、修改-->
			<el-dialog :title="title" v-model="openAdd" width="800px" append-to-body v-show="openAdd">
				<newAdd v-if="openAdd" :filesType="filesType" :ID="ID" :mainID="mainID" :parentId="parentId"
					:parentTargetId="parentTargetId" @openNewAdd="openNewAdd" @getDataList="getDataList"></newAdd>
			</el-dialog>
			<!-- <el-dialog title="密集柜详情" v-model="openJoint" width="1000px" append-to-body v-if="openJoint">
				<el-table :data="tableData" style="width: 100%" row-key="id" lazy :load="load" height="650"
					:tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
					<el-table-column prop="name1" label="密集柜编码">
					</el-table-column>
					<el-table-column prop="name2" label="提名/盒名">
					</el-table-column>
					<el-table-column prop="name3" label="档号/盒名">
					</el-table-column>
					<el-table-column prop="name4" label="上架类型">
					</el-table-column>
					<el-table-column prop="name5" label="存址">
					</el-table-column>
				</el-table>
			</el-dialog> -->
		</el-container>
	</el-container>
</template>

<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
import shelves from "./shelves"
import VisualWarehouseDiagram from "./VisualWarehouseDiagram"
import container from "./container"
import lattice from "./lattice"
import newAdd from "./newAdd/newAdd"
import library from "./library"

// import { node } from "@/api/model/systemDeploy/auditAndFlow";
export default {
	name: "storeroom",
	components: {
		shelves,
		container,
		lattice,
		newAdd,
		library,
		VisualWarehouseDiagram
	},

	data() {
		return {
			// 右侧仓库展示
			detailsOpen: true,
			componentKey: 0,
			//父级关联Id
			parentTargetId: '',
			//父级ID
			parentId: '',
			mainID: '',
			ID: '',
			title: '',
			// 新增弹窗
			openAdd: false,
			// 分页
			queryParams: {
				current: 1,
				size: 10,
			},
			total: 0,
			// 库房数据
			storeroomList: [],
			parentDataInfo: [],
			// 所属区域
			houseRegion: '',
			// 菜单加载中
			menuloading: false,
			// 菜单列表
			menuList: [],
			menuProps: {
				label: (data) => {
					return data.name;
				},
			},
			// 柜子查看展示
			openStorey: false,
			// 货架展示
			openShelves: false,
			latticeView: '',
			// 货架
			list: [{
				name: '1',
				name1: '2',
				name2: '3',
				name3: '4',
				name4: '5',
				name5: '6',
				name6: '7',
				name7: '8',
				name8: '9',
				name9: '10',
				name10: '10',
			}, {
				name: '1',
				name1: '2',
				name2: '3',
				name3: '4',
				name4: '5',
				name5: '6',
				name6: '7',
				name7: '8',
				name8: '9',
				name9: '10',
				name10: '10',
			}],
			// 货柜form
			form: {},
			// 货柜编辑弹窗
			open: false,
			// 展示货柜页
			openContainer: false,
			// 档案柜参数
			parameters: [],
			// 密集柜详情
			openJoint: false,

			//查询表单
			searchForm: {},
			// 档案库区分
			filesType: ''
		};
	},
	watch: {
		//   //菜单过滤，顶部搜索框
		//   menuFilterText(val) {
		//     this.$refs.menu.filter(val);
		//   },
	},
	mounted() {
		this.getDataList();
		this.detailsOpen = true;
	},
	methods: {
		returnWarehouseView() {
			this.parentDataInfo = {};
			this.detailsOpen = true;
			this.openShelves = false;
			this.openContainer = false;
			this.openStorey = false;
		},
		// 左侧树结构点击事件
		menuClick(data, node, type) {
			this.detailsOpen = false;
			this.componentKey += 1;
			switch (data.recordHouseInfoType || type) {
				case '1':
					this.parentDataInfo = data;
					this.openShelves = true;
					this.detailsOpen = false;
					this.openContainer = false;
					this.openStorey = false;
					break;
				case '2':
					this.parameters = data;
					this.detailsOpen = false;
					this.openShelves = false;
					this.openContainer = true;
					this.openStorey = false;
					break;
				case '3':
					this.latticeView = data.recordHouseInfoTargetId;
					this.detailsOpen = false;
					this.openShelves = false;
					this.openContainer = false;
					this.openStorey = true;
					break;
			}
		},

		// 新增库房
		addLibrary(type, node, data) {
			if (data.recordHouseInfoType === '1') {
				this.ID = '';
				this.mainID = '';
				this.filesType = '2';
				this.parentId = data.id;
				this.title = '新增档案室';
				this.parentTargetId = data.recordHouseInfoTargetId;
				this.openAdd = true;
			} else if (data === '1') {
				this.ID = '';
				this.mainID = '';
				this.filesType = '1';
				this.title = '新增档案库';
				this.openAdd = true;
			} else if (data.recordHouseInfoType === '2') {
				this.ID = '';
				this.mainID = '';
				this.filesType = '3';
				this.parentId = data.id;
				this.title = '新增档案柜';
				this.parentTargetId = data.recordHouseInfoTargetId;
				this.openAdd = true;
			}
		},

		//右侧点击查看库房详情
		libraryView(data) {
			this.menuClick(data, null, '1');
		},
		// 右侧点击查看档案室详情
		shelvesView(data) {
			this.menuClick(data, null, '2');
		},

		// 查看柜详情
		handleView(data) {
			this.latticeView = data.id;
			this.detailsOpen = false;
			this.openShelves = false;
			this.openContainer = false;
			this.openStorey = true;
		},

		// 查看节
		viewJoint() {
			this.openJoint = true;
		},

		// 库房查询
		getDataList() {
			this.openAdd = false;
			this.menuloading = true;
			//初始化数据列表
			this.menuList = [];
			this.mainID = '';
			storeroomData.treeData().then(res => {
				if (res.code === 200) {
					this.menuList = res.data;
					this.menuloading = false;
				}
			})
		},

		/*
		 * 删除档案库
		 * @author: saya
		 * @date: 2023-03-23 17:46:37
		 */
		async delMenu() {
			let _this = this;
			//获取选中的节点
			let CheckedNodes = _this.$refs.menu.getCheckedNodes();
			if (CheckedNodes.length === 0) {
				_this.$message.warning("请选择需要删除的项");
				return false;
			}
			//删除操作确认
			await _this.$confirm("确认删除已选择的菜单吗？", "提示", {
				type: "warning",
				confirmButtonText: "删除",
				confirmButtonClass: "el-button--danger",
				cancelButtonText: "取消",
				cancelButtonClass: "el-button--primary el-button--large"
			}).then(() => {
				_this.menuloading = true;
                const ids = CheckedNodes.map(item=>item.id);
                storeroomData.deleteInfo({ ids: ids.toString() }).then(res => {
                    if (res.code === 200) {
                        //在列表中移除已删除的菜单项
                        CheckedNodes.forEach((item) => {
                            let node = _this.$refs.menu.getNode(item);
                            //移除菜单项
                            _this.$refs.menu.remove(item);
                            if (node.isCurrent) {
                                //当前删除的是当前编辑的菜单，则清空编辑表单页面
                                _this.returnWarehouseView();
                            }
                        });
                        _this.$message.success("删除成功");
                    } else if(res.code === 500){
                        _this.$message.error(res.msg);
                    }
                }).catch(error => {
                    _this.$Response.errorNotice(error, "删除失败");
                }).finally(()=>{
                    _this.menuloading = false;
                });

			}).catch(() => {
				//error为cancel
			});
		},

		/*
		 * 根据Id获取仓库配置数据
		 * @author: saya
		 */
		queryById(node, data) {
			switch (data.recordHouseInfoType) {
				case '1':
					this.mainID = data.id;
					this.ID = data.recordHouseInfoTargetId;
					this.filesType = data.recordHouseInfoType;
					this.title = '修改档案库';
					this.openAdd = true;
					break;
				case '2':
					this.mainID = data.id;
					this.parentId = data.parentId;
					this.ID = data.recordHouseInfoTargetId;
					this.filesType = data.recordHouseInfoType;
					this.title = '修改档案室';
					this.openAdd = true;
					break;
				case '3':
					this.mainID = data.id;
					this.parentId = data.parentId;
					this.ID = data.recordHouseInfoTargetId;
					this.filesType = data.recordHouseInfoType;
					this.title = '修改档案柜';
					this.openAdd = true;
					break;
			}
		},
		// 关闭新增弹窗
		openNewAdd() {
			this.getDataList();
            this.returnWarehouseView();
			this.openAdd = false;
		}
	}
};
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
	display: flex;
	flex: 1;
	height: 100%;
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	height: 100%;
	padding-right: 24px;
}

.custom-tree-node .label {
	display: flex;
	align-items: center;
	height: 100%;
}

.custom-tree-node .label .el-tag {
	margin-left: 5px;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .do {
	display: inline-block;
}

.time {
	font-size: 13px;
	color: #999;
}

.clearfix:before,
.clearfix:after {
	display: table;
	content: "";
}

.clearfix:after {
	clear: both
}
</style>
