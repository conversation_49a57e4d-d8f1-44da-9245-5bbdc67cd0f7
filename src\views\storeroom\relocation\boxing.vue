
<template>
    <el-container>
        <el-main v-if="openTree" ref="main" v-loading="loading" class="noPadding" style="">
			<div style="margin-bottom: 10px;">
                <el-form ref="formList" :inline="true" :model="form" label-position="left" label-width="auto" class="compact-form">
                    <el-form-item label="档案名称" prop="name" class="form-item-compact">
                        <el-input v-model="form.name" placeholder="请输入档案名称"/>
                    </el-form-item>
                    <el-form-item label="档案号" prop="numFormat" class="form-item-compact">
                        <el-input v-model="form.numFormat" placeholder="请输入档案号"/>
                    </el-form-item>
                    <el-form-item class="form-item-buttons">
                        <el-button type="primary" :icon="Search" @click="() => handleQuery()">查询</el-button>
                        <el-button :icon="RefreshRight" plain @click="() => resetQuery()">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="receiveData" @selection-change="handleSelectionChange" border>
				<el-table-column align="center" min-width="30" type="selection" width="50"/>
				<el-table-column align="center" label="档案名称" prop="name"/>
				<el-table-column align="center" label="档案号" prop="num"/>
				<el-table-column align="center" label="档案年份" prop="year" width="80"/>
				<el-table-column align="center" label="档案月份" prop="month" width="80"/>
				<el-table-column align="center" label="档案创建时间" prop="createDate" width="180">
					<template #default="scope">
						{{
							(scope.row.createDate ?
								moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') :
								undefined) || '--'
						}}
					</template>
				</el-table-column>
				<el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="100px">
					<template #default="scope">
						<el-button link type="primary" @click="collectFile(scope.row)">查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页和按钮区域 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 0 20px;">
                <div></div> <!-- 左侧占位 -->
                <div>
                    <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                        @pagination="paginationing()" />
                </div>
            </div>

            <!-- 底部按钮区域 -->
            <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
                <el-button plain @click="() => cancellation(1)">取消</el-button>
                <el-button v-if="!props.handList.id" type="primary" @click="() => collectRemove()">确定</el-button>
            </div>
            <!-- 查看 -->
			<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="8vh" width="92%">
				<viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
			</el-dialog>
        </el-main>
    </el-container>
</template>

<script setup>
import boxList from '@/api/archive/storeroom/filingBox';
import {defineProps, getCurrentInstance, reactive, ref, toRefs} from 'vue'
import {RefreshRight, Search} from '@element-plus/icons-vue'
import moment from 'moment';

// 查看弹窗
import viewFiles from '../view.vue';

const emit = defineEmits(["childEvent"]);
const props = defineProps({
    handList: {
        type: Array
    },
    clickEvenId: {
        type: Array
    }
})
// 加载
const loading = ref(true)
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    },
    fourConfig: {},
})
const total = ref(0)
const { queryParams } = toRefs(data)
const { proxy } = getCurrentInstance()
// 接收库List
const receiveData = ref([])
// 点击查询列表
const openTree = ref(false)

// 头部查询
const form = ref([])
function handleQuery() {
    let id = '';
    if (props.handList.id) {
        id = props.handList.id
    } else {
        id = props.handList[0].id
    }
    boxList.mainList({
        current: queryParams.value.current,
        size: queryParams.value.size,
        name: form.value.name,
        numFormat: form.value.numFormat,
        'box.id': id
    }).then(res => {
        if (res.code === 200) {
            receiveData.value = res.data.records;
            total.value = res.data.total;
            openTree.value = true;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}
// 重置
function resetQuery() {
    form.value = [];
    getList();
}

// 分页
function paginationing() {
    // 检查是否有搜索条件，如果有则使用搜索，否则使用默认列表
    if (form.value.name || form.value.numFormat) {
        handleQuery();
    } else {
        getList();
    }
}

// 进入默认
// emit("childMove");

// 取消
function cancellation(val) {
    emit("childMove", '', val);
}

// 查看receiveId
const receiveId = ref('')
const title = ref('')
const openView = ref(false)
function collectFile(val) {
    title.value = '查看';
    receiveId.value = val;
    openView.value = true;
}
// 关闭查看
function parentView() {
    openView.value = false;
}

const handData = ref([])
// 选中表格
function handleSelectionChange(val) {
    handData.value = val;
}
// 拆盒时移除档案
function collectRemove() {
    if (handData.value.length > 0) {
        proxy.$confirm('是否要移除档案?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            var idStr = handData.value.map((v) => v.id);
            //拼接的数组字符串，接口传参
            var ids = idStr.join(",");
            boxList.dismantleInfo({
                recordIds: ids
            }).then(res => {
                if (res.code === 200) {
                    proxy.msgSuccess('移除成功');
                    getList();
                }
            }).catch(() => {
                proxy.msgError('移除失败');
            })
        }).catch(() => {
            console.log(111);
        })
    } else {
        proxy.msgError('请先选择需要移除的数据！');
    }
}


// 进入时查询全部
function getList() {
    let id = '';
    if (props.handList.id) {
        id = props.handList.id
    } else {
        id = props.handList[0].id
    }
    boxList.mainList({
        current: queryParams.value.current,
        size: queryParams.value.size,
        'box.id': id
    }).then(res => {
        if (res.code === 200) {
            receiveData.value = res.data.records;
            loading.value = false;
            total.value = res.data.total;
            openTree.value = true;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}


getList();

</script>

<style scoped>
/* 紧凑表单样式 */
.compact-form {
    display: flex;
    align-items: center;
    gap: 0;
}

.form-item-compact {
    margin-right: 10px;
    margin-bottom: 0;
}

.form-item-buttons {
    margin-bottom: 0;
}

/* 覆盖 Element Plus 默认的标签间距 */
:deep(.form-item-compact .el-form-item__label-wrap) {
    margin-right: 0 !important;
}

:deep(.form-item-buttons .el-form-item__label-wrap) {
    margin-right: 0 !important;
}
</style>
