import objectUtils from '@/utils/objectUtils';
import { ElMessage } from 'element-plus'

export default {
	
    /*
     *@functionName: 成功消息气泡提示
     *@params1: data
     *@params2: msg
     *@description:
     *@author: 路正宁
     *@date: 2023-03-21 13:40:31
    */
	successNotice: function (data,msg) {
        if(objectUtils.isEmpty(data)){
            ElMessage.success(msg);
            return false;
        }
		if(objectUtils.isNotEmpty(data.msg)){
            ElMessage.success(data.msg)
            return true;
        }else{
            ElMessage.success(msg)
            return false;
        }
        
	},
    /*
     *@functionName: 错误消息气泡提示
     *@params1: data
     *@params2: msg
     *@description:
     *@author: 路正宁
     *@date: 2023-03-21 13:40:24
    */
    errorNotice: function (data,msg) {
        if(objectUtils.isEmpty(data)){
            ElMessage.error(msg);
            return false;
        }
		if(objectUtils.isNotEmpty(data.msg)){
            ElMessage.error(data.msg)
            return true;
        }else{
            ElMessage.error(msg)
            return false;
        }
	},
}
