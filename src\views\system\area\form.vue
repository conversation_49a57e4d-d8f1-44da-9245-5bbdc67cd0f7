<template>
  <el-dialog
    :title="titleName"
    v-model="visible"
    :width="500"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      v-loading="dialogLoading"
      :model="form"
      :rules="rules"
      :disabled="mode == 'show'"
      ref="dialogForm"
      label-width="100px"
    >
      <el-form-item label="上级区域" prop="name">
        <el-input v-model="form.parent.name" clearable :disabled="true"></el-input>
      </el-form-item>
      <el-form-item
        label="区域名称"
        prop="name"
        :rules="[{ required: true, message: '区域名称不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.name" placeholder="请输入区域名称" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="区域编码"
        prop="code"
        :rules="[{ required: true, message: '区域编码', trigger: 'blur' }]"
      >
        <el-input v-model="form.code" placeholder="请输入区域编码" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="submitVisible"
        type="primary"
        :loading="isSubmiting"
        @click="submit()"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<script>
export default {
  props: {
    //回调函数
    callback: { type: Function },
  },
  data() {
    return {
      //表单类型
      mode: "add",
      //表单标题
      titleName: "",
      //是否显示或隐藏表单弹框
      visible: false,
      //提交中
      isSubmiting: false,
      //弹框加载中
      dialogLoading: false,
      //是否隐藏提交按钮
      submitVisible: false,
      //表单数据
      form: {},
      //所需数据选项
      groups: [],
      groupsProps: {
        value: "id",
        emitPath: false,
        checkStrictly: true,
      },
    };
  },
  mounted() {},
  methods: {
    /*
     * 添加视图
     * @author: 路正宁
     * @date: 2023-03-24 13:20:15
     */
    addView(parent) {
      //设置标题
      this.titleName = "添加";
      this.mode = "add";
      this.form = {
        parent: {
          id: "",
        },
      };
      this.form.parent = parent;
      //显示表单
      this.visible = true;
      //显示提交按钮
      this.submitVisible = true;
    },
    /*
     * 编辑视图
     * @author: 路正宁
     * @date: 2023-03-24 13:20:35
     */
    async editView(formData) {
      //设置标题
      this.titleName = "编辑";
      this.mode = "edit";
      //显示表单
      this.visible = true;
      //显示提交按钮
      this.submitVisible = true;
      //释放提交等待
      this.isSubmiting = false;
      //页面加载中
      this.dialogLoading = true;
      this.form = formData;
      //重新查询
		var res = await this.$API.sysAreaService.queryById(formData.id);
      if (res.code == 200) {
        //设置表单数据
        this.form.parent = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
        //锁定提交按钮
        this.isSubmiting = true;
      }
      //释放页面加载中
      this.dialogLoading = false;
    },
    /*
     * 查看视图
     * @author: 路正宁
     * @date: 2023-03-24 13:21:14
     */
    async view(formData) {
      //设置标题
      this.titleName = "查看";
      this.mode = "view";
      //显示表单
      this.visible = true;
      //隐藏提交按钮
      this.submitVisible = false;
      //页面加载中
      this.dialogLoading = true;
      //设置表单数据
      this.form = formData;
      //重新查询
      var res = await this.$API.sysAreaService.queryById(formData.parent.id);
      if (res.code == 200) {
        //设置表单数据
        this.form.parent = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
        //锁定提交按钮
        this.isSubmiting = true;
      }
      //释放页面加载中
      this.dialogLoading = false;
    },
    /*
     * 表单提交
     * @author: 路正宁
     * @date: 2023-03-24 14:11:20
     */
    async submit() {
      //表单校验
      var valid = await this.$refs.dialogForm.validate().catch(() => {});
      if (!valid) {
        return false;
      }
      //锁定提交按钮
      this.isSubmiting = true;
      var res = await this.$API.sysAreaService.save(this.form);
      if (res.code == 200) {
        //关闭页面
        this.visible = false;
        this.$message.success("操作成功");
        //回调函数
        this.callback(res.data, this.mode);
      } else {
        this.$Response.errorNotice(res, "保存失败");
      }
      //释放提交按钮
      this.isSubmiting = false;
    },
  },
};
</script>

<style></style>
