import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/housenum/erpWarehouseNumberHandledBySet/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/housenum/erpWarehouseNumberHandledBySet/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/housenum/erpWarehouseNumberHandledBySet/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/housenum/erpWarehouseNumberHandledBySet/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/housenum/erpWarehouseNumberHandledBySet/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/housenum/erpWarehouseNumberHandledBySet/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/housenum/erpWarehouseNumberHandledBySet/import',
      data
    )
  }
}
