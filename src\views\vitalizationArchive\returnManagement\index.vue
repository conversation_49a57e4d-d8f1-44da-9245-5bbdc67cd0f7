<template>
	<el-container>
		<el-main class="noPadding">
			<el-card body-style="padding-top: 0;padding-bottom: 0;" class="box-card" style="margin:10px;">
				<el-tabs v-model="activeName" v-loading="loading" class="demo-tabs" @tab-change="handleClick">
					<el-tab-pane label="未归还" name="1">
						<div class="card-header"
							 style="display: flex;justify-content:flex-end;align-items:center;margin-bottom: 10px;">
							<div>
                                <span style="margin-right: 15px;" @click="RefreshList">
                                    <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                            <Refresh/>
                                        </el-icon>
                                    </el-tooltip>
                                </span>
								<span @click="screen">
                                    <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC"
												 style="cursor:pointer;"><el-icon-full-screen/></el-icon>
                                    </el-tooltip>
                                </span>
							</div>
						</div>
						<el-table :data="receiveData" border @selection-change="handleSelectionChange">
							<el-table-column align="center" fixed="left" min-width="30" type="selection" width="50" />
							<el-table-column align="center" label="借阅人" prop="borrowApply.name" width="280"/>
							<el-table-column align="center" label="借阅部门" prop="borrowApply.sysOffice.name"
											 width="250"/>
							<el-table-column align="center" label="借阅起始日期" prop="borrowStartTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowStartTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="借阅归还日期" prop="borrowEndTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowEndTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="是否包含水印" min-width="100" prop="borrowIsWatermark">
								<template #default="scope">
									<span>{{ scope.row.borrowIsWatermark == '1' ? '是' : '否' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="审核状态" prop="borrowAuditStatus">
								<template #default="scope">
									<span>{{ scope.row.borrowAuditStatus == '1' ? '未审核' : '已审核' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="借阅理由" min-width="180" prop="borrowRemark" show-overflow-tooltip />
							<el-table-column align="center" fixed="right" label="操作" min-width="220">
								<template #default="scope">
									<el-button icon="View" link size="small" type="primary" @click="collectFile(scope.row)">查看</el-button>
									<el-button icon="FolderChecked" link size="small" type="primary" @click="returnFile(scope.row)">归还</el-button>
									<el-button icon="Postcard" link size="small" type="primary" @click="renewFile(scope.row)">续借</el-button>
								</template>
							</el-table-column>
						</el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 22px" @pagination="getList()"/>
						</div>
					</el-tab-pane>

					<el-tab-pane label="已归还" name="2">
						<div class="card-header"
							 style="display: flex;justify-content:flex-end;align-items:center;margin-bottom: 10px;">
							<div>
                                <span style="margin-right: 15px;" @click="RefreshList">
                                    <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                            <Refresh/>
                                        </el-icon>
                                    </el-tooltip>
                                </span>
								<span @click="screen">
                                    <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC"
												 style="cursor:pointer;"><el-icon-full-screen/></el-icon>
                                    </el-tooltip>
                                </span>
							</div>
						</div>
						<el-table :data="receiveStop" border @selection-change="handleSelectionChange">
							<el-table-column align="center" fixed="left" min-width="30" type="selection" width="50"/>
							<el-table-column align="center" label="借阅人" prop="borrowApply.name" width="280"/>
							<el-table-column align="center" label="借阅部门" prop="borrowApply.sysOffice.name"
											 width="250"/>
							<el-table-column align="center" label="借阅起始日期" prop="borrowStartTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowStartTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="借阅归还日期" prop="borrowEndTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowEndTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="是否包含水印" min-width="100" prop="borrowIsWatermark">
								<template #default="scope">
									<span>{{ scope.row.borrowIsWatermark == '1' ? '是' : '否' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="审核状态" prop="borrowAuditStatus">
								<template #default="scope">
									<span>{{ scope.row.borrowAuditStatus == '1' ? '未审核' : '已审核' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="借阅理由" min-width="180" prop="borrowRemark" show-overflow-tooltip />
							<el-table-column align="center" fixed="right" label="操作" min-width="220">
								<template #default="scope">
									<el-button icon="View" link size="small" type="primary" @click="collectFile(scope.row, '1')">查看</el-button>
									<el-button icon="Postcard" link size="small" type="primary" @click="renewFile(scope.row)">续借</el-button>
								</template>
							</el-table-column>
						</el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 22px" @pagination="getList()"/>
						</div>
					</el-tab-pane>
					<el-tab-pane label="超期" name="3">
						<div class="card-header"
							 style="display: flex;justify-content:flex-end;align-items:center;margin-bottom: 10px;">
							<div>
                                <span style="margin-right: 15px;" @click="RefreshList">
                                    <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                            <Refresh/>
                                        </el-icon>
                                    </el-tooltip>
                                </span>
								<span @click="screen">
                                    <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC"
												 style="cursor:pointer;"><el-icon-full-screen/></el-icon>
                                    </el-tooltip>
                                </span>
							</div>
						</div>
						<el-table :data="receiveOverdue" border @selection-change="handleSelectionChange">
							<el-table-column align="center" fixed="left" min-width="30" type="selection" width="50"/>
							<el-table-column align="center" label="借阅人" prop="borrowApply.name" width="200"/>
							<el-table-column align="center" label="借阅部门" prop="borrowApply.sysOffice.name"/>
							<el-table-column align="center" label="借阅起始日期" prop="borrowStartTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowStartTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="借阅归还日期" prop="borrowEndTime" width="180">
								<template #default="scope">
                                    <span>{{
											moment(scope.row.borrowEndTime).format("YYYY-MM-DD HH:mm:ss")
										}}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="是否包含水印" min-width="100" prop="borrowIsWatermark">
								<template #default="scope">
									<span>{{ scope.row.borrowIsWatermark == '1' ? '是' : '否' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="审核状态" prop="borrowAuditStatus" width="100">
								<template #default="scope">
									<span>{{ scope.row.borrowAuditStatus == '1' ? '未审核' : '已审核' }}</span>
								</template>
							</el-table-column>
							<el-table-column align="center" label="借阅理由" min-width="180" prop="borrowRemark" show-overflow-tooltip>
								<template #default="scope">
									<el-tooltip
										:content="scope.row.borrowRemark"
										class="box-item"
										effect="dark"
										placement="top"
									>
										{{ scope.row.borrowRemark }}
									</el-tooltip>
								</template>
							</el-table-column>
							<el-table-column align="center" fixed="right" label="操作" min-width="260">
								<template #default="scope">
									<el-button icon="View" link size="small" type="primary" @click="collectFile(scope.row)">查看</el-button>
									<el-button icon="FolderChecked" link size="small" type="primary" @click="returnFile(scope.row)">归还</el-button>
									<el-button icon="AlarmClock" link size="small" type="primary" @click="repaymentFile(scope.row)">催还</el-button>
									<el-button icon="Postcard" link size="small" type="primary" @click="renewFile(scope.row)">续借</el-button>
								</template>
							</el-table-column>
						</el-table>
						<div style="float: right;">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
										style="padding: 22px" @pagination="getList()"/>
						</div>
					</el-tab-pane>
				</el-tabs>
			</el-card>
		</el-main>
		<!-- 审批 -->
		<el-dialog v-if="open" v-model="open" :title="title" append-to-body width="1300px">
			<examine :receiveId="receiveId" @childMove="parentView"></examine>
		</el-dialog>
	</el-container>
</template>
<script setup>
import {Refresh} from '@element-plus/icons-vue'
import vitalizationArchiveList from '@/api/archive/vitalizationArchive'
import examine from './examine.vue';
import tool from '@/utils/tool';
import moment from "moment";
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import {useRouter} from 'vue-router';

const {proxy} = getCurrentInstance()
const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
})
const total = ref(0)
const {queryParams} = toRefs(data)
const activeName = ref('1')

// 是否显示审批
const open = ref(false)
const title = ref('')
const receiveId = ref('')

// 审批
function collectFile(val) {
	receiveId.value = val;
	title.value = '查看';

	open.value = true;
}

// 关闭审批
function parentView() {
	open.value = false;
	getList();
}

// 切换tab时
const handleClick = (tabName) => {
	activeName.value = tabName;
	getList();
}

// 刷新列表
const RefreshList = () => {
	getList();
};

//全屏
function screen() {
	var element = document.documentElement;
	tool.screen(element);
}

// 列表
const receiveData = ref([]) // 未归还
const receiveStop = ref([]) //已归还
const receiveOverdue = ref([]) //超期
const loading = ref(true)
const handList = ref([])

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

onMounted(() => {
	const router = useRouter();
	activeName.value = router.currentRoute.value.query.activeName ? router.currentRoute.value.query.activeName : "1";
	getList()
});

// 进入时查询全部
function getList() {
	vitalizationArchiveList.borrowList({
		current: queryParams.value.current,
		size: queryParams.value.size,
		borrowStatus: activeName.value == '3' ? '1' : activeName.value,
		borrowExtended: activeName.value == '3' ? true : null,
		borrowAuditStatus: '7'
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				receiveData.value = res.data.records;
			} else if (activeName.value == '2') {
				receiveStop.value = res.data.records;
			} else if (activeName.value == '3') {
				receiveOverdue.value = res.data.records;
			}
			total.value = res.data.total;
			loading.value = false;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 归还
function returnFile(val) {
	proxy.$confirm('是否需要归还！', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		vitalizationArchiveList.save({
			borrowStatus: '2',
			id: val.id,
		}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('归还成功');
				getList();
			}
		}).catch(() => {
			proxy.msgError('归还失败');
		})
	}).catch(() => {
		console.log(111);
	})
}

// 续借renewFile
function renewFile(val) {
	proxy.$prompt('请输入续借天数:', '续借', {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		inputPattern: /^[1-9]*[1-9][0-9]*$/,
		inputErrorMessage: '请输入数字或者非0的数字',
	}).then((value) => {
		vitalizationArchiveList.borrowAuditContent({
			'borrowAuditContent': JSON.stringify({
				borrowEndTime: value.value
			}),
			id: val.id,
		}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('续借成功');
				getList();
			}
		}).catch(() => {
			proxy.msgError('续借失败');
		})
	}).catch(() => {
		console.log(111);
	})
}

// 催还
function repaymentFile(val) {
	proxy.$confirm('确认催还并发送催还通知!', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		vitalizationArchiveList.borrowOfReturn({
			borrowId: val.id,
		}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('催还成功');
				getList();
			}
		}).catch(() => {
			proxy.msgError('催还失败');
		})
	}).catch(() => {
		console.log(111);
	})
}
</script>
<style setup>
.fileUrl {
	margin-bottom: 10px;
	cursor: pointer;
}

.fileUrl:hover {
	color: #2a76f8;
}

.el-input--small {
	margin-left: 30%;
	width: 150px;
	position: relative;
	bottom: 35px;
}

.el-message-box__input {
	height: 20px;
}
</style>
