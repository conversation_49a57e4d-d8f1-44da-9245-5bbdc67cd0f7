import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/storage/erpGoodsPosition/list',params)
  },
  delete: function (ids) {
    return http.delete('/erp/storage/erpGoodsPosition/delete',ids)
  },
  save: function (params) {
    return http.post('/erp/storage/erpGoodsPosition/save',params)
  },
  detail: function (id) {
    return http.get('/erp/storage/erpGoodsPosition/queryById',id)
  },
  logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
}