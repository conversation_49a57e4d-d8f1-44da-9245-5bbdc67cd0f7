<template>
	<el-container>
		<el-header height="auto" style="background: none;border-bottom: none;padding: 10px 10px 5px 10px">
			<el-card style="height: 100%;width: 100%">
				<el-input v-model="dataNameSearch" class="w-50 m-2" placeholder="请输入需要查询的名称"
						  style="width: 521px;" @keydown.enter="selectList"/>
				<el-button icon="Search" style="margin-left: 20px;" type="primary" @click="selectList">
					查询
				</el-button>
				<el-button icon="RefreshRight" plain @click="() => {dataNameSearch = '';selectList();}">
					重置
				</el-button>
			</el-card>
		</el-header>
		<el-main style="padding: 5px 10px 10px 10px">
			<el-card :body-style="{height: '100%',width: '100%',padding: '20px 20px 0 20px'}"
					 style="height: 100%;width: 100%">
				<el-container>
					<el-header height="auto" style="background: none;border-bottom: none;padding: 0 0 15px 0">
						<el-button icon="Plus" plain type="primary" @click="openView">
							新增
						</el-button>
					</el-header>
					<el-main style="padding: 0">
						<el-table v-loading="loading" :data="dataList" border style="height: 100%;width: 100%">
							<el-table-column align="center" label="序号" prop="sort" width="80">
								<template #default="scope">
									{{ scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="印章名称" min-width="182" prop="name"/>
							<el-table-column align="center" label="印章类型" prop="type" width="182"/>
							<el-table-column align="center" label="印章预览" prop="signetUrl" width="92">
								<template #default="scope">
									<el-image
										:max-scale="7"
										:preview-src-list="viewImgList"
										:src="scope.row.signetUrl"
										fit="fill"
										style="width: 50px; height: 50px"
										@click="viewImgList = [scope.row.signetUrl]"
									>
										<template #error>
											<div class="image-slot">
												<el-icon>
													<Picture/>
												</el-icon>
											</div>
										</template>
									</el-image>
								</template>
							</el-table-column>
							<el-table-column align="center" label="印章所属门类" min-width="420" prop="categoryId">
								<template #default="scope">
									<el-tag v-for="(item,index) in matchNameById(scope.row.categoryId, categoryList)"
											v-if="scope.row.categoryId" :key="index" effect="light" round
											type="primary">
										{{ item }}
									</el-tag>
									<el-text v-else type="primary">未设置类型</el-text>
								</template>
							</el-table-column>
							<el-table-column align="center" label="印章位置" prop="signetPosition" width="82">
								<template #default="scope">
									<el-text v-if="scope.row.signetPosition === '1'">左上</el-text>
									<el-text v-if="scope.row.signetPosition === '2'">右上</el-text>
									<el-text v-if="scope.row.signetPosition === '3'">中下</el-text>
									<el-text v-if="scope.row.signetPosition === '4'">左下</el-text>
									<el-text v-if="scope.row.signetPosition === '5'">右下</el-text>
								</template>
							</el-table-column>
							<el-table-column align="center" label="印章备注" prop="remark" width="200">
								<template #default="scope">
									<el-text v-if="scope.row.remark" truncated>{{ scope.row.remark }}</el-text>
									<el-text v-else>未设置备注</el-text>
								</template>
							</el-table-column>
							<el-table-column align="center"
											 label="印章创建时间"
											 prop="createDate"
											 width="172">
								<template #default="scope">
									{{ moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
								</template>
							</el-table-column>
							<el-table-column align="center"
											 class-name="small-padding fixed-width"
											 fixed="right"
											 label="操作"
											 width="172">
								<template #default="scope">
									<el-button icon="Edit" link type="primary" @click="editInfo(scope.row)">
										编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="deleteInfo(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:total="total" style="padding: 0" @pagination="selectList()"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-if="open" v-model="open" :title="title" append-to-body width="30%">
			<el-form ref="formRef" v-loading="loading" :model="form" :rules="rules" label-width="auto"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="印章名称" prop="name">
					<el-input v-model="form.name" placeholder="请输入印章名称"/>
				</el-form-item>
				<el-form-item label="印章类型" prop="type">
					<el-input v-model="form.type" placeholder="请输入印章类型"/>
				</el-form-item>
				<el-form-item label="印章图片" prop="signetUrl">
					<el-upload
						:accept="accept"
						:action="uploadUrl"
						:headers='headers'
						:on-success="(res, file, fileList) => form.signetUrl = res.data.url"
						:show-file-list="false"
						class="avatar-uploader">
						<img v-if="form.signetUrl" :src="form.signetUrl" alt="印章图片"
							 style="height: 30px;width: 30px;">
						<el-icon v-else class="avatar-uploader-icon">
							<Plus/>
						</el-icon>
					</el-upload>
				</el-form-item>
				<el-form-item label="印章所属门类" prop="categoryId">
					<el-tree-select v-model="form.categoryId"
									ref="categoryTree"
									:data="chooseCategoryList"
									multiple
									:render-after-expand="false"
									clearable
									collapse-tags
									collapse-tags-tooltip
									highlight-current
									show-checkbox
									:props="{ value: 'id', label: 'name', disabled: 'disabled', check: 'check' }"
									node-key="id"
									@check="checkNode"
									style="width: 100%">
						<template #default="{ data }">
							<el-text v-if="!data.disabled" style="float: left;">{{ data.name }}</el-text>
							<el-tooltip v-else class="box-item" content="已有对应门类" effect="dark" placement="right">
								<el-text style="float: left;" type="info">{{ data.name }}</el-text>
							</el-tooltip>
							<el-tooltip :content="matchInfoById(data.recordGroupId)" class="box-item" effect="dark"
										placement="right">
								<el-text style="float: right;" type="primary">所属全宗</el-text>
							</el-tooltip>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item label="印章位置" prop="signetPosition">
					<el-select v-model="form.signetPosition" placeholder="请选择印章位置"
							   style="width: 100%;">
						<el-option label="左上" value="1"/>
						<el-option label="右上" value="2"/>
						<el-option label="中下" value="3"/>
						<el-option label="左下" value="4"/>
						<el-option label="右下" value="5"/>
					</el-select>
				</el-form-item>
				<el-form-item label="印章备注" prop="remark">
					<el-input v-model="form.remark" :autosize="{minRows: 6}" clearable maxlength="500"
							  placeholder="请输入印章备注"
							  show-word-limit type="textarea"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitData">确 定</el-button>
					<el-button @click="() => open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue';
import signetManagement from '@/api/archive/signetManagement';
import {Plus} from '@element-plus/icons-vue';
import moment from "moment/moment";
import tool from "@/utils/tool";
import category from "@/api/archive/categoryManagement/category";
import completeManagement from "@/api/archive/systemConfiguration/completeManagement";

const {proxy} = getCurrentInstance();
const data = reactive({
	form: {
		id: '',
		name: '',
		type: '',
		signetUrl: '',
		signetPosition: '',
		categoryId: '',
		remark: ''
	},
	queryParams: {
		current: 1,
		size: 10,
	}
});
// 表单校验
const rules = reactive({
	name: [{required: true, message: '请输入印章名称', trigger: 'blur'},],
	type: [{required: true, message: '请输入印章类型', trigger: 'blur'},],
	signetUrl: [{required: true, message: '请上传印章图片', trigger: 'change'},],
	signetPosition: [{required: true, message: '请选择印章位置', trigger: 'change'},],
	categoryId: [{required: true, message: '请选择印章所属门类', trigger: 'change'},],
	remark: [{required: true, message: '请输入印章备注', trigger: 'blur'},],
})
const dataNameSearch = ref("");
const loading = ref(false);
const total = ref(0);
const {queryParams, fourConfig, form} = toRefs(data);
// 数据集合
const dataList = ref([]);
const viewImgList = ref([]);
// 门类List
const categoryList = ref([]);
// 全宗List
const respectList = ref([]);
//弹窗状态
const open = ref(false);
const title = ref("");
const formRef = ref(null);
const categoryTree = ref(null);
const accept = ref("image/gif, image/jpeg, image/png, image/jpg");
const uploadUrl = process.env.VUE_APP_API_UPLOAD;
const headers = {
	Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
	ContentType: 'multipart/form-data',
	clientType: "PC"
};
const chooseCategory = ref([]);
const chooseCategoryList = ref([]);
const selectedValues = ref([]);

onBeforeMount(() => {
	selectList();
});

//查询数据集合
function selectList() {
	loading.value = true
	signetManagement.getList({
		name: dataNameSearch.value,
		current: queryParams.value.current,
		size: queryParams.value.size
	}).then(res => {
		if (res.code === 200) {
			dataList.value = res.data.records;
			total.value = res.data.total;
			loading.value = false;
			getRespectList();
			getCategoryList();
			getAllCategoryList();
		}
	})
}

//查询已经选中的门类
function getAllCategoryList() {
	signetManagement.getList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			chooseCategory.value = res.data.records.flatMap(item => item.categoryId.split(","))
		}
	})
}

//查询匹配所有门类
function getUserChooseCategoryList(categoryId) {
	chooseCategoryList.value = categoryList.value;

	if (categoryId) {
		matchCategoryById(chooseCategoryList.value, chooseCategory.value.filter(item => !categoryId.split(",").includes(item)));
	} else {
		matchCategoryById(chooseCategoryList.value, chooseCategory.value);
	}
}

function checkNode(nowData, checkList) {
	categoryTree.value.setCheckedNodes(checkList.checkedNodes.concat(checkList.halfCheckedNodes));
	console.log(nowData);
	console.log(checkList);
	console.log(checkList.checkedNodes.concat(checkList.halfCheckedNodes));
}

//处理门类数据
function matchCategoryById(dataList, chooseIdList) {
	console.log(chooseIdList, 'chooseIdList');
	dataList.forEach(category => {
		if (chooseIdList.includes(category.id)) {
			category.disabled = true;
		}

		if (category.children && category.children.length > 0) {
			matchCategoryById(category.children, chooseIdList)
		}
	})
}

//数据提交
function submitData() {
	formRef.value.validate(valid => {
		if (valid) {
			signetManagement.save({
				id: form.value.id,
				name: form.value.name,
				type: form.value.type,
				signetUrl: form.value.signetUrl,
				signetPosition: form.value.signetPosition,
				categoryId: categoryTree.value.getCheckedKeys().concat(categoryTree.value.getHalfCheckedKeys()).join(","),
				remark: form.value.remark
			}).then((res) => {
				if (res.code === 200) {
					proxy.msgSuccess("操作成功");
					open.value = false;
					selectList();
					formRef.value.resetFields();
				} else {
					proxy.msgError(res.msg);
				}
			}).catch((err) => {
				proxy.msgError("操作失败");
			});
		}
	});
}

//修改数据
function editInfo(data) {
	form.value.id = data.id;
	form.value.name = data.name;
	form.value.type = data.type;
	form.value.signetUrl = data.signetUrl;
	form.value.signetPosition = data.signetPosition;
	form.value.categoryId = data.categoryId.split(",");
	form.value.remark = data.remark;
	getUserChooseCategoryList(data.categoryId);
	open.value = true;
}

//删除数据
function deleteInfo(data) {
	signetManagement.delete({
		ids: data.id
	}).then((res) => {
		if (res.code === 200) {
			proxy.msgSuccess("删除成功");
			selectList();
		} else {
			proxy.msgError("删除失败");
		}
	}).catch(error => {
		proxy.msgError("删除失败");
	});
}

//根据id匹配全宗名称
function matchInfoById(id) {
	let groupName = '';
	respectList.value.forEach(item => {
		if (item.id === id) {
			groupName = item.recordGroupName;
		}
	})
	return groupName;
}

//根据名称匹配
function matchNameById(idString, dataList) {
	let idList = [];
	dataList.forEach(item => {
		idString.split(',').forEach(id => {
			if (item.id === id) {
				idList.push(item.name);
			}
		})

		if (item.children && item.children.length > 0) {
			idList = [...matchNameById(idString, item.children), ...idList]
		}
	})
	return idList;
}

// 查询全宗列表
function getRespectList() {
	completeManagement.getList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			respectList.value = res.data.records;
		}
	})
}

// 查询门类列表
function getCategoryList() {
	category.list({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			categoryList.value = res.data
		}
	})
}

//新建
function openView() {
	data.form = {
		id: '',
		name: '',
		type: '',
		signetUrl: '',
		signetPosition: '',
		categoryId: '',
		remark: ''
	};
	getUserChooseCategoryList(null);
	open.value = true;
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__cell {
	position: static !important;
}
</style>
