import http from "@/utils/request"

/*
 *@description: 系统菜单
 *@author: 路正宁
 *@date: 2023-03-17 11:31:48
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/menu/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/sys/menu/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/menu/queryById',
      {id: id}
    )
  },

  treeData () {
    return http.get(
      '/sys/menu/treeData',
    )
  },
  /*
   * 修改排序
   * @author: 路正宁
   * @date: 2023-03-23 09:52:48
  */
  updateSort (draggingNodeId,dropNodeId,dropType,sort) {
    return http.get(
      '/sys/menu/updateSort',
      {draggingNodeId: draggingNodeId,dropNodeId:dropNodeId,dropType:dropType,sort:sort}
    )
  }
}
