/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-06 15:21:34
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-05-06 15:24:42
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\utils\dictLabel.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default function selectDictLabel(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].value == ('' + value)) {
			actions.push(datas[key].name);
			return true;
		}
	})
	return actions.join('');
}