<template>
	<el-container>
		<el-aside style="border-right: 0;padding: 10px 0 10px 10px;background-color: #fff" width="21%">
			<el-card :body-style="{ padding: '8px 0 0 0', height: '100%', width: '100%' }"
					 style="height: 100%;width: 100%;">
				<tree @clickChild="menuClick"></tree>
			</el-card>
		</el-aside>
		<el-container style="background-color: rgb(255, 255, 255);margin-top: -5px;">
			<el-main ref="main">
				<el-card body-style="padding-bottom: 0;" class="box-card">
					<el-tabs v-model="activeName" @tab-change="handleClick">
						<el-tab-pane label="人员进出记录" name="1">
							<el-button icon="Plus" plain size="default" type="primary"
									   @click="personelAdd(personaddform)">新增
							</el-button>
							<el-table v-loading="loading" :data="personnelList"
									  border style="margin-top:10px;" @selection-change="handleSelectionChange">
								<el-table-column align="center" label="序号" prop="sort" width="80">
									<template #default="scope">
										<span>{{ scope.$index + 1 }}</span>
									</template>
								</el-table-column>
								<el-table-column align="center" label="进出人员" prop="person" />
								<el-table-column align="center" label="事由" prop="houseEnterContent" />
								<el-table-column align="center" label="档案库房" min-width="130" prop="houseInfo.houseName" />
								<el-table-column align="center" label="档案室" min-width="150" prop="roomInfo.roomName" />
								<el-table-column align="center" label="进库时间" min-width="160" prop="houseEnterTime" />
								<el-table-column align="center" label="出库时间" min-width="160" prop="houseOutTime" />
								<el-table-column align="center" label="操作" min-width="100px">
									<template #default="scope">
										<el-button icon="Delete" link size="small" type="danger" @click="deletePerson(scope.row)">删除
										</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div style="float: right;">
								<pagination v-model:limit="perqueryParams.size" v-model:page="perqueryParams.current"
											:total="pertotal"
											style="padding: 22px" @pagination="getList"/>
							</div>
						</el-tab-pane>
						<el-tab-pane label="温湿度记录" name="2">
							<el-button icon="Plus" plain size="default" type="primary" @click="humidityAdd(humitureform)">新增</el-button>
							<el-table v-loading="loading" :data="humidityList"
									  border style="margin-top:10px;" @selection-change="handleSelectionChange">
								<el-table-column align="center" label="检测日期" min-width="120" prop="thermographyDate"/>
								<el-table-column align="center" label="检测时间" min-width="100" prop="thermographyTime"/>
								<el-table-column align="center" label="档案库房" min-width="120" prop="houseInfo.houseName"/>
								<el-table-column align="center" label="档案室" min-width="150" prop="roomInfo.roomName" show-overflow-tooltip/>
								<el-table-column align="center" label="检测人" min-width="150" prop="checkPerson.name" show-overflow-tooltip/>
								<el-table-column align="center" label="温度" min-width="100" prop="temperature" show-overflow-tooltip>
									<template #default="scope">
										{{ scope.row.temperature }}°C
									</template>
								</el-table-column>
								<el-table-column align="center" label="湿度" min-width="100" prop="humidness" show-overflow-tooltip>
									<template #default="scope">
										{{ scope.row.humidness }}%RH
									</template>
								</el-table-column>
								<el-table-column align="center" label="检测情况" min-width="120" prop="thermographyContent" show-overflow-tooltip/>
								<el-table-column align="center" fixed="right" label="操作" width="100px">
									<template #default="scope">
										<el-button icon="Delete" link size="small" type="danger" @click="deleteHumidity(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div style="float: right;">
								<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
											style="padding: 22px" @pagination="getList2"/>
							</div>
						</el-tab-pane>
					</el-tabs>
				</el-card>
			</el-main>
		</el-container>
	</el-container>
	<!-- 人员增加弹框 -->
	<el-dialog v-model="dialogFormVisible" title="新增进出记录" width="30%">
		<el-form ref="personaddform" :model="personaddForm" :rules="personrules" label-position="right" label-width="80px"
			size="normal">
			<div style="margin-left:4%;margin-top: 1%;">
				<el-form-item label="档案库房" prop="houseInfo">
					<el-select v-model="personaddForm.houseInfo" class="form_225" clearable
						placeholder="请选择档案库房" style="width:90% !important;" @change="handlerHouseChange">
						<el-option v-for="item in warehouseList" :key="item.recordHouseInfoTargetId" :label="item.name"
							:value="item.recordHouseInfoTargetId" />
					</el-select>
				</el-form-item>
				<el-form-item label="档案室" prop="roomInfo">
					<el-select v-model="personaddForm.roomInfo" class="form_225" clearable placeholder="请选择档案室"
						style="width:90% !important;">
						<el-option v-for="item in archivesList" :key="item.recordHouseInfoTargetId" :label="item.name"
							:value="item.recordHouseInfoTargetId" />
					</el-select>
				</el-form-item>
				<el-form-item label="进出人员" prop="person">
					<el-input v-model="personaddForm.person" clearable placeholder="请输入进出人员" size="normal"
						style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="事由" prop="houseEnterContent">
					<el-input v-model="personaddForm.houseEnterContent" clearable placeholder="请输入事由" size="normal"
						style="width:90%;"></el-input>
				</el-form-item>
				<el-form-item label="进库时间" prop="houseEnterTime">
					<el-date-picker v-model="personaddForm.houseEnterTime" placeholder="请选择进库时间" style="width:90%;"
						type="datetime" value-format="YYYY-MM-DD HH:mm:ss" @change="handleEnterTimeChange"></el-date-picker>
				</el-form-item>
				<el-form-item label="出库时间" prop="houseOutTime">
					<el-date-picker v-model="personaddForm.houseOutTime" placeholder="请选择出库时间" style="width:90%;"
						type="datetime" value-format="YYYY-MM-DD HH:mm:ss" @change="handleOutTimeChange"></el-date-picker>
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="dialogFormVisible = false">取消</el-button>
			<el-button type="primary" @click="personAdd(personaddform)">确定</el-button>
		</div>
	</el-dialog>
	<!-- 温湿度增加弹框 -->
	<el-dialog v-model="dialogFormVisible2" title="新增温湿度记录" width="30%">
		<el-form ref="humitureform" :model="humitureaddForm" :rules="humitureRules" label-position="right"
				 label-width="80px" size="normal">
			<div style="margin-left:4%;margin-top:1%;">
				<el-form-item label="档案库房" prop="houseInfo">
					<el-select v-model="humitureaddForm.houseInfo" class="form_225"
							   clearable
							   placeholder="请选择档案库房" style="width:90% !important;" @change="handlerHouseChange">
						<el-option v-for="item in warehouseList" :key="item.recordHouseInfoTargetId"
								   :label="item.name"
								   :value="item.recordHouseInfoTargetId"/>
					</el-select>
				</el-form-item>
				<el-form-item label="档案室" prop="roomInfo">
					<el-select v-model="humitureaddForm.roomInfo" class="form_225" clearable
						placeholder="请选择档案室" style="width:90% !important;">
						<el-option v-for="item in archivesList" :key="item.recordHouseInfoTargetId" :label="item.name"
							:value="item.recordHouseInfoTargetId" />
					</el-select>
				</el-form-item>
				<el-form-item label="检测日期" prop="thermographyDate">
					<el-date-picker v-model="humitureaddForm.thermographyDate" placeholder="请选择检测日期" style="width:90%;"
						value-format="YYYY-MM-DD"></el-date-picker>
				</el-form-item>
				<el-form-item label="检测时间" prop="thermographyTime">
					<el-time-picker v-model="humitureaddForm.thermographyTime" placeholder="请选择检测时间" style="width:90%;"
						value-format="HH:mm:ss" />
				</el-form-item>
				<el-form-item label="检测人" prop="checkPerson">
					<el-select v-model="humitureaddForm.checkPerson" class="form_225" clearable
						placeholder="请选择检测人" style="width:90% !important;">
						<el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item label="温度" prop="temperature">
					<el-input v-model="humitureaddForm.temperature" clearable placeholder="请输入温度" size="normal"
						style="width:90%;" @input="handleTemperatureInput" @blur="formatTemperature">
						<template #suffix>
							<span style="color: #999;">°C</span>
						</template>
					</el-input>
					<div style="font-size: 12px; color: #999; margin-top: 2px; margin-left: 4px;">
						有效范围：-50°C ~ 80°C，支持小数（最多2位）
					</div>
				</el-form-item>
				<el-form-item label="湿度" prop="humidness">
					<el-input v-model="humitureaddForm.humidness" clearable placeholder="请输入湿度" size="normal"
						style="width:90%;" @input="handleHumidityInput" @blur="formatHumidity">
						<template #suffix>
							<span style="color: #999;">%RH</span>
						</template>
					</el-input>
					<div style="font-size: 12px; color: #999; margin-top: 2px; margin-left: 4px;">
						有效范围：0% ~ 100%，支持小数（最多2位）
					</div>
				</el-form-item>
				<el-form-item label="检测情况" prop="thermographyContent">
					<el-input v-model="humitureaddForm.thermographyContent" :rows="2" placeholder="请输入检测情况" style="width:90%;"
						type="textarea" />
				</el-form-item>
			</div>
		</el-form>
		<div class="dialog-footer">
			<el-button @click="dialogFormVisible2 = false">取消</el-button>
			<el-button type="primary" @click="humitureAdd">确定</el-button>
		</div>
	</el-dialog>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, nextTick} from 'vue'
import tree from '../tree'
import storeroomData from "@/api/archive/storeroom/storeroom";
import dailyManagement from "@/api/archive/storeroom/dailyManagement";
import fileInventory from "@/api/archive/storeroom/fileInventory";
import moment from "moment";
import {ElMessage} from "element-plus";

const personrules = reactive({
	houseInfo: [{required: true, message: '请选择档案库房', trigger: 'blur'},],
	roomInfo: [{required: true, message: '请选择档案室', trigger: 'blur'},],
	person: [{required: true, message: '请填写进出人员', trigger: 'blur'},],
	houseEnterContent: [{required: true, message: '请填写事由', trigger: 'blur'},],
	houseEnterTime: [
		{required: true, message: '请选择进库时间', trigger: 'blur'},
		{validator: dataEnter, trigger: 'blur'},
	],
	houseOutTime: [
		{required: true, message: '请选择出库时间', trigger: 'blur'},
		{validator: dataOut, trigger: 'blur'},
	],
})
const humitureRules = reactive({
	houseInfo: [{required: true, message: '请选择档案库房', trigger: 'change'},],
	roomInfo: [{required: true, message: '请选择档案室', trigger: 'change'},],
	thermographyDate: [{required: true, message: '请选择检测日期', trigger: 'blur'},],
	thermographyTime: [{required: true, message: '请选择检测时间', trigger: 'blur'},],
	checkPerson: [{required: true, message: '请选择检测人', trigger: 'blur'},],
	temperature: [
		{required: true, message: '请填写温度', trigger: 'blur'},
		{validator: validateTemperature, trigger: 'blur'},
	],
	humidness: [
		{required: true, message: '请填写湿度', trigger: 'blur'},
		{validator: validateHumidity, trigger: 'blur'},
	],
	thermographyContent: [{required: true, message: '请填写检测情况', trigger: 'blur'},],
})
const {proxy} = getCurrentInstance();
const personnelList = ref([])
const personaddform = ref()
const orguserList = ref([])
const humitureform = ref()
const warehouseList = ref([])
const archivesList = ref([])
const humidityList = ref([])
const personaddForm = reactive({
	houseEnterTime: '',
	houseOutTime: ''
})
const humitureaddForm = reactive({})
const activeName = ref('1')
const superadminId = ref('')
const orgId = ref('')
const dialogFormVisible = ref(false)
const dialogFormVisible2 = ref(false)
const loading = ref(false);
const pertotal = ref(0)
const total = ref(0)
const perqueryParams = ref({
	current: 1,
	size: 10
})
const queryParams = ref({
	current: 1,
	size: 10
})

// 添加缺失的函数
const handleClick = (tab) => {
	activeName.value = tab;
	// 根据tab切换重新加载数据
	if (tab === '1') {
		getList();
	} else if (tab === '2') {
		getList2();
	}
}

const handleSelectionChange = (selection) => {
	// 处理表格选择变化
	console.log('Selection changed:', selection);
}

//进库时间验证
function dataEnter(rule, value, callback) {
	// 如果没有值，跳过验证（由required规则处理）
	if (!value) {
		callback();
		return;
	}

	// 如果出库时间还没有设置，跳过验证
	if (!personaddForm.houseOutTime) {
		callback();
		return;
	}

	try {
		const enterTime = new Date(personaddForm.houseEnterTime);
		const outTime = new Date(personaddForm.houseOutTime);

		// 检查日期是否有效
		if (isNaN(enterTime.getTime()) || isNaN(outTime.getTime())) {
			callback();
			return;
		}

		if (enterTime > outTime) {
			callback('进库时间不能大于出库时间');
		} else if (enterTime.getTime() === outTime.getTime()) {
			callback('进库时间与出库时间不能保持一致');
		} else {
			callback();
		}
	} catch (error) {
		console.error('进库时间验证错误:', error);
		callback();
	}
}

//出库时间验证
function dataOut(rule, value, callback) {
	// 如果没有值，跳过验证（由required规则处理）
	if (!value) {
		callback();
		return;
	}

	// 如果进库时间还没有设置，跳过验证
	if (!personaddForm.houseEnterTime) {
		callback();
		return;
	}

	try {
		const enterTime = new Date(personaddForm.houseEnterTime);
		const outTime = new Date(personaddForm.houseOutTime);

		// 检查日期是否有效
		if (isNaN(enterTime.getTime()) || isNaN(outTime.getTime())) {
			callback();
			return;
		}

		if (outTime < enterTime) {
			callback('出库时间不能小于进库时间');
		} else if (enterTime.getTime() === outTime.getTime()) {
			callback('进库时间与出库时间不能保持一致');
		} else {
			callback();
		}
	} catch (error) {
		console.error('出库时间验证错误:', error);
		callback();
	}
}

// 温度验证
function validateTemperature(rule, value, callback) {
	if (!value) {
		callback();
		return;
	}

	// 检查是否为数字
	const numValue = parseFloat(value);
	if (isNaN(numValue)) {
		callback('温度必须为数字');
		return;
	}

	// 检查温度范围（一般档案室温度范围 -50°C 到 80°C）
	if (numValue < -50 || numValue > 80) {
		callback('温度范围应在-50°C到80°C之间');
		return;
	}

	// 检查小数位数（最多2位小数）
	if (value.toString().includes('.')) {
		const decimalPart = value.toString().split('.')[1];
		if (decimalPart && decimalPart.length > 2) {
			callback('温度最多保留2位小数');
			return;
		}
	}

	callback();
}

// 湿度验证
function validateHumidity(rule, value, callback) {
	if (!value) {
		callback();
		return;
	}

	// 检查是否为数字
	const numValue = parseFloat(value);
	if (isNaN(numValue)) {
		callback('湿度必须为数字');
		return;
	}

	// 检查湿度范围（0% 到 100%）
	if (numValue < 0 || numValue > 100) {
		callback('湿度范围应在0%到100%之间');
		return;
	}

	// 检查小数位数（最多2位小数）
	if (value.toString().includes('.')) {
		const decimalPart = value.toString().split('.')[1];
		if (decimalPart && decimalPart.length > 2) {
			callback('湿度最多保留2位小数');
			return;
		}
	}

	callback();
}

// 温度输入处理
const handleTemperatureInput = (value) => {
	// 只允许数字、小数点和负号
	let filteredValue = value.replace(/[^-0-9.]/g, '');

	// 确保负号只能在开头
	if (filteredValue.indexOf('-') > 0) {
		filteredValue = filteredValue.replace(/-/g, '');
	}

	// 确保只有一个小数点
	const parts = filteredValue.split('.');
	if (parts.length > 2) {
		filteredValue = parts[0] + '.' + parts.slice(1).join('');
	}

	// 限制小数位数为2位
	if (parts.length === 2 && parts[1].length > 2) {
		filteredValue = parts[0] + '.' + parts[1].substring(0, 2);
	}

	humitureaddForm.temperature = filteredValue;
}

// 湿度输入处理
const handleHumidityInput = (value) => {
	// 只允许数字和小数点（湿度不能为负数）
	let filteredValue = value.replace(/[^0-9.]/g, '');

	// 确保只有一个小数点
	const parts = filteredValue.split('.');
	if (parts.length > 2) {
		filteredValue = parts[0] + '.' + parts.slice(1).join('');
	}

	// 限制小数位数为2位
	if (parts.length === 2 && parts[1].length > 2) {
		filteredValue = parts[0] + '.' + parts[1].substring(0, 2);
	}

	// 限制最大值为100
	const numValue = parseFloat(filteredValue);
	if (!isNaN(numValue) && numValue > 100) {
		filteredValue = '100';
	}

	humitureaddForm.humidness = filteredValue;
}

// 温度格式化（失去焦点时）
const formatTemperature = () => {
	if (humitureaddForm.temperature) {
		const numValue = parseFloat(humitureaddForm.temperature);
		if (!isNaN(numValue)) {
			// 格式化为最多2位小数，去除多余的0
			humitureaddForm.temperature = parseFloat(numValue.toFixed(2)).toString();
		}
	}
}

// 湿度格式化（失去焦点时）
const formatHumidity = () => {
	if (humitureaddForm.humidness) {
		const numValue = parseFloat(humitureaddForm.humidness);
		if (!isNaN(numValue)) {
			// 格式化为最多2位小数，去除多余的0
			humitureaddForm.humidness = parseFloat(numValue.toFixed(2)).toString();
		}
	}
}

// 进库时间变化处理
const handleEnterTimeChange = () => {
	// 如果进库时间大于等于出库时间，自动调整出库时间
	if (personaddForm.houseEnterTime && personaddForm.houseOutTime) {
		const enterTime = new Date(personaddForm.houseEnterTime);
		const outTime = new Date(personaddForm.houseOutTime);

		if (enterTime >= outTime) {
			// 自动将出库时间设置为进库时间后1小时
			const newOutTime = new Date(enterTime.getTime() + 60 * 60 * 1000);
			personaddForm.houseOutTime = moment(newOutTime).format('YYYY-MM-DD HH:mm:ss');
		}
	}

	// 延迟验证
	setTimeout(() => {
		if (personaddform.value && personaddForm.houseOutTime) {
			personaddform.value.validateField('houseOutTime');
		}
	}, 100);
}

// 出库时间变化处理
const handleOutTimeChange = () => {
	// 如果出库时间小于等于进库时间，自动调整进库时间
	if (personaddForm.houseEnterTime && personaddForm.houseOutTime) {
		const enterTime = new Date(personaddForm.houseEnterTime);
		const outTime = new Date(personaddForm.houseOutTime);

		if (outTime <= enterTime) {
			// 自动将进库时间设置为出库时间前1小时
			const newEnterTime = new Date(outTime.getTime() - 60 * 60 * 1000);
			personaddForm.houseEnterTime = moment(newEnterTime).format('YYYY-MM-DD HH:mm:ss');
		}
	}

	// 延迟验证
	setTimeout(() => {
		if (personaddform.value && personaddForm.houseEnterTime) {
			personaddform.value.validateField('houseEnterTime');
		}
	}, 100);
}

//人员增加按钮
const personelAdd = (formEl) => {
	// 显示对话框
	dialogFormVisible.value = true;

	// 等待DOM更新后再操作表单
	nextTick(() => {
		// 先重置表单，清除之前的验证状态
		if (formEl) {
			formEl.resetFields();
		}

		// 然后设置默认值
		const now = new Date();
		personaddForm.houseEnterTime = moment(now).format('YYYY-MM-DD HH:mm:ss');
		// 出库时间默认比进库时间晚1小时
		personaddForm.houseOutTime = moment(now).add(1, 'hour').format('YYYY-MM-DD HH:mm:ss');

		// 再次清除可能的验证错误
		if (formEl) {
			formEl.clearValidate();
		}
	});
}
//温湿度增加按钮
const humidityAdd = (formEl) => {
	dialogFormVisible2.value = true;
	humitureaddForm.thermographyDate = moment(new Date()).format('YYYY-MM-DD');
	humitureaddForm.thermographyTime = moment(new Date()).format('HH:mm:ss');
	formEl.resetFields()
}
//侧边栏树结构
const treeList = () => {
	storeroomData.treeData().then(res => {
		if (res.code === 200) {
			warehouseList.value = res.data;
		}
	});
}
// 侧边栏点击事件
const menuClick = (data) => {
		if (activeName.value === '1') {
			let params = {};
			if (data.recordHouseInfoType === '1') {
				personaddForm.houseInfo = data.name;
				params = { 'houseInfo.id': data.recordHouseInfoTargetId };
			}
			if (data.recordHouseInfoType === '2') {
				personaddForm.roomInfo = data.name;
				warehouseList.value.forEach((i) => {
					if (i.id === data.parent.id) {
						personaddForm.houseInfo = i.name;
						archivesList.value = i.children;
					}
				});
				params = { 'roomInfo.id': data.recordHouseInfoTargetId };
			}
            if(data.recordHouseInfoType === '3'){
				personnelList.value = []
				return
			}
			loading.value = true;
			dailyManagement.list(params).then(res => {
				if (res.code === 200) {
			       console.log(res.data.records);
					// warehouseList.value.forEach((i) => {
					// 	res.data.records.forEach((v) => {
					// 		if (i.id === v.houseInfo.id) {
					// 			v.houseInfo.houseName = i.name;
					// 		}
					// 	});
					// });

					// warehouseList.value.forEach((i) => {
					// 	// console.log(i);
					// 	i.children.forEach((item) => {
					// 		res.data.records.forEach((v) => {
					// 			if (item.id === v.roomInfo.id) {
					// 				v.roomInfo.roomName = item.name;
					// 			}
					// 		});
					// 	});
					// });
					personnelList.value = res.data.records;
					pertotal.value = res.data.total;
					loading.value = false;
				}
			})
		}
		if (activeName.value === '2') {
			let params = {}
			if (data.recordHouseInfoType === '1') {
				humitureaddForm.houseInfo = data.name;
				params = { 'houseInfo.id': data.recordHouseInfoTargetId };
			}
			if (data.recordHouseInfoType === '2') {
				humitureaddForm.roomInfo = data.name;
				warehouseList.value.forEach((i) => {
					if (i.id === data.parent.id) {
						humitureaddForm.houseInfo = i.name
						archivesList.value = i.children
					}
				});
				params = { 'roomInfo.id': data.recordHouseInfoTargetId };
			}
			loading.value = true;
			dailyManagement.humitureList(params).then(res => {
				if (res.code === 200) {
					humidityList.value = res.data.records;
					total.value = res.data.total;
					loading.value = false;
				}
			});
		}
}
//获取机构成员
const orgList = () => {
	let org = JSON.parse(localStorage.getItem("Organization"));
	org.content.forEach((v) => {
		orgId.value = v.id
	})
	fileInventory.orgList({ 'sysOrg.id': orgId.value }).then(res => {
		if (res.code === 200) {
			orguserList.value = res.data.records
		}
	});
}
//档案库房
const handlerHouseChange = (v) => {
	warehouseList.value.forEach((i) => {
		if (i.recordHouseInfoTargetId === v) {
			archivesList.value = i.children;
		}
	});
}
//新增人员进出
const personAdd = async (formEl) => {
	if (!formEl) return;

	try {
		const valid = await formEl.validate();
		if (valid) {
			let params = {
				houseInfo: { id: personaddForm.houseInfo },
				roomInfo: { id: personaddForm.roomInfo },
				person: personaddForm.person,
				houseEnterContent: personaddForm.houseEnterContent,
				houseEnterTime: personaddForm.houseEnterTime,
				houseOutTime: personaddForm.houseOutTime
			};
			dailyManagement.save(params).then(res => {
				if (res.code === 200) {
					ElMessage({
						message: "保存成功",
						type: "success",
					});
					dialogFormVisible.value = false;
					getList();
				}
			}).catch(() => {
				ElMessage({
					message: "保存失败",
					type: "error",
				});
			});
		}
	} catch (error) {
		// 验证失败时的处理
		console.log('表单验证失败:', error);
		// 可以在这里添加特定的错误处理逻辑
	}
}
//新增温湿度记录
const humitureAdd = () => {
	proxy.$refs["humitureform"].validate(valid => {
		if (valid) {
			let params = {
				houseInfo: {id: humitureaddForm.houseInfo},
				roomInfo: {id: humitureaddForm.roomInfo},
				thermographyDate: humitureaddForm.thermographyDate,
				thermographyTime: humitureaddForm.thermographyTime,
				checkPerson: {id: humitureaddForm.checkPerson},
				temperature: humitureaddForm.temperature,
				humidness: humitureaddForm.humidness,
				thermographyContent: humitureaddForm.thermographyContent
			};
			dailyManagement.humitureSave(params).then(res => {
				if (res.code === 200) {
					ElMessage({
						message: "保存成功",
						type: "success",
					});
					dialogFormVisible2.value = false;
					getList2();
				}
			});
		}
	});
}

//人员进出记录列表
const getList = () => {
	loading.value = true
	dailyManagement.list({ ...perqueryParams.value }).then(res => {
		if (res.code === 200) {
			// warehouseList.value.forEach((i) => {
			// 	res.data.records.forEach((v) => {
			// 		if (i.id === v.houseInfo.id) {
			// 			v.houseInfo.houseName = i.name
			// 		}
			// 	});
			// });
			// warehouseList.value.forEach((i) => {
			// 	i.children.forEach((item) => {
			// 		res.data.records.forEach((v) => {
			// 			if (item.id === v.roomInfo.id) {
			// 				v.roomInfo.roomName = item.name
			// 			}
			// 		});
			// 	});
			// });
			personnelList.value = res.data.records;
			pertotal.value = res.data.total;
			loading.value = false;
		}
	})
}

//温湿度记录列表
const getList2 = () => {
	let Organization = JSON.parse(localStorage.getItem("USER_INFO"));
	humitureaddForm.checkPerson = Organization.content.id;
	superadminId.value = Organization.content.id;
	loading.value = true;
	dailyManagement.humitureList({ ...queryParams.value }).then(res => {
		if (res.code === 200) {
			humidityList.value = res.data.records
			total.value = res.data.total
			loading.value = false
		}
	});
}

//删除人员进出记录
function deletePerson(row) {
	proxy.$confirm('是否确认删除此条人员进出记录?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		dailyManagement.delete({ ids: row.id }).then(res => {
			if (res.code === 200) {
				getList();
				proxy.msgSuccess("删除成功");
			}
		})
	}).catch(() => { });
}

//删除温湿度记录
function deleteHumidity(row) {
	proxy.$confirm('是否确认删除此条温湿度记录?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		dailyManagement.humitureDelete({ ids: row.id }).then(res => {
			if (res.code === 200) {
				getList2();
				proxy.msgSuccess("删除成功");
			}
		});
	}).catch(() => { });
}
orgList()
treeList();
getList();
getList2();
</script>

<style lang="scss" scoped>
.dialog-footer {
	display: flex;
	justify-content: end;
	margin: 20px 10px 10px 0;
}
</style>
