<template>
  <el-container>
    <el-main style="padding: 0">
      <el-container>
        <!-- 搜索区域 -->
        <el-header height="auto" style="padding: 10px; background: none; border-bottom: none">
          <el-card :body-style="{ height: '100%', width: '100%' }" shadow="never" style="height: 100%;width:100%">
            <el-form :inline="true" :model="searchForm" label-position="left" label-width="auto">
              <el-form-item label="资质名称" prop="name" style="margin-bottom: 0; min-width: 200px">
                <el-input v-model="searchForm.name" clearable placeholder="请输入资质名称" @keydown.enter="search" />
              </el-form-item>
              <el-form-item style="margin-bottom: 0">
                <el-button icon="el-icon-search" type="primary" @click="search">搜索</el-button>
                <el-button icon="el-icon-refresh" plain @click="resetSearch">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-header>

        <!-- 内容区域 -->
        <el-main style="padding: 0 10px 10px 10px">
          <el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }" shadow="never" style="height: 100%;width:100%">
            <el-container>
              <el-header height="auto" style="padding: 0 0 10px 0">
                <div class="flex-between" style="height: 100%;width:100%;">
                  <div>
                    <el-button v-if="isSpecialOrg" icon="el-icon-plus" type="primary" @click="uploadQualification">上传</el-button>
                    <el-button v-if="!isSpecialOrg" icon="el-icon-download" type="warning" @click="batchDownload">批量下载</el-button>
                  </div>
                  <div>
                    <el-button circle icon="el-icon-refresh" @click="refreshTable"></el-button>
                    <el-button circle icon="el-icon-full-screen" @click="screen"></el-button>
                  </div>
                </div>
              </el-header>
              <el-main style="padding: 0">
                <el-table v-loading="loading" :data="tableData" border
                  row-key="id" stripe>
                  <el-table-column align="center" label="资质名称" min-width="180" prop="name"></el-table-column>
                  <el-table-column v-if="isSpecialOrg" align="center" label="是否公开" prop="isPublic" width="80">
                    <template #default="scope">
                      <el-switch v-model="scope.row.isPublic" :active-value="true"
                        :inactive-value="false" :loading="scope.row.$switch_status" size="small"
                        @change="changePublicStatus($event, scope.row)"></el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="公开时间" prop="publicTime" width="180">
                    <template #default="scope">
                      {{ scope.row.isPublic ? scope.row.publicTime : '' }}
                    </template>
                  </el-table-column>
                  <el-table-column v-if="isSpecialOrg" align="center" label="创建时间" prop="createDate" width="180"></el-table-column>
                  <el-table-column :width="isSpecialOrg ? 180 : 120" align="center" fixed="right" label="操作">
                    <template #default="scope">
                      <el-button link size="small" type="primary" @click="viewQualification(scope.row)">查看</el-button>
                      <el-button v-if="isSpecialOrg" link size="small" type="warning" @click="editQualification(scope.row)">修改</el-button>
                      <el-button link size="small" type="success" @click="downloadQualification(scope.row)">下载</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-main>
              <el-footer>
                <div style="display: flex; justify-content: flex-end">
                  <pagination v-model:limit="pageSize" v-model:page="currentPage" :page-sizes="[10, 20, 50, 100]"
                    :total="total" @pagination="handlePagination" />
                </div>
              </el-footer>
            </el-container>
          </el-card>
        </el-main>
      </el-container>
    </el-main>

    <!-- 上传资质对话框 -->
    <el-dialog v-model="uploadDialogVisible" :close-on-click-modal="false" :title="formData.id ? '编辑资质' : '上传资质'"
      width="600px" @close="handleDialogClose">
      <el-form ref="uploadForm" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="资质名称" prop="name">
          <el-input v-model="formData.name" maxlength="30" placeholder="请输入资质名称" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="资质文件" prop="fileInfos" required>
          <el-upload ref="uploadComponent" :action="uploadUrl" :before-upload="beforeUpload" :file-list="fileList" :headers="headers"
            :limit="9" :on-error="handleError" :on-preview="handlePreview" :on-remove="handleRemove"
            :on-success="handleSuccess" accept="image/*,.pdf" class="upload-demo" list-type="picture-card" multiple>
            <el-icon class="upload-plus-icon">
              <Plus />
            </el-icon>
            <template #file="{ file }">
              <div class="custom-file-item">
                <div class="file-thumbnail">
                  <!-- 图片文件显示缩略图 -->
                  <img v-if="isImageFile(file)" :src="file.url" class="upload-image"  alt=""/>
                  <!-- PDF文件显示PDF图标 -->
                  <div v-else-if="isPdfFile(file)" class="pdf-thumbnail">
                    <el-icon class="pdf-icon">
                      <Document />
                    </el-icon>
                    <span class="file-type-text">PDF</span>
                  </div>
                  <!-- 其他文件显示通用图标 -->
                  <div v-else class="other-file-thumbnail">
                    <el-icon class="file-icon">
                      <Document />
                    </el-icon>
                    <span class="file-type-text">文件</span>
                  </div>
                </div>
                <div :class="{ 'single-action': !isImageFile(file) }" class="file-actions">
                  <!-- 只有图片文件才显示查看按钮 -->
                  <el-icon v-if="isImageFile(file)" class="action-icon" @click.stop="handlePreview(file)">
                    <View />
                  </el-icon>
                  <el-icon class="action-icon" @click.stop="handleCustomRemove(file)">
                    <Delete />
                  </el-icon>
                </div>
                <div class="file-name-overlay">{{ file.name }}</div>
              </div>
            </template>
            <template #tip>
              <div class="el-upload__tip">请上传资质相关文件，最多支持9张，大小不限，文件支持图片、PDF</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="是否公开" prop="isPublic" required>
          <el-radio-group v-model="formData.isPublic">
            <el-radio :label="true">公开</el-radio>
            <el-radio :label="false">不公开</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 资质图片查看器 -->
    <el-dialog v-model="imageViewerVisible" :close-on-click-modal="false" class="image-viewer-dialog" title="资质查看"
      top="5vh" width="90%">
      <el-container class="image-viewer-container">
        <!-- 左侧资质导航 -->
        <el-aside v-if="allQualifications.length > 1" class="qualification-aside" width="300px">
          <div class="aside-header">
            <div class="header-content">
              <el-icon class="header-icon">
                <Document />
              </el-icon>
              <span class="header-title">资质列表</span>
              <div class="header-stats">
                <span class="stats-text">共{{ allQualifications.length }}条资质，{{ totalFileCount }}个附件</span>
              </div>
            </div>
          </div>
          <div class="aside-content">
            <div v-for="(item, index) in allQualifications" :key="item.id"
              :class="['qualification-item', { active: currentViewIndex === index }]"
              @click="switchToQualification(index)">
              <div class="item-content">
                <el-icon class="item-icon">
                  <Document />
                </el-icon>
                <el-tooltip :content="item.name" :disabled="!isNameTooLong(item.name)" placement="top">
                  <div class="item-name">{{ item.name }}</div>
                </el-tooltip>
                <div class="item-right">
                  <div class="count-container">
                    <el-tag v-if="item.fileInfos && item.fileInfos.length > 0" class="file-count-tag" size="small">
                      {{ item.fileInfos.length }}
                    </el-tag>
                  </div>
                  <div class="arrow-container">
                    <el-icon v-if="currentViewIndex === index" class="item-arrow">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-aside>

        <!-- 右侧预览区域 -->
        <el-main :class="{ 'full-width': allQualifications.length <= 1 }" class="preview-area" style="padding: 0;">
          <div class="preview-header">
            <div class="qualification-info">
              <div class="title-row">
                <!-- 左侧：标题和标签信息 -->
                <div class="title-and-tags">
                  <div class="title-content">
                    <h3>{{ currentViewQualification?.name }}</h3>
                  </div>
                  <div class="info-tags">
                    <el-tag :type="currentViewQualification?.isPublic ? 'success' : 'info'" size="small">
                      {{ currentViewQualification?.isPublic ? '公开' : '不公开' }}
                    </el-tag>
                    <span class="create-time">创建时间: {{ currentViewQualification?.createDate }}</span>
                    <span v-if="currentViewQualification?.isPublic && currentViewQualification?.publicTime"
                      class="public-time">
                      公开时间: {{ currentViewQualification.publicTime }}
                    </span>
                    <div class="file-type-stats">
                      <span v-if="getImageCount(currentViewQualification) > 0" class="file-stat">
                        <el-icon>
                          <Picture />
                        </el-icon>
                        {{ getImageCount(currentViewQualification) }}张图片
                      </span>
                      <span v-if="getPdfCount(currentViewQualification) > 0" class="file-stat">
                        <el-icon>
                          <Document />
                        </el-icon>
                        {{ getPdfCount(currentViewQualification) }}个PDF
                      </span>
                      <span v-if="getOtherCount(currentViewQualification) > 0" class="file-stat">
                        <el-icon>
                          <Folder />
                        </el-icon>
                        {{ getOtherCount(currentViewQualification) }}个其他
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 右侧：资质切换控件 -->
                <div v-if="allQualifications.length > 1" class="qualification-navigation">
                  <div class="navigation-controls">
                    <span class="nav-label">切换资质</span>
                    <el-button :key="`prev-qual-${currentViewIndex}`" :disabled="currentViewIndex === 0"
                      circle icon="ArrowLeft" size="default" @click="previousQualification">
                    </el-button>
                    <span class="page-info">{{ currentViewIndex + 1 }} / {{ allQualifications.length }}</span>
                    <el-button :key="`next-qual-${currentViewIndex}`"
                      :disabled="currentViewIndex === allQualifications.length - 1" circle
                      icon="ArrowRight" size="default" @click="nextQualification">
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="preview-content">
            <div v-if="currentViewQualification?.fileInfos && currentViewQualification.fileInfos.length > 0"
              class="file-preview">

              <!-- 文件导航控制 -->
              <div v-if="currentViewQualification.fileInfos.length > 0" class="file-navigation">
                <div class="file-info-section">
                  <div class="file-name">{{ currentFile?.fileName }}</div>
                </div>
                <div v-if="currentViewQualification?.fileInfos && currentViewQualification.fileInfos.length > 1"
                  class="file-controls">
                  <span class="nav-label">切换文件</span>
                  <el-button :key="`prev-file-${currentFileIndex}`" :disabled="isFirstFile" circle
                    class="file-nav-button" icon="ArrowLeft" size="small" @click="previousFile">
                  </el-button>
                  <div class="file-meta">
                    <span class="file-count">{{ currentFileIndex + 1 }} / {{ currentViewQualification.fileInfos.length
                    }}</span>
                  </div>
                  <el-button :key="`next-file-${currentFileIndex}`" :disabled="isLastFile" circle
                    class="file-nav-button" icon="ArrowRight" size="small" @click="nextFile">
                  </el-button>
                </div>
              </div>

              <!-- 当前文件预览 -->
              <div :key="`file-${currentFileIndex}-${currentFile?.fileName}`" class="current-file-preview">
                <!-- 图片预览 -->
                <div v-if="isImage(currentFile?.fileUrl)" class="image-container">
                  <el-image :key="`image-${currentFileIndex}`" :alt="currentFile.fileName" :hide-on-click-modal="true"
                    :initial-index="getImageIndex()" :preview-src-list="getImageUrls()" :preview-teleported="true"
                    :src="getImageUrl(currentFile.fileUrl)" :z-index="9999" class="preview-image" fit="contain"
                    @error="onImageError">
                    <template #error>
                      <div class="image-error">
                        <el-icon>
                          <Picture />
                        </el-icon>
                        <p>图片加载失败</p>
                      </div>
                    </template>
                  </el-image>
                </div>
                <!-- PDF预览 - 修复firstPagePromise错误 -->
                <div v-else-if="isPdf(currentFile?.fileUrl)" v-loading="pdfLoading" class="pdf-container">
                  <PDFViewer v-if="pdfRef" :key="`pdf-${currentFileIndex}-${pdfRef}`" :src="pdfRef" height="100%"
                    pageScale="page-fit" theme="light" width="100%" @loaded="onLoaded" />
                  <div v-else class="pdf-placeholder">
                    <p>正在准备PDF预览...</p>
                  </div>
                </div>
                <!-- 其他文件类型 -->
                <div v-else class="other-file">
                  <el-icon class="file-icon">
                    <Document />
                  </el-icon>
                  <p>{{ currentFile?.fileName }}</p>
                  <el-button type="primary" @click="downloadFile(currentFile?.fileUrl)">
                    下载文件
                  </el-button>
                </div>
              </div>
            </div>
            <div v-else class="no-file">
              <el-icon class="no-file-icon">
                <Picture />
              </el-icon>
              <p>暂无文件</p>
            </div>
          </div>

          <div class="preview-actions">
            <el-button class="action-btn" size="default" type="primary" @click="downloadAllFiles">
              <el-icon>
                <Download />
              </el-icon>
              下载全部附件
            </el-button>
            <el-button class="action-btn" size="default" type="success"
              @click="editQualification(currentViewQualification)">
              <el-icon>
                <Edit />
              </el-icon>
              编辑
            </el-button>
          </div>
        </el-main>
      </el-container>
    </el-dialog>


  </el-container>
</template>

<script>
import pagination from '@/components/Pagination/index.vue'
import tool from '@/utils/tool';
import qualificationService from '@/api/model/qualificationService';
import { Plus, Document, Picture, Download, Edit, View, ArrowRight, Folder, Delete } from '@element-plus/icons-vue';
import { extractFilenameFromContentDisposition } from '@/utils';

import PDFViewer from '@/views/archiveReception/common/PDFViewer.vue';


export default {
  name: 'QualificationManagement',
  components: {
    pagination,
    Plus,
    Document,
    Picture,
    Download,
    Edit,
    View,
    ArrowRight,
    Folder,
    Delete,
    PDFViewer
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        name: ''
      },

      // 上传对话框可见性
      uploadDialogVisible: false,
      // 图片查看器可见性
      imageViewerVisible: false,
      // 当前查看的资质索引
      currentViewIndex: 0,
      // 所有资质数据（用于查看器）
      allQualifications: [],
      // 用户权限控制
      isSpecialOrg: false, // 是否为特殊组织（753188220495929344）
      // 表单数据
      formData: {
        id: '', // 用于区分新增和编辑
        name: '',
        isPublic: false,
        fileInfos: []
      },
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入资质名称', trigger: 'blur' }
        ],
        fileInfos: [
          {
            required: true,
            validator: (_, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请上传资质文件'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      // 文件列表
      fileList: [],
      headers: {
        Authorization: "Bearer " + tool.cookie.get("TOKEN"),
        ContentType: "multipart/form-data",
        clientType: "PC",
      },
      // 上传URL
      uploadUrl: process.env.VUE_APP_API_UPLOAD,
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false,
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: ''
      },
      // 当前文件索引
      currentFileIndex: 0,
      // PDF加载状态
      pdfLoading: false,
      // PDF源URL
      pdfRef: null,
      // 当前查看的资质详情数据（来自详情接口）
      currentQualificationDetail: null,
      // 文件重命名映射表 (uid -> 重命名后的文件名)
      fileRenameMap: new Map()
    }
  },
  computed: {
    // 当前查看的文件
    currentFile() {
      if (!this.currentViewQualification?.fileInfos || this.currentViewQualification.fileInfos.length === 0) {
        return null
      }
      return this.currentViewQualification.fileInfos[this.currentFileIndex] || this.currentViewQualification.fileInfos[0]
    },
    // 当前查看的资质（优先使用详情数据）
    currentViewQualification() {
      // 如果有详情数据，优先使用详情数据
      if (this.currentQualificationDetail) {
        return this.currentQualificationDetail
      }
      // 否则使用列表数据
      return this.allQualifications[this.currentViewIndex] || null
    },
    // 是否是第一个文件
    isFirstFile() {
      return !this.currentViewQualification?.fileInfos ||
        this.currentViewQualification.fileInfos.length === 0 ||
        this.currentFileIndex <= 0
    },
    // 是否是最后一个文件
    isLastFile() {
      // 确保有文件信息
      const fileInfos = this.currentViewQualification?.fileInfos
      if (!fileInfos || fileInfos.length === 0) {
        return true
      }

      // 确保索引在有效范围内
      const fileCount = fileInfos.length
      const currentIndex = this.currentFileIndex

      return currentIndex >= fileCount - 1
    },
    // 计算总附件数
    totalFileCount() {
      if (!this.allQualifications || this.allQualifications.length === 0) {
        return 0
      }
      return this.allQualifications.reduce((total, qualification) => {
        return total + (qualification.fileInfos ? qualification.fileInfos.length : 0)
      }, 0)
    }

  },
  created() {
    // 初始化用户权限
    this.initUserPermissions()
  },
  mounted() {
    this.getTableData()
  },
  methods: {

    /**
     * 智能调整文件索引
     * 在文件被删除后，确保文件索引指向有效的文件
     * @param {Array} newFileInfos - 更新后的文件列表
     */
    adjustFileIndexAfterUpdate(newFileInfos) {
      const newFileCount = newFileInfos?.length || 0

      if (newFileCount === 0) {
        // 如果没有文件了，重置文件索引为0
        this.currentFileIndex = 0
        return
      }

      if (this.currentFileIndex >= newFileCount) {
        // 如果当前文件索引超出范围，调整到最后一个有效文件
        this.currentFileIndex = newFileCount - 1
      }
    },

    /**
     * 批量下载功能
     * 下载所有资质文件
     */
    async batchDownload() {

      const loading = this.$loading({
        lock: true,
        text: '准备批量下载...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 调用批量下载接口，传递 resDetail 为 true 以获取完整响应对象（包含headers）
        const response = await qualificationService.batchDownload({}, '', true, 'blob')

        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/zip' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 从响应头的 content-disposition 中提取文件名
        const extractedFilename = extractFilenameFromContentDisposition(response.headers['content-disposition'])
        link.download = extractedFilename || `资质文件批量下载_${new Date().getTime()}.zip`

        // 执行下载
        document.body.appendChild(link)
        link.click()

        // 清理
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }, 100)

        loading.close()
        this.$message.success('批量下载成功')

      } catch (error) {
        loading.close()
        console.error('批量下载失败:', error)

        // 根据错误类型提供更具体的错误信息
        if (error.response && error.response.status === 404) {
          this.$message.error('批量下载接口暂未实现，请联系管理员')
        } else if (error.response && error.response.status === 500) {
          this.$message.error('服务器内部错误，请稍后重试')
        } else {
          this.$message.error('批量下载失败，请重试')
        }
      }
    },



    /**
     * 文件上传前校验
     * @param {Object} file - 要上传的文件对象
     * @returns {boolean} 是否允许上传
     */
    beforeUpload(file) {
      // 允许的文件类型
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf'
      ];

      // 允许的文件扩展名
      const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
      const fileExtension = file.name.split('.').pop().toLowerCase();

      // 检查文件类型和扩展名
      const isAllowedType = allowedTypes.includes(file.type);
      const isAllowedExtension = allowedExtensions.includes(fileExtension);

      if (!isAllowedType || !isAllowedExtension) {
        // 提示错误信息
        this.$message.error('只能上传图片(JPG/PNG/GIF)或PDF文件！')
        return false; // 阻止上传
      }

      // 检查文件名是否重复，如果重复则自动重命名
      const isDuplicate = this.fileList.some(existingFile => existingFile.name === file.name)
      if (isDuplicate) {
        const newName = this.generateUniqueFileName(file.name)
        // 存储重命名映射
        this.fileRenameMap.set(file.uid, newName)
        // 修改文件对象的名称
        Object.defineProperty(file, 'name', {
          writable: true,
          value: newName
        })
        this.$message.info(`文件名重复，已自动重命名为: ${newName}`)
      } else {
        // 如果没有重命名，也要记录原始名称
        this.fileRenameMap.set(file.uid, file.name)
      }

      return true
    },
    /**
     * 切换公开状态
     * @param {boolean} val - 新的公开状态值 (true 公开, false 不公开)
     * @param {Object} row - 当前行数据
     */
    async changePublicStatus(val, row) {
      // 防止重复点击
      if (row.$switch_status) {
        return
      }

      row.$switch_status = true

      try {
        // 调用API更新状态
        const response = await qualificationService.updatePublicStatus(row.id)

        if (response && response.code === 200) {
          this.$message.success('状态更新成功')

          // 如果是设为公开，更新公开时间
          if (val === true) {
            row.publicTime = this.formatDate(new Date())
          } else {
            row.publicTime = ''
          }
        } else {
          throw new Error(response?.message || '状态更新失败')
        }
      } catch (error) {
        console.error('更新状态失败:', error)
        this.$message.error('状态更新失败')
        // 回滚状态
        row.isPublic = !row.isPublic
      } finally {
        // 确保loading状态被清除
        delete row.$switch_status
      }
    },

    /**
     * 检测文件类型
     * @param {string} fileUrl - 文件URL
     * @returns {string} 文件类型：'image', 'pdf', 'other'
     */
    checkFileType(fileStrUrl) {
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
      const pdfExtensions = ['pdf'];

      if (fileStrUrl) {
        let returnData = '';
        imageExtensions.forEach(extension => {
          if (fileStrUrl.toLowerCase().includes(extension)) {
            returnData = 'image';
          }
        });
        pdfExtensions.forEach(extension => {
          if (fileStrUrl.toLowerCase().includes(extension)) {
            returnData = 'pdf';
          }
        });
        return returnData;
      } else {
        return 'other';
      }
    },

    /**
     * 下载全部附件 - 与列表下载功能保持一致
     */
    async downloadAllFiles() {
      if (!this.currentViewQualification?.fileInfos || this.currentViewQualification.fileInfos.length === 0) {
        this.$message.warning('暂无附件可下载')
        return
      }

      // 直接调用列表下载方法，保持功能一致
      await this.downloadQualification(this.currentViewQualification)
    },

    /**
     * 下载文件
     * @param {string} url - 文件下载链接
     */
    downloadFile(url) {
      try {
        if (!url) {
          this.$message.warning('文件链接无效')
          return
        }
        window.open(url, '_blank')
      } catch (error) {
        console.error('文件下载失败:', error)
        this.$message.error('文件下载失败')
      }
    },
    /**
     * 下载资质文件
     * @param {Object} row - 资质数据行
     */
    async downloadQualification(row) {
      // 参数验证
      if (!row || !row.id) {
        this.$message.error('资质信息不完整，无法下载')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '准备下载...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 检查附件数量
        const fileCount = row.fileInfos ? row.fileInfos.length : 0

        if (fileCount > 1) {
          // 多个附件，下载为zip文件
          const res = await qualificationService.download({
            id: row.id
          }, '', true, 'blob')

          // 创建下载链接
          const blob = new Blob([res.data], { type: 'application/zip' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url

          // 从响应头的 content-disposition 中提取文件名
          const extractedFilename = extractFilenameFromContentDisposition(res.headers['content-disposition'])
          link.download = extractedFilename || `${row.name}_附件.zip`

          // 执行下载
          document.body.appendChild(link)
          link.click()

          // 清理
          setTimeout(() => {
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
          }, 100)
        } else {
          // 单个附件，直接下载原文件
          const res = await qualificationService.download({
            id: row.id
          }, '', true, 'blob')

          // 创建下载链接
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url

          // 从响应头的 content-disposition 中提取文件名，如果没有则使用原始文件名
          const extractedFilename = extractFilenameFromContentDisposition(res.headers['content-disposition'])
          link.download = extractedFilename || row.fileInfos[0].fileName

          // 执行下载
          document.body.appendChild(link)
          link.click()

          // 清理
          setTimeout(() => {
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
          }, 100)
        }

        loading.close()
        this.$message.success(`${row.name} 下载成功`)

      } catch (error) {
        loading.close()
        console.error('下载失败:', error)
        this.$message.error('下载失败，请重试')
      }
    },
    // 编辑资质
    editQualification(row) {
      // 确保使用最新的数据
      // 如果是从预览区域点击编辑，使用 currentViewQualification 的最新数据
      // 如果是从表格点击编辑，使用传入的 row 数据
      const qualificationData = (row.id === this.currentViewQualification?.id)
        ? this.currentViewQualification
        : row



      this.formData = {
        id: qualificationData.id, // 有id表示编辑
        name: qualificationData.name,
        isPublic: qualificationData.isPublic,
        fileInfos: qualificationData.fileInfos || []
      }
      this.fileList = qualificationData.fileInfos && qualificationData.fileInfos.length > 0 ?
        qualificationData.fileInfos.map((file, index) => ({
          name: file.fileName,
          url: file.fileUrl,
          uid: file.uid || `existing-file-${Date.now()}-${index}` // 为已存在的文件生成唯一ID
        })) : []
      this.fileRenameMap.clear() // 清理文件重命名映射表
      this.uploadDialogVisible = true
      // 清除表单校验状态
      this.$nextTick(() => {
        this.$refs.uploadForm?.clearValidate()
      })
    },

    /**
     * 格式化日期
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD)
     */
    formatDate(date) {
      try {
        // 参数验证
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
          return ''
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      } catch (error) {
        console.error('日期格式化失败:', error)
        return ''
      }
    },

    /**
     * 生成唯一的文件名
     * @param {string} originalName - 原始文件名
     * @returns {string} 唯一的文件名
     */
    generateUniqueFileName(originalName) {
      const existingNames = this.fileList.map(file => file.name)
      let counter = 1
      let newName = originalName

      // 分离文件名和扩展名
      const lastDotIndex = originalName.lastIndexOf('.')
      const nameWithoutExt = lastDotIndex > 0 ? originalName.substring(0, lastDotIndex) : originalName
      const extension = lastDotIndex > 0 ? originalName.substring(lastDotIndex) : ''

      // 循环直到找到唯一的文件名
      while (existingNames.includes(newName)) {
        newName = `${nameWithoutExt}(${counter})${extension}`
        counter++
      }

      return newName
    },

    /**
     * 获取资质中图片文件的数量
     * @param {Object} qualification - 资质对象
     * @returns {number} 图片文件数量
     */
    getImageCount(qualification) {
      if (!qualification?.fileInfos) return 0
      return qualification.fileInfos.filter(file => this.isImage(file.fileUrl)).length
    },

    /**
     * 获取当前图片在图片列表中的索引
     */
    getImageIndex() {
      if (!this.currentViewQualification?.fileInfos || !this.currentFile) return 0
      const imageFiles = this.currentViewQualification.fileInfos.filter(file => this.isImage(file.fileUrl))
      return imageFiles.findIndex(file => file.fileUrl === this.currentFile.fileUrl)
    },

    /**
     * 获取图片URL
     * @param {string} fileUrl - 文件URL路径
     * @returns {string} 处理后的图片URL
     */
    getImageUrl(fileUrl) {
      return fileUrl;
    },

    /**
     * 获取所有图片的URL列表，用于图片预览器
     */
    getImageUrls() {
      if (!this.currentViewQualification?.fileInfos) return []
      return this.currentViewQualification.fileInfos
        .filter(file => this.isImage(file.fileUrl))
        .map(file => this.getImageUrl(file.fileUrl))
    },

    /**
     * 获取资质中其他类型文件的数量
     * @param {Object} qualification - 资质对象
     * @returns {number} 其他文件数量
     */
    getOtherCount(qualification) {
      if (!qualification?.fileInfos) return 0
      return qualification.fileInfos.filter(file =>
        !this.isImage(file.fileUrl) && !this.isPdf(file.fileUrl)
      ).length
    },

    /**
     * 获取资质中PDF文件的数量
     * @param {Object} qualification - 资质对象
     * @returns {number} PDF文件数量
     */
    getPdfCount(qualification) {
      if (!qualification?.fileInfos) return 0
      return qualification.fileInfos.filter(file => this.isPdf(file.fileUrl)).length
    },
    /**
     * 获取表格数据
     * 支持分页和搜索功能
     */
    async getTableData() {
      this.loading = true
      try {
        // 更新查询参数
        this.queryParams.current = this.currentPage
        this.queryParams.size = this.pageSize
        this.queryParams.name = this.searchForm.name?.trim() || ''

        // 调用真实的API接口
        const response = await qualificationService.getPublicPage(this.queryParams)

        // 处理响应数据
        if (response && response.code === 200) {
          this.tableData = response.data.records || []
          this.total = response.data.total || 0
        } else {
          throw new Error(response?.message || '获取数据失败')
        }
      } catch (error) {
        this.$message.error('获取数据失败')
        console.error('获取表格数据失败:', error)
        // 确保在出错时也要重置数据状态
        this.tableData = []
        this.total = 0
      } finally {
        // 确保loading状态总是被重置
        this.loading = false
      }
    },




    /**
     * 自定义文件删除处理（用于自定义模板）
     * @param {Object} file - 要删除的文件对象
     */
    async handleCustomRemove(file) {
      try {
        // 先确认删除
        const fileName = file?.name || '该文件'
        await this.$confirm(`确定移除 ${fileName}？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        console.log('删除文件:', file)
        console.log('当前文件列表:', this.fileList)

        // 获取正确的文件名（可能是重命名后的）
        const finalFileName = this.fileRenameMap.get(file.uid) || file.name

        // 从重命名映射表中移除
        this.fileRenameMap.delete(file.uid)

        // 从 formData.fileInfos 中移除对应的文件信息
        if (finalFileName) {
          this.formData.fileInfos = this.formData.fileInfos.filter(
            fileInfo => fileInfo.fileName !== finalFileName
          )
        }

        // 从文件列表中移除文件，使用多种方式确保正确匹配
        const newFileList = this.fileList.filter(f => {
          // 优先使用 uid 比较
          if (f.uid && file.uid) {
            return f.uid !== file.uid
          }
          // 如果没有 uid，使用 name 和 url 组合比较
          return !(f.name === file.name && f.url === file.url)
        })

        console.log('删除后的文件列表:', newFileList)

        // 更新文件列表
        this.fileList = [...newFileList] // 创建新数组引用，强制触发响应式更新

        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.uploadForm?.validateField('fileInfos')
        })

        this.$message.success('文件删除成功')
      } catch (error) {
        // 用户取消删除，不做任何操作
        console.log('用户取消删除或删除出错:', error)
      }
    },

    /**
     * 处理对话框关闭事件
     * 清除表单校验状态，避免下次打开时显示之前的校验结果
     */
    handleDialogClose() {
      // 清除表单校验状态
      this.$nextTick(() => {
        this.$refs.uploadForm?.clearValidate()
      })
    },

    /**
     * 文件上传失败回调
     * @param {Error} error - 错误对象
     * @param {Object} file - 上传失败的文件对象
     */
    handleError(error, file) {
      console.error('文件上传失败:', error, file)
      this.$message.error(`文件 ${file?.name || '未知文件'} 上传失败`)
    },

    /**
     * 处理分页变化
     * @param {Object} pagination - 分页参数对象
     * @param {number} pagination.page - 当前页码
     * @param {number} pagination.limit - 每页条数
     */
    handlePagination(pagination) {
      if (!pagination) {
        return
      }

      this.currentPage = pagination.page || 1
      this.pageSize = pagination.limit || 10
      this.getTableData()
    },

    /**
     * 文件预览回调
     * @param {Object} file - 要预览的文件对象
     */
    handlePreview(file) {
      try {
        // 检查文件URL是否有效
        if (file?.url) {
          window.open(file.url, '_blank')
        } else {
          this.$message.warning('文件预览链接无效')
        }
      } catch (error) {
        console.error('文件预览失败:', error)
        this.$message.error('文件预览失败')
      }
    },

    /**
     * 文件移除回调（Element Plus默认调用）
     * @param {Object} file - 被移除的文件对象
     */
    handleRemove(file) {
      try {
        // 获取正确的文件名（可能是重命名后的）
        const finalFileName = this.fileRenameMap.get(file.uid) || file.name

        // 从重命名映射表中移除
        this.fileRenameMap.delete(file.uid)

        // 从 fileInfos 中移除对应的文件信息
        if (finalFileName) {
          this.formData.fileInfos = this.formData.fileInfos.filter(
            fileInfo => fileInfo.fileName !== finalFileName
          )
          // 触发表单验证
          this.$nextTick(() => {
            this.$refs.uploadForm?.validateField('fileInfos')
          })
        }

      } catch (error) {
        console.error('处理文件移除时出错:', error)
      }
    },
    /**
     * 文件上传成功回调
     * @param {Object} response - 服务器响应
     * @param {Object} file - 上传的文件对象
     * @param {Array} fileList - 文件列表
     */
    handleSuccess(response, file, fileList) {
      try {
        // 检查响应格式和状态
        if (response && response.code === 200 && response.data) {
          // 使用重命名映射表中的文件名，确保使用正确的重命名后文件名
          const finalFileName = this.fileRenameMap.get(file.uid) || file.name

          // 添加文件信息到 formData.fileInfos
          const fileInfo = {
            fileName: finalFileName,
            fileUrl: response.data.url || response.data.fileUrl
          }
          this.formData.fileInfos.push(fileInfo)

          // 更新文件列表中对应文件的显示名称，确保缩略图显示正确的名称
          const fileIndex = this.fileList.findIndex(f => f.uid === file.uid)
          if (fileIndex !== -1) {
            this.fileList[fileIndex].name = finalFileName
          }

          // 触发表单验证
          this.$nextTick(() => {
            this.$refs.uploadForm?.validateField('fileInfos')
          })
          this.$message.success('文件上传成功')
        } else {
          // 处理上传失败的情况
          this.$message.error(response?.message || '文件上传失败')
          // 安全地移除失败的文件
          if (fileList && fileList.length > 0) {
            fileList.pop()
          }
        }
      } catch (error) {
        console.error('处理上传成功回调时出错:', error)
        this.$message.error('文件上传处理失败')
        // 确保移除有问题的文件
        if (fileList && fileList.length > 0) {
          fileList.pop()
        }
      }
    },

    /**
     * 处理PDF文件查看 - 完全参照receive.vue的实现
     * @param {string} url - PDF文件URL
     */
    handleViewFile(url) {
      // 处理URL，确保以/开头以便代理正确工作
      let processedUrl = url;
      if (url && !url.startsWith('/') && !url.startsWith('http')) {
        processedUrl = '/' + url;
      }

      // 避免firstPagePromise错误，先完全清空并延迟设置
      if (this.pdfRef !== processedUrl) {
        this.pdfRef = ''
        this.pdfLoading = true

        // 使用$nextTick确保DOM更新后再设置PDF URL
        this.$nextTick(() => {
          this.pdfRef = processedUrl
        })
      }
    },
    /**
     * 初始化用户权限
     * 判断当前用户是否为特殊组织
     */
    initUserPermissions() {
      try {
        const userInfo = tool.data.get("USER_INFO")
        if (userInfo && userInfo.sysOrg && userInfo.sysOrg.id) {
          this.isSpecialOrg = userInfo.sysOrg.id === '753188220495929344'
        } else {
          this.isSpecialOrg = false
        }
        console.log('用户组织ID:', userInfo?.sysOrg?.id, '是否为特殊组织:', this.isSpecialOrg)
      } catch (error) {
        console.error('初始化用户权限失败:', error)
        this.isSpecialOrg = false
      }
    },
    /**
     * 判断是否为图片文件
     * @param {string} url - 文件URL
     * @returns {boolean} 是否为图片
     */
    isImage(url) {
      if (!url || typeof url !== 'string') {
        return false
      }
      return this.checkFileType(url) === 'image'
    },

    /**
     * 判断是否为图片文件（用于上传组件）
     * @param {Object} file - 文件对象
     * @returns {boolean} 是否为图片
     */
    isImageFile(file) {
      if (!file || !file.name) return false
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      const extension = file.name.split('.').pop()?.toLowerCase()
      return imageExtensions.includes(extension)
    },

    /**
     * 判断资质名称是否过长需要显示tooltip
     * @param {string} name - 资质名称
     * @returns {boolean} 是否需要显示tooltip
     */
    isNameTooLong(name) {
      if (!name) return false
      // 根据字符长度和容器宽度判断，这里设置一个合理的阈值
      // 考虑到中文字符和英文字符的宽度差异，以及图标和数量标签占用的空间
      return name.length > 12 // 可以根据实际情况调整这个值
    },

    /**
     * 判断是否为PDF文件
     * @param {string} url - 文件URL
     * @returns {boolean} 是否为PDF
     */
    isPdf(url) {
      if (!url || typeof url !== 'string') {
        return false
      }
      return this.checkFileType(url) === 'pdf'
    },

    /**
     * 判断是否为PDF文件（用于上传组件）
     * @param {Object} file - 文件对象
     * @returns {boolean} 是否为PDF
     */
    isPdfFile(file) {
      if (!file || !file.name) return false
      const extension = file.name.split('.').pop()?.toLowerCase()
      return extension === 'pdf'
    },

    /**
     * 加载当前资质的详情数据
     */
    async loadQualificationDetail() {
      const currentQualification = this.allQualifications[this.currentViewIndex]
      if (currentQualification?.id) {
        try {
          const response = await qualificationService.getById(currentQualification.id)
          if (response && response.code === 200 && response.data) {
            this.currentQualificationDetail = response.data
          }
        } catch (error) {
          console.error('加载资质详情失败:', error)
          this.currentQualificationDetail = null
        }
      }
    },

    /**
     * 切换到下一个文件
     */
    nextFile() {
      if (this.currentViewQualification?.fileInfos &&
        this.currentFileIndex < this.currentViewQualification.fileInfos.length - 1) {
        this.currentFileIndex++
        // 如果是PDF文件，使用handleViewFile方法
        this.$nextTick(() => {
          if (this.currentFile && this.isPdf(this.currentFile.fileUrl)) {
            this.handleViewFile(this.currentFile.fileUrl)
          }
        })
      }
    },

    /**
     * 切换到下一个资质
     */
    async nextQualification() {
      if (this.currentViewIndex < this.allQualifications.length - 1) {
        this.currentViewIndex++
        this.currentFileIndex = 0
        await this.loadQualificationDetail()
        this.$nextTick(() => {
          if (this.currentFile && this.isPdf(this.currentFile.fileUrl)) {
            this.handleViewFile(this.currentFile.fileUrl)
          }
        })
      }
    },

    /**
     * 图片加载错误处理
     */
    onImageError() {
      // 图片加载失败时的处理逻辑
    },

    /**
     * PDF加载完成回调 - 参照receive.vue的onLoaded方法
     */
    onLoaded() {
      this.pdfLoading = false
    },

    /**
     * 切换到上一个文件
     */
    previousFile() {
      if (this.currentFileIndex > 0) {
        this.currentFileIndex--
        // 如果是PDF文件，使用handleViewFile方法
        this.$nextTick(() => {
          if (this.currentFile && this.isPdf(this.currentFile.fileUrl)) {
            this.handleViewFile(this.currentFile.fileUrl)
          }
        })
      }
    },

    /**
     * 切换到上一个资质
     */
    async previousQualification() {
      if (this.currentViewIndex > 0) {
        this.currentViewIndex--
        this.currentFileIndex = 0
        await this.loadQualificationDetail()
        this.$nextTick(() => {
          if (this.currentFile && this.isPdf(this.currentFile.fileUrl)) {
            this.handleViewFile(this.currentFile.fileUrl)
          }
        })
      }
    },



    /**
     * 刷新当前查看的资质数据
     * 用于编辑保存后同步更新预览区域的数据
     */
    async refreshCurrentViewQualification() {
      try {
        const currentQualificationId = this.currentViewQualification?.id
        if (!currentQualificationId) {
          return
        }

        const response = await qualificationService.getById(currentQualificationId)
        if (response && response.code === 200 && response.data) {
          const updatedQualification = response.data
          this.currentQualificationDetail = updatedQualification

          const currentIndex = this.allQualifications.findIndex(q => q.id === currentQualificationId)
          if (currentIndex !== -1) {
            this.allQualifications.splice(currentIndex, 1, updatedQualification)
            this.adjustFileIndexAfterUpdate(updatedQualification.fileInfos)
          }
        }
      } catch (error) {
        console.error('刷新当前查看资质数据失败:', error)
      }
    },

    /**
     * 刷新表格数据
     * 保持当前页码和搜索条件
     */
    refreshTable() {
      this.getTableData()
    },

    /**
     * 重置搜索条件
     * 清空搜索表单并重新获取数据
     */
    resetSearch() {
      this.searchForm.name = ''
      this.currentPage = 1
      this.getTableData()
    },

    /**
     * 切换全屏模式
     * 使用工具类进行全屏切换
     */
    screen() {
      try {
        const element = document.documentElement
        if (tool && typeof tool.screen === 'function') {
          tool.screen(element)
        } else {
          this.$message.warning('全屏功能暂不可用')
        }
      } catch (error) {
        console.error('切换全屏失败:', error)
        this.$message.error('切换全屏失败')
      }
    },


    /**
     * 执行搜索操作
     * 重置到第一页并重新获取数据
     */
    search() {
      this.currentPage = 1
      this.getTableData()
    },


    /**
     * 提交上传表单
     * 包含表单验证和错误处理
     */
    async submitUpload() {
      // 检查表单引用是否存在
      if (!this.$refs.uploadForm) {
        this.$message.error('表单初始化失败，请重试')
        return
      }

      this.$refs.uploadForm.validate(async (valid) => {
        if (valid) {

          const loading = this.$loading({
            lock: true,
            text: '提交中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          try {
            // 准备提交数据
            const submitData = {
              name: this.formData.name,
              isPublic: this.formData.isPublic,
              fileInfos: this.formData.fileInfos
            }

            let response
            if (this.formData.id) {
              // 编辑模式 - 调用编辑接口
              submitData.id = this.formData.id
              response = await qualificationService.edit(submitData)
            } else {
              // 新增模式 - 调用新增接口
              response = await qualificationService.add(submitData)
            }

            if (response && response.code === 200) {
              loading.close()
              this.$message.success('保存成功')
              this.uploadDialogVisible = false

              // 重新加载表格数据
              await this.getTableData()

              // 如果是编辑模式，需要同步更新当前查看的资质数据
              if (this.formData.id) {
                await this.refreshCurrentViewQualification()
              }
            } else {
              throw new Error(response?.message || '保存失败')
            }
          } catch (error) {
            loading.close()
            console.error('保存失败:', error)
            this.$message.error('保存失败，请重试')
          }
        } else {
          this.$message.warning('请完善表单信息')
        }
      })
    },



    /**
     * 切换到指定资质
     * @param {number} index - 资质索引
     */
    async switchToQualification(index) {
      // 边界检查
      if (index < 0 || index >= this.allQualifications.length) {
        return
      }

      this.currentViewIndex = index
      this.currentFileIndex = 0

      // 获取当前资质的详情数据
      const currentQualification = this.allQualifications[index]
      if (currentQualification?.id) {
        try {
          const response = await qualificationService.getById(currentQualification.id)
          if (response && response.code === 200 && response.data) {
            this.currentQualificationDetail = response.data
          }
        } catch (error) {
          console.error('获取资质详情失败:', error)
          this.currentQualificationDetail = null
        }
      }

      // 如果第一个文件是PDF，立即加载
      this.$nextTick(() => {
        if (this.currentFile && this.isPdf(this.currentFile.fileUrl)) {
          this.handleViewFile(this.currentFile.fileUrl)
        }
      })
    },
    // 上传资质
    uploadQualification() {
      this.formData = {
        id: '', // 空id表示新增
        name: '',
        isPublic: false,
        fileInfos: []
      }
      this.fileList = []
      this.fileRenameMap.clear() // 清理文件重命名映射表
      this.uploadDialogVisible = true
      // 清除表单校验状态
      this.$nextTick(() => {
        this.$refs.uploadForm?.clearValidate()
      })
    },
    // 查看资质
    async viewQualification(row) {
      let loading = null
      try {
        loading = this.$loading({
          lock: true,
          text: '加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 并行获取所有资质列表和当前资质详情
        const [allQualificationsResponse, currentQualificationResponse] = await Promise.all([
          // 获取所有资质列表，用于左侧导航
          qualificationService.getPublicPage({
            current: 1,
            size: -1, // 获取所有数据
          }),
          // 获取当前资质的详细信息
          qualificationService.getById(row.id)
        ])

        // 处理所有资质列表数据
        if (allQualificationsResponse && allQualificationsResponse.code === 200) {
          this.allQualifications = allQualificationsResponse.data.records || []
        } else {
          // 如果获取列表失败，至少显示当前资质
          this.allQualifications = []
        }

        // 处理当前资质详情数据
        if (currentQualificationResponse && currentQualificationResponse.code === 200 && currentQualificationResponse.data) {
          const currentQualification = currentQualificationResponse.data

          // 存储详情数据，用于右侧预览显示
          this.currentQualificationDetail = currentQualification

          // 如果成功获取了列表数据，找到当前资质在列表中的位置
          if (this.allQualifications.length > 0) {
            const currentIndex = this.allQualifications.findIndex(item => item.id === row.id)
            if (currentIndex !== -1) {
              // 更新列表中对应项的详细信息（确保包含fileInfos）
              const updatedItem = {
                ...this.allQualifications[currentIndex],
                ...currentQualification
              }
              this.allQualifications.splice(currentIndex, 1, updatedItem)
              this.currentViewIndex = currentIndex
            } else {
              // 如果在列表中没找到，添加到开头
              this.allQualifications.unshift(currentQualification)
              this.currentViewIndex = 0
            }
          } else {
            // 如果没有列表数据，只显示当前资质
            this.allQualifications = [currentQualification]
            this.currentViewIndex = 0
          }

          // 重置文件索引
          this.currentFileIndex = 0

          // 初始化PDF源（如果当前文件是PDF）- 参照receive.vue的实现
          this.$nextTick(() => {
            if (this.currentFile && this.isPdf(this.currentFile.fileUrl)) {
              this.handleViewFile(this.currentFile.fileUrl)
            }
          })

          this.imageViewerVisible = true
        } else {
          throw new Error(currentQualificationResponse?.msg || '获取资质详情失败')
        }

        loading.close()
      } catch (error) {
        console.error('获取资质详情失败:', error)
        this.$message.error('获取资质详情失败')
        if (loading) loading.close()
      }
    }

  }
}
</script>

<style lang="scss" scoped>
/* 通用工具类 */
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 图片查看器样式 */
:deep(.image-viewer-dialog) {
  max-height: 95vh;
  min-height: 92vh;
}

.image-viewer-dialog :deep(.el-dialog) {
  max-height: 95vh;
  height: auto;
  min-height: 92vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.image-viewer-dialog :deep(.el-dialog__header) {
  flex-shrink: 0;
}

.image-viewer-dialog :deep(.el-dialog__body) {
  padding: 0;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  max-height: calc(95vh - 90px);
}

.image-viewer-container {
  display: flex;
  height: 100%;
  min-height: 500px;
  max-height: calc(95vh - 90px);
  overflow: hidden;
}

/* 左侧资质导航 */
.qualification-aside {
  border: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  flex-shrink: 0;
}

.aside-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #606266;
  font-size: 16px;
}

.header-title {
  color: #303133;
  font-size: 15px;
  font-weight: 600;
  margin-right: 12px;
}

.header-stats {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.stats-text {
  color: #606266;
  font-size: 12px;
  background-color: #f0f2f5;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #dcdfe6;
}

.aside-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: 100%;
}

/* 自定义滚动条样式 */
.aside-content::-webkit-scrollbar {
  width: 6px;
}

.aside-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.aside-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.aside-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 资质列表项样式 */
.qualification-item {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.qualification-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background-color: #f0f2f5;
  transition: opacity 0.3s ease;
}

.qualification-item:last-child::after {
  display: none;
}

.qualification-item.active::after {
  left: 24px;
  opacity: 0.5;
}

.qualification-item:hover {
  background-color: #f8f9fa;
  transform: translateX(2px);
}

.qualification-item.active {
  background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
  border-left: 4px solid #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.item-content {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  gap: 12px;
}

.item-icon {
  color: #909399;
  font-size: 16px;
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.qualification-item.active .item-icon {
  color: #409eff;
}

.item-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  transition: color 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}

.qualification-item.active .item-name {
  color: #409eff;
  font-weight: 600;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 60px;
  flex-shrink: 0;
}

.count-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 20px;
}

.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 20px;
}

.file-count-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
  height: 20px;
  line-height: 18px;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qualification-item.active .file-count-tag {
  background-color: #e6f7ff;
  color: #409eff;
  border-color: #b3d8ff;
}

.item-arrow {
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 右侧预览区域 */
.preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.preview-area.full-width {
  width: 100%;
}

.preview-header {
  padding: 12px 16px;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  background-color: white;
  flex-shrink: 0;
}

.title-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.qualification-info h3 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
}

/* 标题行布局 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

/* 左侧标题和标签容器 */
.title-and-tags {
  flex: 1;
}

/* 资质导航容器 */
.qualification-navigation {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-label {
  font-size: 14px;
  color: #606266;
  font-weight: 600;
  margin-right: 8px;
}

.page-info {
  font-size: 14px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

.info-tags {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  min-height: 24px;
}

.info-tags .create-time,
.info-tags .public-time {
  font-size: 12px;
  color: #8c8c8c;
  height: 22px;
  line-height: 22px;
  display: flex;
  align-items: center;
}

.file-type-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
  background-color: #f8f9fa;
  padding: 3px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  height: 22px;
  line-height: 1;
}

.file-stat .el-icon {
  font-size: 14px;
}

.file-stat:nth-child(1) {
  color: #67c23a;
  border-color: #c2e7b0;
  background-color: #f0f9ff;
}

.file-stat:nth-child(1) .el-icon {
  color: #67c23a;
}

.file-stat:nth-child(2) {
  color: #e6a23c;
  border-color: #f5dab1;
  background-color: #fdf6ec;
}

.file-stat:nth-child(2) .el-icon {
  color: #e6a23c;
}

.file-stat:nth-child(3) {
  color: #909399;
  border-color: #d3d4d6;
  background-color: #f4f4f5;
}

.file-stat:nth-child(3) .el-icon {
  color: #909399;
}

.preview-content {
  flex: 1;
  overflow: auto;
  min-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #e4e7ed;
}

.file-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.file-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.file-info-section {
  flex: 1;
}

.file-navigation .file-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
  margin-bottom: 2px;
}

.file-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.file-count {
  font-size: 12px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  font-weight: 500;
}

.file-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

/* 文件导航按钮样式 */
.file-nav-button {
  transition: all 0.3s ease;
}

.file-nav-button:hover:not(.is-disabled) {
  transform: scale(1.1);
}

.current-file-preview {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}





.image-container {
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 300px);
  background-color: #fafafa;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  cursor: pointer;
}

.preview-image :deep(.el-image__inner) {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  font-size: 14px;
  padding: 40px;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.pdf-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
}

.pdf-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.pdf-container iframe {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.other-file,
.no-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  gap: 15px;
}

.file-icon,
.no-file-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.other-file p,
.no-file p {
  margin: 0;
  font-size: 14px;
}

.preview-actions {
  padding: 12px 16px;
  background-color: white;
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-shrink: 0;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
}

.action-btn {
  min-width: 120px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 全屏图片预览器样式 */
:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__img) {
  max-width: 90vw !important;
  max-height: 90vh !important;
  object-fit: contain !important;
}

:deep(.el-image-viewer__close) {
  background-color: rgba(0, 0, 0, 0.5) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  transition: background-color 0.2s ease !important;
}

:deep(.el-image-viewer__close:hover) {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-viewer-container {
    flex-direction: column;
  }

  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .navigation-controls {
    align-self: flex-end;
  }
}

/* 自定义上传组件样式 */
.upload-demo :deep(.el-upload-list--picture-card) {
  --el-upload-list-picture-card-size: 120px;
}

.custom-file-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.file-thumbnail {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #f8f9fa;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pdf-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.other-file-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
}

.pdf-icon,
.file-icon {
  font-size: 32px;
  margin-bottom: 4px;
}

.file-type-text {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.file-actions {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 5;
  pointer-events: none;
}

/* 当只有一个按钮时，确保居中显示 */
.file-actions.single-action {
  gap: 0;
}

.custom-file-item:hover .file-actions {
  opacity: 1;
}

.action-icon {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  z-index: 10;
}

.action-icon:hover {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.file-name-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
  color: white;
  padding: 12px 6px 6px;
  font-size: 11px;
  line-height: 1.2;
  text-align: center;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.custom-file-item:hover .file-name-overlay {
  opacity: 0.8;
}

/* 上传区域样式优化 */
.upload-demo :deep(.el-upload--picture-card) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-demo :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 上传按钮图标大小与文件缩略图一致 */
.upload-plus-icon {
  font-size: 32px;
  color: #8c939d;
  transition: all 0.3s ease;
}

.upload-demo :deep(.el-upload--picture-card:hover) .upload-plus-icon {
  color: #409eff;
  transform: scale(1.1);
}

.upload-demo :deep(.el-upload-list__item) {
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

/* 确保操作按钮在最顶层 */
.upload-demo :deep(.el-upload-list__item-actions) {
  display: none !important;
}

/* 为不同文件类型的操作按钮提供更好的对比度 */
.pdf-thumbnail .action-icon,
.other-file-thumbnail .action-icon {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 2px solid rgba(255, 255, 255, 1);
}

.pdf-thumbnail .action-icon:hover,
.other-file-thumbnail .action-icon:hover {
  background: rgba(255, 255, 255, 1);
  color: #000;
  transform: scale(1.15);
}
</style>
