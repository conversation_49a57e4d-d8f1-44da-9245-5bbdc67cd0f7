import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/sys/sysOrgUser/save',
      inputForm
    )
  },
	configOrgUser: function (userId,orgIds) {
		return http.get(
			'/sys/sysOrgUser/configOrgUser',
			{userId: userId,orgIds:orgIds}
		)
	},
  delete: function (ids) {
    return http.delete(
      '/sys/sysOrgUser/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/sysOrgUser/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/sys/sysOrgUser/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/sys/sysOrgUser/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/sys/sysOrgUser/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/sys/sysOrgUser/import',
      data
    )
  }
}
