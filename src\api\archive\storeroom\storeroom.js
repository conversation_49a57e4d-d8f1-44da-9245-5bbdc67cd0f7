import http from "@/utils/request"

export default {
	// 查询主表
	treeData: function (params) {
		return http.get(
			'/archive/warehouse/info/treeData',
			params
		)
	},
	//查询可视化数据
	queryVisual: function (params) {
		return http.get(
			'/archive/warehouse/details/queryVisual',
			params
		)
	},
	//根据id查询库
	queryById: function (params) {
		return http.get(
			'/archive/warehouse/details/queryById',
			params
		)
	},
	// 查询仓库列表
	detailsList: function (params) {
		return http.get(
			'/archive/warehouse/details/list',
			params
		)
	},
	list: function (params) {
		return http.get(
			'/archive/warehouse/list',
			params
		)
	},
	// 查询档案室列表
	roomList: function (params) {
		return http.get(
			'/archive/warehouse/room/list',
			params
		)
	},
	// 保存仓库信息
	submitSave: function (data) {
		return http.post(
			'/archive/warehouse/details/save',
			data
		)
	},
	// 保存主表信息
	hostSave: function (data) {
		return http.post(
			'/archive/warehouse/info/save',
			data
		)
	},
	// 保存档案室信息
	roomSave: function (data) {
		return http.post(
			'/archive/warehouse/room/save',
			data
		)
	},
	// 保存档案柜信息
	containerSave: function (data) {
		return http.post(
			'/archive/warehouse/container/save',
			data
		)
	},
	//根据id查询档案室
	roomQueryById: function (params) {
		return http.get(
			'/archive/warehouse/room/queryById',
			params
		)
	},
	//根据id查询档案室
	containerById: function (params) {
		return http.get(
			'/archive/warehouse/container/queryById',
			params
		)
	},
	//查询档案室所属档案柜
	containerList: function (params) {
		return http.get(
			'/archive/warehouse/container/list',
			params
		)
	},
	// 保存档案架信息
	storageSave: function (data) {
		return http.post(
			'/archive/warehouse/storage/save',
			data
		)
	},
	//查询档案架
	storageList: function (params) {
		return http.get(
			'/archive/warehouse/storage/list',
			params
		)
	},
	//  删主表
	deleteInfo: function (params) {
		return http.delete(
			'/archive/warehouse/info/delete',
			params
		)
	},
	//  查询档案架结构数据
	getStoragesByContainerId: function (params) {
		return http.get(
			'/archive/warehouse/storage/getStoragesByContainerId',
			params
		)
	},
	//设置档案架子
	updateStorageInfoList: function (params) {
		return http.get(
			'/archive/warehouse/storage/updateStorageInfoList',
			params
		)
	},
}
