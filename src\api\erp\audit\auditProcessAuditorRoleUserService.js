import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/audit/auditProcessAuditorRoleUser/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/audit/auditProcessAuditorRoleUser/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/audit/auditProcessAuditorRoleUser/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/audit/auditProcessAuditorRoleUser/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/audit/auditProcessAuditorRoleUser/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/audit/auditProcessAuditorRoleUser/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/audit/auditProcessAuditorRoleUser/import',
      data
    )
  }
}
