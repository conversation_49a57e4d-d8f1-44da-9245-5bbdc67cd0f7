import http from "@/utils/request"

/*
 *@description: 部门管理
 *@author: 路正宁
 *@date: 2023-03-17 11:33:36
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
      '/sys/office/save',
      inputForm
    )
  },
  list: function (params) {
    return http.get(
       '/sys/office/list',
       params
    )
  },
  delete: function (ids) {
    return http.delete(
      '/sys/office/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/sys/office/queryById',
      {id: id}
    )
  },

  treefunction () {
    return http.get(
      '/sys/office/treeData'
    )
  }
}
