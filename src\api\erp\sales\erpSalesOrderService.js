import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/sales/erpSalesOrder/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/sales/erpSalesOrder/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/sales/erpSalesOrder/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrder/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/sales/erpSalesOrder/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/sales/erpSalesOrder/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/sales/erpSalesOrder/import',
      data
    )
  }
}
