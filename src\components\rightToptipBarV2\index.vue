<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-23 15:15:29
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-12 16:46:19
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\components\rightToptipBar\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div style="display: flex;justify-content: end;padding-right: 10px;align-items:center">
        <el-tooltip class="box-item" effect="dark" content="刷新" placement="top">
            <el-icon style="font-size: 20px;margin-right: 7px;color: #2a76f8;cursor: pointer;" @click="refreshClick">
                <Refresh />
            </el-icon>
        </el-tooltip>
        <el-tooltip class="box-item" effect="dark" content="全屏" placement="top">
            <el-icon style="font-size: 18px;margin-right: 7px;color: #2a76f8;cursor: pointer;" @click="fullScreenClick">
                <FullScreen />
            </el-icon>
        </el-tooltip>
        <!-- <el-tooltip class="box-item" effect="dark" content="显隐列" placement="top">
            <el-icon style="font-size: 19px;margin-right: 7px;color: #2a76f8;cursor: pointer;" @click="transferCol">
                <Menu />
            </el-icon>

        </el-tooltip> -->
        <el-dialog v-model="dialogVisible" v-if="dialogVisible" title="表格列" width="40%"
            :before-close="() => dialogVisible = false">
            <div style="margin:0 auto" v-loading="loading">
                <el-transfer v-model="checkList" :data="copyList" :props="{ key: 'prop' }" :titles="['显示列', '隐藏列']"
                    @change="handleChange1" />
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>

    </div>
</template>

<script setup>
import { FullScreen, Menu, Refresh } from '@element-plus/icons-vue'
import { reactive, ref, getCurrentInstance, toRefs, defineProps, defineEmits, onMounted, watch, onBeforeMount } from 'vue'
import tool from '@/utils/tool';
import tableConfigApi from '@/api/erp/table/config'
import emitter from '@/utils/bus'
const { proxy } = getCurrentInstance();
const emit = defineEmits()
const checkList = ref([])
const dialogVisible = ref(false)
const loading = ref(false)
const id = ref(null)
const list = ref([])
const copyList = ref([])
const props = defineProps({
    className: {
        type: String,
        default: ''
    }
})
const { className } = toRefs(props)
watch(() => dialogVisible.value, (newValue, oldValue) => {
    if (dialogVisible.value) {
        checkList.value = []
        list.value.forEach(v => {
            if (v.isShow) {
                checkList.value.push(v.prop)
            }
        })
    }
})

const transferCol = () => {
    getTableColunms()
    dialogVisible.value = true
}
const handleChange1 = (arr, position) => {
    checkList.value = arr
}
const saveTableColunms = () => {
    tableConfigApi.save({ userId: tool.data.get("USER_INFO").id, formKey: className.value, formValue: JSON.stringify(list.value), id: id.value }).then(res => {
        if (res.code == 200) {
            proxy.msgSuccess('保存成功')
            dialogVisible.value = false
            emitter.emit("getColunms", res => {

            })
        } else {
            proxy.msgError('保存失败')
        }
    })

}
const handleSubmit = () => {
    list.value.forEach(v => {
        if (checkList.value.includes(v.prop)) {
            v.isShow = false
        } else {
            v.isShow = true
        }
    })
    saveTableColunms()

}
const refreshClick = () => {
    emit("handleRefresh");
}
const fullScreenClick = () => {
    tool.screen(document.querySelectorAll('.' + className.value)[0])
}
// 获取config数据
const getTableColunms = () => {
    loading.value = true
    list.value = []
    checkList.value = []
    copyList.value = []
    tableConfigApi.queryById({ userId: tool.data.get("USER_INFO").id, formKey: className.value }).then(res => {
        if (res.code == 200) {
            if (res?.data?.records?.[0]) {
                list.value = JSON.parse(res?.data?.records?.[0]?.formValue)
                id.value = res?.data?.records?.[0]?.id
                copyList.value = list.value.filter((x) => (x.type != 'selection' && x.type != 'operate'))
                copyList.value.forEach((item) => {
                    if (item.isShow === false) {
                        checkList.value.push(item.prop)

                    } else {
                        item.isShow = true
                    }
                })
                loading.value = false
            }
        }
    })
}
</script>

<style lang="scss" scoped>
::v-deep .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>