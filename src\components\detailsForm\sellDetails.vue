<template>
	<el-dialog v-model="dialogTableVisible" title="查看详情" width="80%">

		<div class="demo-collapse">
			<el-collapse v-model="activeNames">
				<h4
					slot="title"
					class="stateTitle"
				>
					{{ echo1(data.editStrs.orderHeader) }}
				</h4>
				<el-collapse-item name="1">
					<template #title>
						<h3>表头信息</h3>
					</template>
					<el-form
						:disabled="true"
						:inline="true"
						:model="formInline1"
						class="demo-form-inline"
						label-position="right"
						label-width="95px"
						style="overflow-x:auto"
					>
						<div class="formBox">
							<el-form-item label="客户" prop="n1">
								<el-select
									v-model="formInline1.n1"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
								</el-select>
							</el-form-item>
							<el-form-item label="客户代表" prop="n2">
								<el-select
									v-model="formInline1.n2"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>

								</el-select>
							</el-form-item>
							<el-form-item label="结算方式" prop="n3">
								<el-select
									v-model="formInline1.n3"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(
										item, index
									) in data.clearingFormType"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="收款期限" prop="n4">
								<el-date-picker
									v-model="formInline1.n4"
									placeholder=" "
									style="width: 100%;min-width:100px"
									type="datetime"
								/>
							</el-form-item>
							<el-form-item label="发票类型" prop="n5">
								<el-select
									v-model="formInline1.n5"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(item, index) in data.invoice"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="业务类型" prop="n6">
								<el-select
									v-model="formInline1.n6"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(item, index) in data.business"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="物流方式" prop="n7">
								<el-select
									v-model="formInline1.n7"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(item, index) in data.logistics"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="三方物流" prop="n8">
								<el-select
									v-model="formInline1.n8"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(
										item, index
									) in data.TripartiteLogistics"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="收货地址" prop="n9">
								<el-cascader
									v-model="formInline1.n9"
									:options="regionData"
									placeholder=" "
									size="default"
									style="width: 100%;min-width:100px"
								>
								</el-cascader>
							</el-form-item>
							<el-form-item label="详细地址" prop="n10">
								<el-input
									v-model="formInline1.n10"
									style="width: 100%;min-width:100px"
								/>
							</el-form-item>
							<el-form-item label="收货人" prop="n11">
								<el-input
									v-model="formInline1.n11"
									style="width: 100%;min-width:100px"
								/>
							</el-form-item>
							<el-form-item label="收货人电话" prop="n12">
								<el-input
									v-model="formInline1.n12"
									style="width: 100%;min-width:100px"
								/>
							</el-form-item>
							<el-form-item label="库号" prop="n13">
								<el-select
									v-model="formInline1.n13"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>

								</el-select>
							</el-form-item>
							<el-form-item label="经手人" prop="n14">
								<el-select
									v-model="formInline1.n14"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
								</el-select>
							</el-form-item>
							<el-form-item
								:prop="data.myFlag ? 'n15' : ''"
								label="自营扣率"
							>
								<el-select
									v-model="formInline1.n15"
									class="m-2"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(item, index) in data.discount"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="制单人" prop="n16">
								<el-input
									v-model="formInline1.n16.name"
									disabled
									style="width: 100%;min-width:100px"
								/>
							</el-form-item>


							<el-form-item label="折扣金额">
								<el-input v-model="formInline1.n19"
										  disabled
										  style="width: 100%;min-width:100px"/>
							</el-form-item>

							<el-form-item label="合同模板" prop="n21">
								<el-select
									v-model="formInline1.n21"
									placeholder=" "
									style="width: 100%;min-width:100px"
								>
									<el-option
										v-for="(item, index) in data.contract"
										:key="index"
										:label="item.name"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="备注">
								<el-input
									v-model="formInline1.n17"
									style="width: 100%;min-width:100px"
								/>
							</el-form-item
							>

						</div>
						<el-form-item label=" ">
							<el-checkbox
								v-model="formInline1.n20"
								label="是否开具发票"
							/>
						</el-form-item>
						<el-form-item>
							<el-checkbox
								v-model="formInline1.n22"
								label="是否生成合同"
							/>
						</el-form-item>
					</el-form>
				</el-collapse-item>
				<el-collapse-item name="2">
					<template #title>
						<h3>细单信息</h3>
					</template>

					<el-table
						:cell-style="{ textAlign: 'center' }"
						:data="addGoods.allGoods"
						:header-cell-style="{ 'text-align': 'center' }"
						border
						style="width: 100%"
					>
						<el-table-column
							fixed
							label="序号"
							prop="tag"
							width="80px"
						>
							<template #default="scope">
								{{ scope.$index + 1 }}
							</template>
						</el-table-column>
						<el-table-column
							label="自编码"
							prop="commodity.commoditySelfCode"
							width="120px"
						/>
						<el-table-column
							label="商品名称"
							prop="commodity.tradeName"
							width="120px"
						/>
						<el-table-column
							label="规格"
							prop="commodity.packageSpecification"
							width="120px"
						/>
						<el-table-column
							label="产地"
							prop="commodity.originPlace"
							width="120px"
						/>
						<el-table-column
							label="单位"
							prop="commodity.basicUnit"
							width="120px"
						/>
						<el-table-column
							label="有效期(月)"
							prop="commodity.validityTime"
							width="120px"
						/>
						<el-table-column
							fixed="right"
							label="数量"
							prop="date"
							width="150px"
						>
							<template #default="scope">
								<p style="width: 100%">
									<span>{{ scope.row.num.str }}</span>
								</p>
							</template>
						</el-table-column>
						<el-table-column
							fixed="right"
							label="单价"
							prop="date"
							width="150px"
						>
							<template #default="scope">
								<p style="width: 100%">
									<span>
										{{ scope.row.price.str.toFixed(2) }}
									</span>
								</p>
							</template>
						</el-table-column>
						<el-table-column
							fixed="right"
							label="单价合计金额"
							prop="date"
							width="120px"
						>
							<template #default="scope">
								{{ scope.row.total }}
							</template>
						</el-table-column>
						<el-table-column
							label="入库数量"
							prop="intoQuantity"
							width="120px"
						/>
						<el-table-column
							label="库存余量"
							prop="inventoryBalance"
							width="120px"
						/>
						<el-table-column
							label="可开数量"
							prop="openableQuantity"
							width="120px"
						/>
						<el-table-column
							label="件装量"
							prop="commodity.ratio"
							width="120px"
						/>
						<!--							TODO:比率/数量-->
						<el-table-column
							label="件数"
							prop="pieceNumber"
							width="120px"
						></el-table-column>
						<el-table-column
							label="最后一次销售价格"
							prop="lastSalesPrice"
							width="140px"
						/>
						<el-table-column
							label="类型"
							prop="commodity.grugsType"
							width="120px"
						/>
						<el-table-column
							label="生产厂家"
							prop="manufacture.enterpriseName"
							width="120px"
						/>
						<el-table-column
							label="供应商"
							prop="supplier.enterpriseName"
							width="120px"
						/>
						<el-table-column
							label="批准文号"
							prop="approvalNumber"
							width="120px"
						/>
						<el-table-column
							label="税率"
							prop="commodity.taxRate"
							width="120px"
						/>
						<el-table-column
							label="剂型"
							prop="commodity.dosageForm"
							width="120px"
						/>
						<el-table-column
							label="入库时间"
							prop="intoTime"
							width="150px"
						>
							<template #default="scope">
								{{ transformTimestamp(scope.row.intoTime) }}
							</template>
						</el-table-column>

						<el-table-column
							label="成本单价"
							prop="unitPrice"
							width="120px"
						/>

						<el-table-column
							label="贮藏温区"
							prop="commodity.storageTemperature"
							width="120px"
						/>
						<el-table-column
							label="批号"
							prop="batchNumber"
							width="120px"
						/>
						<el-table-column
							label="生产日期"
							prop="produceDate"
							width="150px"
						>
							<template #default="scope">
								{{ transformTimestamp(scope.row.produceDate) }}
							</template>
						</el-table-column>
						<el-table-column
							label="过期时间"
							prop="validityTime"
							width="120px"
						>
							<template #default="scope">
								{{ transformTimestamp(scope.row.validityTime) }}
							</template>
						</el-table-column>
					</el-table>
				</el-collapse-item>
				<el-collapse-item name="3">
					<template #title>
						<h3>合计信息</h3>
					</template>
					<span style="margin-left: 50px; font-size: 15px"
					>合计数量：<span style="color: red; font-size: 19px">{{
							footForm.num
						}}</span></span
					>
					<span style="margin-left: 50px; font-size: 15px"
					>合计金额：<span style="color: red; font-size: 19px">{{
							footForm.price
						}}</span></span
					>
					<span style="margin-left: 50px; font-size: 15px"
					>折后总金额：<span
						style="color: red; font-size: 19px"
					>{{ footForm.discount }}</span
					></span
					>
				</el-collapse-item>
				<el-collapse-item name="4">
					<template #title>
						<h3>订单配置</h3>
					</template>
					<el-form
						:disabled="true"
						:inline="true"
						:model="formInline2"
						class="demo-form-inline"
						label-position="right"
						label-width="110px"
					>
						<el-form-item
							label="盖章选项"
							label-width="85px"
							prop="n1"
						>
							<el-select
								v-model="formInline2.n1"
								class="m-2"
								placeholder=" "
								style="width: 199px"
							>
								<el-option
									v-for="(item, index) in data.stamp"
									:key="index"
									:label="item.name"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item
							label="货主章选项"
							label-width="95px"
							prop="n2"
						>
							<el-select
								v-model="formInline2.n2"
								class="m-2"
								placeholder=" "
								style="width: 200px"
							>
								<el-option
									v-for="(item, index) in data.owner"
									:key="index"
									:label="item.name"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						<el-form-item
							label="装货选项"
							label-width="85px"
							prop="n3"
						>
							<el-select
								v-model="formInline2.n3"
								class="m-2"
								placeholder=" "
								style="width: 200px"
							>
								<el-option
									v-for="(item, index) in data.encasement"
									:key="index"
									:label="item.name"
									:value="item.value"
								/>
							</el-select>
						</el-form-item
						>
						<br/>
						<el-form-item label="是否拼箱" prop="n4">
							<el-radio-group v-model="formInline2.n4">
								<el-radio :label="false">否</el-radio>
								<el-radio :label="true">是</el-radio>
							</el-radio-group>
						</el-form-item
						>
						<br/>
						<el-form-item label="单据返回选项" prop="n5">
							<el-checkbox-group v-model="formInline2.n5">
								<el-checkbox
									v-for="(item, index) in data.receipts"
									:key="index"
									:label="item.value"
								>{{ item.name }}
								</el-checkbox
								>
							</el-checkbox-group>
						</el-form-item
						>
						<br/>
						<el-form-item label="留存资料选项" prop="n6">
							<el-radio-group v-model="formInline2.n6">
								<el-radio label="0">非装箱联全部保留</el-radio>
								<el-radio label="1">不保留</el-radio>
							</el-radio-group>
						</el-form-item
						>
						<br/>
						<el-form-item label="质检报告" prop="n7">
							<el-checkbox-group v-model="formInline2.n7">
								<el-checkbox
									v-for="(item, index) in data.testing"
									:key="index"
									:label="item.value"
								>{{ item.name }}
								</el-checkbox
								>
							</el-checkbox-group>
						</el-form-item
						>
						<br/>

						<el-form-item label="质量单据返回" prop="n8">
							<el-checkbox-group v-model="formInline2.n8">
								<el-checkbox
									v-for="(item, index) in data.return"
									:key="index"
									:label="item.value"
								>{{ item.name }}
								</el-checkbox
								>
							</el-checkbox-group>
						</el-form-item
						>
						<br/>
						<el-form-item label="收集随货资料" prop="n9">
							<el-checkbox-group v-model="formInline2.n9">
								<el-checkbox
									v-for="(item, index) in data.gather"
									:key="index"
									:label="item.value"
								>{{ item.name }}
								</el-checkbox
								>
							</el-checkbox-group>
						</el-form-item
						>
						<br/>
						<el-form-item label="备注">
							<el-input
								v-model="formInline2.n10"
								style="width: 770px"
							/>
						</el-form-item>
					</el-form>
				</el-collapse-item>
				<el-collapse-item name="5">
					<template #title>
						<h3>操作日志</h3>
					</template>
					<LogQuery ref="logQueryRef"/>
				</el-collapse-item>
				<el-collapse-item v-if="!data.formFlag" name="6">
					<template #title>
						<h3>审批意见</h3>
					</template>
					<auditForms ref="auditRef" @refresh="refresh"/>
				</el-collapse-item>
			</el-collapse>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">
					取消
				</el-button>
				<el-button v-if="!data.formFlag" type="primary" @click="allRight()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import {regionData} from "element-china-area-data";
import {ElLoading} from "element-plus";
import {manageApi} from "@/api/model/salesManagement";
import LogQuery from "@/components/detailsForm/logQuery.vue";

const dialogTableVisible = ref(false);
const activeNames = ref(["1"]);

const logQueryRef = ref(null);
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const auditRef = ref(null);
const emit = defineEmits(["refresh"]);

const formInline1 = reactive({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	n6: "",
	n7: "",
	n8: "",
	n9: "",
	n10: "",
	n11: "",
	n12: "",
	n13: "",
	n14: "",
	n15: "",
	n16: "",
	n17: "",
	n18: "",
	n19: 0,
	n20: false,
	n21: "",
	n22: false,
	n23: "",
	n24: "",
	n25: "",
});
const formInline2 = reactive({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: [],
	n6: "",
	n7: [],
	n8: [],
	n9: [],
	n10: "",
});
const footForm = reactive({
	num: 0,
	price: 0,
	discount: 0,
});
const refresh = () => {
	dialogTableVisible.value = false;
	emit("refresh");
};
const data = reactive({
	ids: "",
	title: "",
	tableList: [],
	formFlag: false,
	deliveryList: [],
	editStrs: {
		orderHeader: {
			status: 0,
		},
	},
	pageNum: 1,
	pageSize: 10,
	total: 0,
	clearingFormType: null,
	invoice: null,
	logistics: null,
	business: null,
	TripartiteLogistics: null,
	contract: null,
	clientType: {
		value: "",
		total: 0,
		pageSize: 5,
		pageNum: 1,
		type: null,
	},
	powerType: null,
	sysAreas: null,
	serial: {
		value: "",
		total: 0,
		pageSize: 5,
		pageNum: 1,
		type: null,
	},
	handle: null,
	myFlag: false,
	discount: null,
	user: null,
	stamp: null,
	owner: null,
	encasement: null,
	receipts: null,
	datum: null,
	testing: null,
	gather: null,
	return: null,
});
const allRight = () => {
	auditRef.value.formSub(data.ids);
};

function transformTimestamp(timestamp) {
	let a = new Date(timestamp).getTime();
	const date = new Date(a);
	const Y = date.getFullYear() + "-";
	const M =
		(date.getMonth() + 1 < 10
			? "0" + (date.getMonth() + 1)
			: date.getMonth() + 1) + "-";
	const D =
		(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
	// const h =
	// 	(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
	// const m =
	// 	date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
	// const s = date.getSeconds(); // 秒
	const dateString = Y + M + D;
	// console.log('dateString', dateString); // > dateString 2021-07-06 14:23
	return dateString;
}

const addGoods = reactive({
	value: "",
	total: 0,
	pageSize: 10,
	pageNum: 1,
	addList: null,
	tableData: [],
	shops: "",
	allGoods: [],
	delList: [],
});
const {proxy} = getCurrentInstance();
const details = (id, ids, mess) => {
	ElLoading.service();
	if (mess) {
		data.formFlag = true
	} else {
		data.formFlag = false
	}
	data.ids = ids;
	manageApi.detailManage({salesOrderId: id}).then((res) => {
		if (res.code == 200) {
			console.log(res);
			addGoods.allGoods = res.data.orderFormList;
			addGoods.allGoods.forEach((item) => {
				item.commodity.originPlace = item.originPlace;
				item.commodity.basicUnit = item.basicUnit;
				item.commodity.ratio = item.unitLoading;
				item.supplier.enterpriseName = item.supplierName;
				item.commodity.taxRate = item.taxRate;
				item.commodity.storageTemperature = item.storageTemperature;
				item.commodity.batchNumber = item.approvalNumber;
				item.commodity.grugsType = item.grugsType;
				item.commodity.validityTime = item.validityTime;
				item.validityTime = item.expirationTime;
				item.price = {
					flag: false,
					str: item.unitPrice ? item.unitPrice : 0,
				};
				item.num = {
					flag: false,
					str: item.quantity ? item.quantity : 0,
				};
				item.manufacture = item.manufacturer;
				item.batchNumber = item.batchNum;
				item.total = item.amountMoney ? item.amountMoney : 0;
				item.unitPrice = item.costUnitPrice;
			});
			data.editStrs = res.data;
			formInline2.n1 = res.data.orderConfig.stampOption;
			formInline2.n2 = res.data.orderConfig.cargoOwnerOption;
			formInline2.n3 = res.data.orderConfig.packingOption;
			formInline2.n4 = res.data.orderConfig.isConsolidation;
			formInline2.n5 =
				res.data.orderConfig.receiptReturnOption.split(",");
			formInline2.n6 = res.data.orderConfig.retainedOption;
			formInline2.n7 =
				res.data.orderConfig.inspectionReportOption.split(",");
			formInline2.n8 =
				res.data.orderConfig.qualityReceiptReturn.split(",");
			formInline2.n9 = res.data.orderConfig.followGenOption.split(",");
			formInline2.n10 = res.data.orderConfig.remark;
			footForm.price = res.data.orderHeader.totalAmount;
			footForm.discount = res.data.orderHeader.totalAmountAfterDiscount;
			footForm.num = res.data.orderHeader.totalQuantity;
			formInline1.n1 = res.data.orderHeader.customer.enterpriseName;
			formInline1.n2 = res.data.orderHeader.delegate.delegateName;
			formInline1.n3 = res.data.orderHeader.settlementMethod;
			formInline1.n4 = res.data.orderHeader.collectionPeriod;
			formInline1.n5 = res.data.orderHeader.invoiceType;
			formInline1.n6 = res.data.orderHeader.businessType;
			formInline1.n7 = res.data.orderHeader.logisticsMode;
			formInline1.n8 = res.data.orderHeader.thirdPartyOgistics;
			formInline1.n9 = res.data.orderHeader.sendAddressCode.split(",");
			formInline1.n10 = res.data.orderHeader.sendAddressDetail;
			formInline1.n11 = res.data.orderHeader.consignee;
			formInline1.n12 = res.data.orderHeader.consigneePhone;
			formInline1.n13 = res.data.orderHeader.warehouseNumber.warehouseNumber;
			formInline1.n15 = res.data.orderHeader.selfRate;
			formInline1.n16 = res.data.orderHeader.preparedBy;
			formInline1.n17 = res.data.orderHeader.remark;
			formInline1.n19 = Number(res.data.orderHeader.discountAmount);
			formInline1.n20 = res.data.orderHeader.isInvoice;
			formInline1.n21 = res.data.orderHeader.contractTemplate;
			formInline1.n22 = res.data.orderHeader.isCreateContract;
			footForm.price = res.data.orderHeader.totalAmount;
			footForm.num = res.data.orderHeader.totalQuantity;
			footForm.discount = res.data.orderHeader.totalAmountAfterDiscount;
			formInline1.n14 = res.data.orderHeader.handledBy.name;

			dialogTableVisible.value = true;
		}
		const loadingInstance = ElLoading.service();
		loadingInstance.close();
	});
};
const echo1 = (row) => {
	let n = "";
	if (row.status == "0") {
		n = "草稿";
	} else if (row.status == "1") {
		n = "待审核";
	} else if (row.status == "2") {
		n = "审核中";
	} else if (row.status == "3") {
		n = "审核通过";
	} else if (row.status == "4") {
		n = "驳回";
	} else if (row.status == "5") {
		n = "撤销";
	} else {
		n = "";
	}
	return n;
};
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')

	if (localStorage.getItem('orderType')) {
		let arrType = JSON.parse(localStorage.getItem('orderType'))
		for (let key in arrType) {
			data[key] = arrType[key];
		}
	} else {
		data.clearingFormType = await proxy.getDictList("erp_clearingForm");
		data.invoice = await proxy.getDictList("erp_invoice");
		data.logistics = await proxy.getDictList("erp_logistics");
		data.business = await proxy.getDictList("erp_business");
		data.TripartiteLogistics = await proxy.getDictList(
			"erp_TripartiteLogistics"
		);
		data.contract = await proxy.getDictList("erp_contract");
		data.discount = await proxy.getDictList("erp_discount");
		data.stamp = await proxy.getDictList("erp_stamp");
		data.owner = await proxy.getDictList("erp_owner");
		data.encasement = await proxy.getDictList("erp_encasement");
		data.receipts = await proxy.getDictList("erp_receipts");
		data.testing = await proxy.getDictList("erp_testing");
		data.gather = await proxy.getDictList("erp_gather");
		data.return = await proxy.getDictList("erp_return");
		localStorage.setItem("orderType", JSON.stringify(data))
	}
	data.user = JSON.parse(localStorage.getItem("USER_INFO")).content;
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	details,
});
</script>
<style lang="scss" scoped>
::v-deep .el-input.is-disabled .el-input__wrapper {
	background: none !important;
	box-shadow: none !important;
	color: #000 !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
	-webkit-text-fill-color: #000 !important; //修改输入框文字颜色
}

::v-deep .el-input__suffix-inner {
	display: none !important;
}

.formBox {
	width: 100%;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
}

.stateTitle {
	position: absolute;
	font-size: 15px;
	top: 21px;
	right: 53px;
}
</style>
