import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/customer/erpCustomersLicence/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/customer/erpCustomersLicence/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/customer/erpCustomersLicence/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/customer/erpCustomersLicence/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/customer/erpCustomersLicence/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/customer/erpCustomersLicence/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/customer/erpCustomersLicence/import',
      data
    )
  }
}
