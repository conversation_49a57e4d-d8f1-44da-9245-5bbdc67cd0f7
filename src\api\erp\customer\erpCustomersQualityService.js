import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/customer/erpCustomersQuality/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/customer/erpCustomersQuality/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/customer/erpCustomersQuality/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/customer/erpCustomersQuality/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/customer/erpCustomersQuality/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/customer/erpCustomersQuality/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/customer/erpCustomersQuality/import',
      data
    )
  }
}
