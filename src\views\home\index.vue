<template>
	<div class="containerBox">
		<div class="headerBox">
			<div class="top_tab">
				<el-row :gutter="10">
					<el-col :lg="4" :md="8" :sm="8" :xl="4" :xs="24" style="margin-top: 5px;margin-bottom: 5px;">
						<el-card class="top_tab_item tap_data_1 tab_header">
							<template #header>
								<div class="item_tab_top">
									<div class="item_top_left">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>档案数量</el-text>
										</div>
									</div>
									<el-select v-model="recordIndex" size="small" style="width: 58px;"
											   @change="getRecordCount">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<div class="item_box">
								<div class="item_num">
									<scrollNumber :color="'#FFFFFF'" :endVal="Number(recordCount)" :fontSize="'36px'"
												  :startVal="0"/>
								</div>
							</div>
						</el-card>
					</el-col>
					<el-col :lg="4" :md="8" :sm="8" :xl="4" :xs="24" style="margin-top: 5px;margin-bottom: 5px;">
						<el-card class="top_tab_item tap_data_2 tab_header">
							<template #header>
								<div class="item_tab_top">
									<div class="item_top_left">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>档案入库</el-text>
										</div>
									</div>
									<el-select v-model="storageIndex" size="small" style="width: 58px;"
											   @change="getStorageCount">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<div class="item_box">
								<div class="item_num">
									<scrollNumber :color="'#FFFFFF'" :endVal="Number(storageCount)" :fontSize="'36px'"
												  :startVal="0"/>
								</div>
							</div>
						</el-card>
					</el-col>
					<el-col :lg="4" :md="8" :sm="8" :xl="4" :xs="24" style="margin-top: 5px;margin-bottom: 5px;">
						<el-card class="top_tab_item tap_data_3 tab_header">
							<template #header>
								<div class="item_tab_top">
									<div class="item_top_left">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>档案借阅</el-text>
										</div>
									</div>
									<el-select v-model="borrowingIndex" size="small" style="width: 58px;"
											   @change="getBorrowCount">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<div class="item_box">
								<div class="item_num">
									<scrollNumber :color="'#FFFFFF'" :endVal="Number(borrowingCount)"
												  :fontSize="'36px'"
												  :startVal="0"/>
								</div>
							</div>
						</el-card>
					</el-col>
					<el-col :lg="4" :md="8" :sm="8" :xl="4" :xs="24" style="margin-top: 5px;margin-bottom: 5px;">
						<el-card class="top_tab_item tap_data_4 tab_header">
							<template #header>
								<div class="item_tab_top">
									<div class="item_top_left">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>档案鉴定</el-text>
										</div>
									</div>
									<el-select v-model="checkIndex" size="small" style="width: 58px;"
											   @change="getCheckCount">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<div class="item_box">
								<div class="item_num">
									<scrollNumber :color="'#FFFFFF'" :endVal="Number(checkCount)" :fontSize="'36px'"
												  :startVal="0"/>
								</div>
							</div>
						</el-card>
					</el-col>
					<el-col :lg="4" :md="8" :sm="8" :xl="4" :xs="24" style="margin-top: 5px;margin-bottom: 5px;">
						<el-card class="top_tab_item tap_data_5 tab_header">
							<template #header>
								<div class="item_tab_top">
									<div class="item_top_left">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>档案修复</el-text>
										</div>
									</div>
									<el-select v-model="repairIndex" size="small" style="width: 58px;"
											   @change="getRepairCount">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<div class="item_box">
								<div class="item_num">
									<scrollNumber :color="'#FFFFFF'" :endVal="Number(repairCount)" :fontSize="'36px'"
												  :startVal="0"/>
								</div>
							</div>
						</el-card>
					</el-col>
					<el-col :lg="4" :md="8" :sm="8" :xl="4" :xs="24" style="margin-top: 5px;margin-bottom: 5px;">
						<el-card class="top_tab_item tap_data_6 tab_header">
							<template #header>
								<div class="item_tab_top">
									<div class="item_top_left">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>档案销毁</el-text>
										</div>
									</div>
									<el-select v-model="delIndex" size="small" style="width: 58px;"
											   @change="getDelCount">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<div class="item_box">
								<div class="item_num">
									<scrollNumber :color="'#FFFFFF'" :endVal="Number(delCount)" :fontSize="'36px'"
												  :startVal="0"/>
								</div>
							</div>
						</el-card>
					</el-col>
				</el-row>
			</div>
		</div>
		<div class="bodyBox">
			<el-row :gutter="10">
				<el-col :lg="24" :md="24" :sm="24" :xl="16" :xs="24" style="padding-top: 5px;padding-bottom: 5px;
					height: 100%;width: 100%">
					<div style="height: 100%;width: 100%;display: flex;flex-direction: column;align-items: stretch;
						flex-wrap: nowrap;">
						<el-card body-style="padding: 12px 10px 10px 10px;" class="top_tab_item">
							<template #header>
								<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-items: center;
								justify-content: space-between;">
									<div class="item_top">
										<img alt="" class="item_top_img" src="../../assets/home/<USER>">
										<div class="item_top_text">
											<el-text>接收数据</el-text>
										</div>
									</div>
									<el-select v-model="receiveIndex" size="small" style="width: 58px;"
											   @change="getReceiveData">
										<el-option label="本月" value="1"/>
										<el-option label="上月" value="2"/>
										<el-option label="本年" value="3"/>
										<el-option label="全部" value="4"/>
									</el-select>
								</div>
							</template>
							<el-divider style="margin: 0 0 5px 0;"/>
							<div class="dataChartBox">
								<div ref="line" class="line"></div>
							</div>
						</el-card>
						<div style="display: flex;gap: 10px;height: 318px">
							<el-card body-style="padding: 12px 10px 10px 10px;" class="top_tab_item">
								<template #header>
									<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-items: center;
									justify-content: space-between;">
										<div class="item_top">
											<img alt="" class="item_top_img" src="../../assets/home/<USER>">
											<div class="item_top_text">
												<el-text>收集数据</el-text>
											</div>
										</div>
										<el-select v-model="collectIndex" size="small" style="width: 58px;"
												   @change="getCollectData">
											<el-option label="本月" value="1"/>
											<el-option label="上月" value="2"/>
											<el-option label="本年" value="3"/>
											<el-option label="全部" value="4"/>
										</el-select>
									</div>
								</template>
								<el-divider style="margin: 0"/>
								<div class="rankingBox">
									<div class="rankingBoxHeader">
										<p>门类</p>
										<p>数量</p>
									</div>
									<div class="rankingBoxContent">
										<div v-for="(item,index) in collectMap" :key="index" class="rankingBoxItem">
											<el-tooltip :content="item.name" class="box-item" effect="dark"
														placement="top">
												<span>{{ item.name }}</span>
											</el-tooltip>
											<el-progress :format="() => item.count" :percentage="item.process"
														 class="progressStyle"/>
										</div>
									</div>
								</div>
							</el-card>
							<el-card body-style="padding: 12px 10px 10px 10px;" class="top_tab_item">
								<template #header>
									<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-items: center;
									justify-content: space-between;">
										<div class="item_top">
											<img alt="" class="item_top_img" src="../../assets/home/<USER>">
											<div class="item_top_text">
												<el-text>归档数据</el-text>
											</div>
										</div>
										<el-select v-model="archiveIndex" size="small" style="width: 58px;"
												   @change="getArchiveData">
											<el-option label="本月" value="1"/>
											<el-option label="上月" value="2"/>
											<el-option label="本年" value="3"/>
											<el-option label="全部" value="4"/>
										</el-select>
									</div>
								</template>
								<el-divider style="margin: 0"/>
								<div class="rankingBox">
									<div class="rankingBoxHeader">
										<p>门类</p>
										<p>数量</p>
									</div>
									<div class="rankingBoxContent">
										<div v-for="(item,index) in archiveMap" :key="index" class="rankingBoxItem">
											<el-tooltip :content="item.name" class="box-item" effect="dark"
														placement="top">
												<span>{{ item.name }}</span>
											</el-tooltip>
											<el-progress :format="() => item.count" :percentage="item.process"
														 class="progressStyleTwo"/>
										</div>
									</div>
								</div>
							</el-card>
						</div>
					</div>
				</el-col>
				<el-col :lg="24" :md="24" :sm="24" :xl="8" :xs="24" style="padding-top: 5px;padding-bottom: 5px;
					height: 100%;width: 100%">
					<div style="height: 100%;width: 100%;">
						<el-row :gutter="10" style="row-gap: 10px;">
							<el-col :lg="12" :md="12" :sm="24" :xl="24" :xs="24">
								<el-card body-style="padding: 12px 10px 10px 10px;height: 320px" class="top_tab_item">
									<template #header>
										<div class="item_top">
											<img alt="" class="item_top_img" src="../../assets/home/<USER>">
											<div class="item_top_text">
												<el-text>待办事项</el-text>
											</div>
										</div>
									</template>
									<el-divider style="margin: 0 0 5px 0;"/>
									<el-scrollbar height="300px" style="padding-right: 12px">
										<el-space direction="vertical" fill
												  style="width: 100%;height: 100%;padding-top: 9px">
											<div v-for="item in doForList" :key="item.id" class="scrollbar-demo-item"
												 @click="DoForJump(item)">
												<div>
													<el-text class="mouseTitle">{{ item.title }}</el-text>
													<el-text>{{ item.sendTime }}</el-text>
												</div>
												<el-divider border-style="hidden" style="margin: 10px 0 0 0"/>
											</div>
										</el-space>
									</el-scrollbar>
								</el-card>
							</el-col>
							<el-col :lg="12" :md="12" :sm="24" :xl="24" :xs="24">
								<el-card body-style="padding: 12px 10px 10px 10px;height: 269px" class="top_tab_item">
									<template #header>
										<div class="item_top">
											<img alt="" class="item_top_img" src="../../assets/home/<USER>">
											<div class="item_top_text">
												<el-text>公告通知</el-text>
											</div>
										</div>
									</template>
									<el-divider style="margin: 0 0 10px 0;"/>
									<div v-if="noticeList.length > 0" class="noticeOne" style="cursor: pointer;" @click="viewNotice(noticeList[0])">
										<div class="noticeTime">
											<div>
												<span>
													{{ moment(noticeList[0].createDate).format('YYYY-MM') }}
												</span>
											</div>
											<span style="font-size: 18px;line-height: 33px;">
												{{ moment(noticeList[0].createDate).format('DD') }}
											</span>
										</div>
										<div class="rightTitle">
											<div class="firstTitle mouseTitle">{{ noticeList[0].noticeTitle }}</div>
											<div class="secondTitle ellipsis">{{ noticeList[0].noticeContent }}</div>
										</div>
									</div>
									<el-scrollbar height="300px" style="padding-top: 10px;">
										<div v-for="(item, index) in noticeList" :key="index">
											<div v-if="index !== 0" class="scrollbar-demo-item"
												 @click="viewNotice(item)">
												<p class="mouseTitle" style="text-align: left;">
													{{ item.noticeTitle }}
												</p>
												<p>
													{{
														item.updateDate
															? moment(item.updateDate).format('YYYY-MM-DD HH:mm:ss')
															: moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')
													}}
												</p>
											</div>
											<el-divider border-style="hidden" style="margin: 5px 0"/>
										</div>
									</el-scrollbar>
								</el-card>
							</el-col>
						</el-row>
					</div>
				</el-col>
			</el-row>
		</div>

		<el-dialog v-if="noticeOpen" v-model="noticeOpen" :before-close="() => noticeOpen = false"
				   :title="noticeValue.noticeType === '1' ? '公告' : '通知'" align-center width="35%">
			<div>
				<p class="noticeTitle">{{ noticeValue.noticeTitle }}</p>
				<p class="noticeMsg">
					<span>发布人：{{ noticeValue.createBy ? noticeValue.createBy.name : '--' }}</span>
					<span>
						发布时间：
						{{
							noticeValue.updateDate
								? moment(noticeValue.updateDate).format('YYYY-MM-DD HH:mm:ss')
								: moment(noticeValue.createDate).format('YYYY-MM-DD HH:mm:ss')
						}}
					</span>
				</p>
				<p class="noticeContent">{{ noticeValue.noticeContent }}</p>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import {getCurrentInstance, markRaw, onBeforeMount, onMounted, ref} from 'vue'
import sysNoticeService from '@/api/sys/sysNoticeService'
import homeApi from '@/api/sys/home'
import moment from 'moment'
import systemApi from '@/api/model/system'
import router from "@/router";
import scrollNumber from '@/utils/scrollNumber'
import {ElDivider} from "element-plus";
import * as echarts from 'echarts';

const {proxy} = getCurrentInstance();
const noticeList = ref([])
const noticeOpen = ref(false)
const noticeValue = ref({})
// 代办事项
const doForList = ref({})
//档案数量参数
const recordIndex = ref('4')
const recordCount = ref('0')
//档案入库参数
const storageIndex = ref('4')
const storageCount = ref('0')
//档案借阅参数
const borrowingIndex = ref('4')
const borrowingCount = ref('0')
//档案鉴定参数
const checkIndex = ref('4')
const checkCount = ref('0')
//档案修复参数
const repairIndex = ref('4')
const repairCount = ref('0')
//档案销毁参数
const delIndex = ref('4')
const delCount = ref('0')
//档案接收参数
const receiveIndex = ref('1')
//档案收集参数
const collectIndex = ref('4')
const collectMap = ref([])
//档案归档参数
const archiveIndex = ref('4')
const archiveMap = ref([])

// 归还提醒
const tableData = ref([])
const line = ref(null)
const chartLine = ref(null)

onBeforeMount(() => {
	//获取档案数据
	getRecordCount(recordIndex.value);
	getStorageCount(storageIndex.value);
	getBorrowCount(borrowingIndex.value);
	getCheckCount(checkIndex.value);
	getRepairCount(repairIndex.value);
	getDelCount(delIndex.value);
	getCollectData(collectIndex.value);
	getArchiveData(archiveIndex.value);
	//获取系统数据
	getListNotice();
	getMessageList();
	getList();
})

onMounted(() => {
	//获取图标数据
	chartLine.value = markRaw(echarts.init(line.value))
	window.addEventListener("dark_change", () => {
		getReceiveData(receiveIndex.value)
	})
	window.onresize = () => {
		chartLine.value.resize()
	};
	getReceiveData(receiveIndex.value);
})

//获取档案数量
function getRecordCount(val) {
	homeApi.infoTime({
		dataType: 1,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			recordCount.value = res.data;
		}
	})
}

//获取入库数量
function getStorageCount(val) {
	homeApi.infoTime({
		dataType: 2,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			storageCount.value = res.data;
		}
	})
}

//获取借阅数量
function getBorrowCount(val) {
	homeApi.getHomePageData({
		dataType: 1,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			borrowingCount.value = res.data;
		}
	})
}

//获取鉴定数量
function getCheckCount(val) {
	homeApi.getHomePageData({
		dataType: 2,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			checkCount.value = res.data;
		}
	})
}

//获取修复数量
function getRepairCount(val) {
	homeApi.getHomePageData({
		dataType: 3,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			repairCount.value = res.data;
		}
	})
}

//获取销毁数量
function getDelCount(val) {
	homeApi.infoTime({
		dataType: 3,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			delCount.value = res.data;
		}
	})
}

//获取接收数量
function getCollectData(val) {
	homeApi.rankingCountByTime({
		dataType: 1,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			collectMap.value = res.data;
			const numArr = []
			collectMap.value.forEach(item => {
				numArr.push(Number(item.count))
			})
			let maxNum = Math.max.apply(null, numArr);
			collectMap.value.forEach(item => {
				item.process = Number(Number(item.count) / maxNum.toFixed(2)) * 100
			})
		}
	})
}

//获取归档数量
function getArchiveData(val) {
	homeApi.rankingCountByTime({
		dataType: 2,
		timeType: val
	}).then(res => {
		if (res.code == 200) {
			archiveMap.value = res.data;
			const numArr = []
			archiveMap.value.forEach(item => {
				numArr.push(Number(item.count))
			})
			let maxNum = Math.max.apply(null, numArr);
			archiveMap.value.forEach(item => {
				item.process = Number(Number(item.count) / maxNum.toFixed(2)) * 100
			})
		}
	})
}

//获取接收数据
function getReceiveData(val) {
	try {
		chartLine.value?.clear();
		homeApi.countByTime({
			type: val,
		}).then(res => {
			if (res.code == 200) {
				let data = {
					xAxisData: res.data.dateList, // 日期
					legendData: res.data.seriesData.length > 0 ? res.data.seriesData.map(item => item.name) : [], // 分类名称
					seriesData: res.data.seriesData.length > 0 ? res.data.seriesData.map(item => {
						return {
							name: item.name,
							type: 'bar',
							stack: '总量',
							barGap: "0.5px",
							barWidth: 20,
							data: item.data
						}
					}) : [],
					maxYValue: 100 // Y轴最大值，可以根据实际数据动态计算
				};
				init(data);
			}
		}).catch((err) => {
			console.log(err);
		})
	} catch (error) {
		proxy.msgError(error)
	}
}

//获取公告&通知
function getListNotice() {
	sysNoticeService.list({status: '2'}).then(res => {
		if (res.code == 200) {
			noticeList.value = res.data.records;
		}
	})
}

// 查看公告
function viewNotice(val) {
	noticeValue.value = val;
	noticeOpen.value = true;
}

//获取消息
const getMessageList = () => {
	systemApi.getMessageList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code == 200) {
			doForList.value = res.data.records;
		}
	})
}

const option = (data) => {
	let back_color = {}
	if (localStorage.getItem('APP_DARK')) {
		back_color = JSON.parse(localStorage.getItem('APP_DARK') || {})
	}
	back_color = back_color.content ? "#1d1e1f" : '#ffffff';
	return {
		backgroundColor: back_color,
		title: {show: false},
		tooltip: {
			trigger: "axis",
			confine: true,
			padding: 0,
			enterable: true,
			appendToBody: true,
			backgroundColor: "rgba(255,255,255,0.9)",
			formatter: function (params) {
				let tooltipHtml = `<div style="width:auto;padding:8px;background:none;border-radius:6px;z-index: 99999">
					<p style="font-size: 14px;font-weight:bold;color: #333;margin:0 0 5px 0;">${params[0].axisValueLabel}</p>`;
				params.forEach(item => {
					tooltipHtml += `<div style="display:flex; align-items:center; margin-bottom: 1px;">
						<span style="display:inline-block;margin-right:5px;border-radius:10px;width:8px;height:8px;background-color:${item.color};"></span>
						<span style="font-size: 10px; color: #555;">${item.seriesName}: </span>
						<span style="font-size: 10px; color: #333; font-weight:bold; margin-left: 5px;">${item.value} 个</span>
					</div>`;
				});
				tooltipHtml += `</div>`;
				return tooltipHtml;
			}
		},
		legend: {
			right: "10%",
			top: 12,
			itemGap: 16,
			itemWidth: 10,
			itemHeight: 10,
			textStyle: {
				color: "#414957",
				fontStyle: "normal",
				fontFamily: "微软雅黑",
				fontSize: 12,
			},
			show: false
		},
		grid: {
			left: "2%",
			right: "5%",
			bottom: "10%"
		},
		xAxis: [
			{
				type: "category",
				data: data.xAxisData, // 修改为从数据中获取X轴数据
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: "#AEAEAE"
					}
				},
				axisLabel: {
					show: true,
					interval: "0",
					textStyle: {
						lineHeight: 16,
						padding: [2, 2, 0, 2],
						height: 50,
						fontSize: 12,
					},
					color: "#687284",
					formatter: function (params, index) {
						if (receiveIndex.value == '1' || receiveIndex.value == '2') {
							if (index % 3 === 0) {
								return moment(params).format('MM-DD')
							}
							return ''
						} else {
							return params
						}
					}
				},
			}
		],
		yAxis: [
			{
				name: "数量(个)",
				type: "value",
				nameLocation: 'end',
				min: 0,
				// max: data.maxYValue, // 可以根据数据动态设置Y轴最大值
				nameTextStyle: {
					padding: [0, -20, 0, 0],
					color: '#9B9B9B'
				},
				position: "right",
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: true,
					color: "#687284"
				},
				splitLine: { // Y轴网格线
					show: true,
					lineStyle: {
						color: '#E6E6E6',
						type: 'dashed'
					}
				}
			},
		],
		series: data.seriesData, // 修改为从数据中获取系列数据
	}
}

const init = (data) => {
	chartLine.value.setOption(option(data));
}

function getList() {
	homeApi.expireInfo().then(res => {
		if (res.code === 200) {
			tableData.value = res.data;
		}
	})
}

// 代办事项跳转
function DoForJump(val) {
	if (val.type == 3) {
		// 3 借阅审批
		router.replace({path: "/borrowingApproval"});
	} else if (val.type == 4) {
		// 4 鉴定审批
		router.replace({path: "/approve"});
	}
}

</script>

<style lang="scss" scoped>
.noticeTitle {
	font-size: 18px;
	color: #333;
	font-weight: bold;
	text-align: center;
	width: 50%;
	margin: 0 auto;
	padding-bottom: 20px
}

.noticeMsg {
	display: flex;
	width: 80%;
	margin: 0 auto;
	justify-content: space-between;
	font-size: 12px;
	color: #666;
	align-items: center;
	padding-bottom: 20px
}

.noticeContent {
	padding: 0 30px 30px 20px;
	line-height: 24px;
	font-size: 14px;
	color: #333;
	text-indent: 28px
}

.main {
	height: 100%;
	width: 100%;
	padding: 10px;
}

.top_tab {
	height: 100%;
	width: 100%;
}

.top_tab_item {
	background-color: #fff;
	border-radius: 10px;
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	align-items: stretch;
	margin-bottom: 10px;

	.item_box {
		height: 100%;
		width: 100%;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: flex-start;
		align-items: stretch;

		.item_num {
			color: #333;
			font-weight: bold;
			display: flex;
			justify-content: center;
			grid-column-gap: 8px;
			align-items: center;
		}
	}
}

.tab_header {
	:deep(.el-card__header) {
		padding: 20px 25px 0 25px;
	}
}

.tap_data_1 {
	background: url(../../assets/home/<USER>
	background-size: 101% 101%;
}

.tap_data_2 {
	background: url(../../assets/home/<USER>
	background-size: 101% 101%;
}

.tap_data_3 {
	background: url(../../assets/home/<USER>
	background-size: 101% 101%;
}

.tap_data_4 {
	background: url(../../assets/home/<USER>
	background-size: 101% 101%;
}

.tap_data_5 {
	background: url(../../assets/home/<USER>
	background-size: 101% 101%;
}

.tap_data_6 {
	background: url(../../assets/home/<USER>
	background-size: 101% 101%;
}

:deep(.marginTop10) {
	margin-top: 10px;
}

:deep(.marginLeft10) {
	margin-left: 10px;
}

.leftBottom {
	height: 100%;
	width: 100%;
	display: flex;
	column-gap: 10px;
	justify-items: stretch;
	justify-content: space-evenly;
	align-items: stretch;
}

.leftBottomAfter {
	width: 100%;
	height: 100%;
	display: grid;
	grid-template-columns: 1fr 1fr;
	grid-column-gap: 10px;
	justify-items: stretch;
	align-items: stretch;
}

.el-divider--horizontal {
	margin: 19px 0 10px 0;
}

.active {
	background-color: #2a76f8;
	color: #fff;
}

// 进度条
.percentage-value {
	display: block;
	margin-top: 5px;
	font-size: 28px;
}

.percentage-label {
	display: block;
	margin-top: 10px;
	font-size: 12px;
}

.dataChartBox {
	display: grid;
	grid-template-columns: 1fr;
	grid-template-rows: auto;
	align-items: stretch;
	align-content: stretch;
	justify-content: center;
	width: 100%;
	height: 300px;
}

.scrollbar-demo-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	flex-wrap: wrap;
	text-align: center;
	border-radius: 4px;
	cursor: pointer;
	width: 100%;
	height: 100%;

	div {
		display: flex;
		justify-content: space-between;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;
		width: 100%;
	}
}

.notice {
	padding: 10px 0;
}

.noticeOne {
	display: flex;
	// align-items: center;
	// justify-content: space-between;
}

.noticeTime {
	width: 73px;
	height: 55px;
	box-shadow: 0 2px 9px 0 rgba(45, 74, 234, 0.11);
	text-align: center;
	font-family: Microsoft YaHei,serif;
	font-weight: 400;
	color: #2A76F8;

	div {
		height: 24px;
		background-color: #4199FF;

		span {
			font-size: 14px;
			font-family: Microsoft YaHei,serif;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 24px;
		}
	}
}

.rightTitle {
	margin-left: 15px;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: space-evenly;
}

.firstTitle {
	font-size: 16px;
	font-family: Microsoft YaHei,serif;
	font-weight: 400;
	color: #333333;
}

.secondTitle {
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #666666;
}

.ellipsis {
	overflow: hidden; //超出文本隐藏
	text-overflow: ellipsis; ///超出部分省略号显示
	display: -webkit-box; //弹性盒模型
	-webkit-box-orient: vertical; //上下垂直
	-webkit-line-clamp: 2;
}

:deep(.el-table__row) {
	height: 25px;
}

.mouseTitle:hover {
	color: #2A76F8;
}

.mouseTitle {
	color: #666666;
}

:deep(.el-card__body) {
	width: 100%;
	height: 100%;
}

.item_tab_top {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	justify-content: space-between;

	.item_top_left {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;

		.item_top_img {
			Object-fit: contain;
			height: 100%;
			width: 18px;
		}

		.item_top_text {
			margin-left: 12px;
			display: flex;
			align-items: center;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: center;
		}
	}

	:deep(.el-select__wrapper) {
		color: #FFFFFF;
		background-color: rgba(255, 255, 255, 0.2);
		box-shadow: none !important;
	}

	:deep(.el-select__input) {
		color: #FFFFFF;
	}

	:deep(.el-select__caret) {
		color: #FFFFFF;
	}

	:deep(.el-select__placeholder) {
		color: #FFFFFF;
	}

	:deep(.el-text) {
		color: #FFFFFF;
	}
}

.item_top {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	justify-content: flex-start;

	.item_top_text {
		margin-left: 9px;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-content: center;
	}

	.item_top_img {
		Object-fit: contain;
		height: 100%;
		width: 22px;
	}
}

.el-progress-circle {
	height: 100%;
	width: 100%;
}

.boxFull {
	width: 100%;
	height: 100%;
}

.containerBox {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: flex-start;
}

.headerBox {
	width: 100%;
	height: auto;
	background: none;
	padding: 5px 10px 0 10px;
}

.bodyBox {
	width: 100%;
	height: 83%;
	background: none;
	padding: 0 10px 10px 10px;
}

.rankingBox {
	width: 100%;
	height: 100%;
	padding: 10px 20px;

	.rankingBoxHeader {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-column-gap: 10px;

		p {
			font-size: 14px;
			font-family: Microsoft YaHei,serif;
			font-weight: 400;
			color: #333333;
		}
	}

	.rankingBoxContent {
		display: flex;
		flex-direction: column;
		flex-wrap: nowrap;
		align-items: stretch;
		margin-top: 13px;
		gap: 5px;

		.rankingBoxItem {
			display: grid;
			grid-template-columns: 1fr 1fr;
			grid-column-gap: 10px;

			.progressStyle {
				:deep(.el-progress-bar__inner) {
					background-color: #FFA04C;
				}

				:deep(.el-progress-bar__outer) {
					height: 8px !important;
				}
			}

			.progressStyleTwo {
				:deep(.el-progress-bar__inner) {
					background-color: #56FB97;
				}

				:deep(.el-progress-bar__outer) {
					height: 8px !important;
				}
			}

			span {
				font-size: 12px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #666666;
			}
		}
	}
}
</style>
