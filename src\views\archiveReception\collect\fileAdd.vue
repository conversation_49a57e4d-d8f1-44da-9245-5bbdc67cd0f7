<!--
 * @Author: saya
 * @Date: 2023-07-24 13:45:13
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-08-22 09:33:37
 * @FilePath: \archive-manage-front\src\views\archiveReception\collect\archivesEdit.vue
 * @Description:
-->
<template>
	<el-dialog v-if="props.addOpen" v-model="props.addOpen" :close-on-click-modal="false" :close-on-press-escape="false"
			   :title="props.title" align-center append-to-body destroy-on-close width="83%"
			   @close="cancellation">
		<el-container>
			<el-main ref="main" v-loading="loading" class="noPadding">
				<el-tabs v-model="activeName" :before-leave="isBefore">
					<el-tab-pane label="档案信息" name="first">
						<el-main class="noPadding">
							<el-form ref="formRef" :inline="true" :model="archivesData" :rules="rules"
									 label-width="auto">
								<el-form-item label="档案名称:" prop="name" style="width: 388px;">
									<el-input v-model="archivesData.name" clearable placeholder="请输入档案名称"/>
								</el-form-item>
								<el-form-item label="档案年份:" prop="year" style="width: 388px;">
									<el-date-picker v-model="archivesData.year" clearable format="YYYY" placeholder="请选择档案年份"
													style="width: 100%;" type="year"/>
								</el-form-item>
								<el-form-item label="档案月份:" prop="month" style="width: 388px;">
									<el-date-picker v-model="archivesData.month" clearable format="MM"
													placeholder="请选择档案月份"
													style="width: 100%;" type="month"/>
								</el-form-item>
								<el-form-item label="保留年限:" prop="retentionPeriod" style="width: 388px;">
									<el-select v-model="archivesData.retentionPeriod" clearable placeholder="请选择保留年限"
											   style="width: 100%;">
										<el-option label="永久" value="Y"/>
										<el-option label="5年" value="D5"/>
										<el-option label="10年" value="D10"/>
										<el-option label="20年" value="D20"/>
										<el-option label="30年" value="D30"/>
									</el-select>
								</el-form-item>
								<el-form-item label="保密密级:" prop="protectLevel" style="width: 388px;">
									<el-select v-model="archivesData.protectLevel" clearable placeholder="请选择保密密级"
											   style="width: 100%;">
										<el-option label="公开" value="GK"/>
										<el-option label="限制" value="KZ"/>
										<el-option label="秘密" value="MOM"/>
										<el-option label="机密" value="JM"/>
										<el-option label="绝密" value="UM"/>
									</el-select>
								</el-form-item>
								<el-form-item label="控制等级:" prop="controlStatus" style="width: 388px;">
									<el-select v-model="archivesData.controlStatus" clearable placeholder="请选择控制等级"
											   style="width: 100%;" @change="controlGroupChange">
										<el-option label="公开" value="1"/>
										<el-option label="公司内部开放" value="2"/>
										<el-option label="部门内部开放" value="3"/>
										<el-option :disabled="!archivesData?.org?.id" label="控制" value="4">
											<el-text v-if="archivesData?.org?.id">控制</el-text>
											<el-tooltip v-else content="请先选择组织机构" effect="dark"
														placement="right">
												<el-text type="info">控制</el-text>
											</el-tooltip>
										</el-option>
									</el-select>
								</el-form-item>
								<el-form-item v-if="archivesData.controlStatus === '4'" label="可查看人员:"
											  prop="ruleUserId"
											  style="width: 388px;">
									<el-select v-model="archivesData.ruleUserId"
											   clearable
											   collapse-tags
											   collapse-tags-tooltip
                                               multiple
											   placeholder="请选择可查看人员"
											   style="width: 100%;">
										<el-option v-for="item in userByOrg" :key="item.id" :label="item.name"
												   :value="item.id"/>
									</el-select>
								</el-form-item>
								<el-form-item label="所属机构:" prop="org.id" style="width: 388px;">
									<el-select v-model="archivesData.org.id" clearable placeholder="请选择所属机构"
											   style="width: 100%;"
											   @change="departmentList">
										<el-option v-for="item in institution" :key="item.id" :label="item.name"
												   :value="item.id"/>
									</el-select>
								</el-form-item>
								<el-form-item label="所属部门:" prop="office.id" style="width: 388px;">
									<el-select v-model="archivesData.office.id" clearable placeholder="请选择所属部门"
											   style="width: 100%;">
										<el-option v-for="item in department" :key="item.id" :label="item.name"
												   :value="item.id"/>
									</el-select>
								</el-form-item>
								<el-form-item label="是否全电文档:" prop="isElectronic" style="width: 388px;">
									<el-select v-model="archivesData.isElectronic" clearable
											   placeholder="请选择是否全电文档"
											   style="width: 100%;" @change="changeStatus">
										<el-option label="否" value="0"/>
										<el-option label="是" value="1"/>
									</el-select>
								</el-form-item>
								<el-form-item label="档案标签:" prop="tagManagerInfo">
									<div class="tag-selection-container">
										<el-button plain style="width: 200px" type="primary" @click="openTagsChose = true">选择标签</el-button>
                                        <!-- 已选择标签的回显 -->
                                        <div v-if="selectedTags.length > 0" class="selected-tags">
                                            <template v-for="tag in tagsList">
                                                <el-tag
                                                    v-if="selectedTags.includes(tag.id)"
                                                    :key="tag.id"
                                                    :effect="judgeTheme(tag.tagTheme).type"
                                                    :round="judgeShape(tag.tagRound).type"
                                                    :type="judgeColor(tag.tagColor).type"
                                                    class="selected-tag"
                                                    closable
                                                    @close="removeTag(tag.id)"
                                                >
                                                    {{ tag.tagName }}
                                                </el-tag>
                                            </template>

                                        </div>
									</div>

									<tagsListDialog v-if="openTagsChose" :choseTagsId="archivesData.tagManagerInfo"
													:dialogIsOpen="openTagsChose"
													@choseTagMethodReturn="choseTagMethodReturn"></tagsListDialog>
								</el-form-item>
								<el-form-item label="档案摘要:" prop="digestContent" style="width: 96.8%;">
									<el-input v-model="archivesData.digestContent" clearable
											  maxlength="500"
											  placeholder="请输入档案摘要" show-word-limit style="width: 100%;" type="textarea"/>
								</el-form-item>
								<el-form-item label="备注:" prop="remark" style="width: 96.8%;">
									<el-input v-model="archivesData.remark" clearable maxlength="100"
											  placeholder="请输入备注" show-word-limit style="width: 100%;" type="textarea"/>
								</el-form-item>
							</el-form>
						</el-main>
					</el-tab-pane>
					<el-tab-pane label="数据信息" name="second">
						<div style="padding:0 0 15px 0;font-weight: 600;">{{ '基本信息' }}</div>
						<el-form ref="formBasic" v-loading="open" :inline="true" :model="dataConfigList"
								 label-position="right" label-width="auto">
							<div style="width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr;justify-items: center;">
								<div v-for="(item, index) in dataConfigList.filter(item => !item.detailChildren)"
									 :key="index" style="width: 100%;">
									<el-form-item v-if="item.detailType === '1'"
												  :label="item.detailRemark"
												  :prop="item.detailRemark">
										<el-input v-model="item['detailValue']" :placeholder="item.detailRemark"
												  clearable
												  style="width: 100%;"/>
									</el-form-item>
									<el-form-item v-if="item.detailType === '2'"
												  :label="item.detailRemark"
												  :prop="item.detailRemark">
										<el-upload
											:accept="'image/gif, image/jpeg, image/png'"
											:action="uploadUrl"
											:headers='headers'
											:on-success="(res, file, fileList) => item.detailValue = res.data.url"
											:show-file-list="false"
											class="avatar-uploader">
											<img v-if="item.detailValue" :src="item.detailValue"
												 alt="" class="avatar"
												 style="height: 30px;width: 30px;border: 1px solid ghostwhite">
											<el-icon v-else class="avatar-uploader-icon">
												<Plus/>
											</el-icon>
										</el-upload>
									</el-form-item>
									<el-form-item v-if="item.detailType === '4'"
												  :label="item.detailRemark"
												  :prop="item.detailRemark">
										<el-date-picker v-model="item['detailValue']"
														:placeholder="'请选择' + item.detailRemark"
														format="YYYY-MM-DD"
														style="width: 100%;"
														type="date"
														value-format="YYYY-MM-DD"/>
									</el-form-item>
									<el-form-item v-if="item.detailType === '5'"
												  :label="item.detailRemark"
												  :prop="item.detailRemark">
										<el-radio-group v-model="item['detailValue']"
														style="width: 100%;">
											<el-radio label="是"/>
											<el-radio label="否"/>
										</el-radio-group>
									</el-form-item>
								</div>
							</div>
							<div v-for="(item, index) in dataConfigList.filter(item => item.detailChildren)"
								 :key="index">
								<el-divider style="margin: 14px 0 16px 0;width: 100%"/>
								<div style="display: flex;justify-content: space-between;align-items: center;
									margin-bottom: 18px">
									<h4 style="font-weight: 600;">{{ item.detailRemark }}</h4>
									<div v-if="props?.infoAddId?.name !== '人员档案'">
										<el-button plain type="primary" @click="addOtherOneInfo(item)">
											添加
										</el-button>
									</div>
								</div>
								<el-divider style="margin: 14px 0 16px 0;width: 100%"/>
								<RecursiveInfoForm :childrenInfo="item"/>
							</div>
						</el-form>
					</el-tab-pane>
					<el-tab-pane label="附件" name="three">
						<el-container>
							<el-main>
								<el-upload v-model:file-list="fileList" :action="uploadUrl" :headers='headers'
										   :on-progress="uploadVideoProcess"
                                           :before-remove="beforeRemove"
										   :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList)"
										   class="upload-demo"
										   drag
										   multiple>
									<el-icon size="22">
										<upload-filled/>
									</el-icon>
									<div class="el-upload__text">
										将文件拖到此处，或<em>点击上传</em>
									</div>
								</el-upload>
							</el-main>
						</el-container>
					</el-tab-pane>
				</el-tabs>
			</el-main>
		</el-container>
		<template #footer>
			<div v-if="activeName === 'three'" style="float: right;margin-left: 20px;">
				<el-button :loading="loading" type="primary" @click="collect">确定</el-button>
			</div>
			<div v-if="activeName !== 'three'" style="float: right;margin-left: 20px;">
				<el-button type="primary" @click="nextStep">下一步</el-button>
			</div>
			<div v-if="activeName !== 'first'" style="float: right;margin-left: 20px;">
				<el-button type="primary" @click="nextBack">上一步</el-button>
			</div>
			<div style="float: right;">
				<el-button plain @click="cancellation">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import tool from '@/utils/tool';
import moment from "moment/moment";
import sysOrgService from "@/api/model/sys/sysOrgService";
import tagsManagement from '@/api/archive/tagsManagement';
import {Plus, UploadFilled} from '@element-plus/icons-vue';
import {node} from "@/api/model/systemDeploy/auditAndFlow";
import sysOfficeService from "@/api/model/sys/sysOfficeService";
import collectList from '@/api/archive/archiveReception/collect';
import tagsListDialog from "@/views/archiveReception/common/tagsListDialog.vue";
import {defineProps, getCurrentInstance, reactive, ref, watch} from 'vue';
import RecursiveInfoForm from "@/views/archiveReception/collect/recursiveInfoForm.vue";

const {proxy} = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const props = defineProps({
	title: {
		type: String
	},
	addOpen: {
		type: Boolean
	},
	chooseMenu: {
		type: Object,
		default: () => {
			return {}
		}
	},
	infoAddId: {
		type: Object,
		default: () => {
			return {}
		}
	},
})
//页面加载
const loading = ref(true);
// 表选选择状态
const openTagsChose = ref(false);
//表单校验
const rules = reactive({
	name: [
		{required: true, message: '请输入档案名称', trigger: 'blur'},
		{
			validator: async (rule, value, callback) => {
				await handleQuery(value);
				if (receiveData.value.length > 0) {
					callback(new Error('此名称已有重复, 请重新输入!'));
				} else {
					callback();
				}
			}
		}
	],
	digestContent: [
		{required: true, message: '请输入档案摘要', trigger: 'blur'},
	],
	year: [
		{required: true, message: '请选择档案年份', trigger: 'change'},
		{
			validator: async (rule, value, callback) => {
				await handleQuery(archivesData.value.name);
				if (receiveData.value.length <= 0) {
					proxy.$refs["formRef"]?.clearValidate('name');
				}
				callback();
			}
		}
	],
	month: [
		{required: true, message: '请输入档案月份', trigger: 'change'},
		{
			validator: async (rule, value, callback) => {
				await handleQuery(archivesData.value.name);
				if (receiveData.value.length <= 0) {
					proxy.$refs["formRef"]?.clearValidate('name');
				}
				callback();
			}
		}
	],
	retentionPeriod: [
		{required: true, message: '请选择保留年限', trigger: 'change'},
	],
	createDate: [
		{required: true, message: '请选择文件日期', trigger: 'blur'},
	],
	controlGroup: {
		id: [{required: true, message: '请输入全宗号', trigger: 'blur'},]
	},
	controlStatus: [
		{required: true, message: '请选择控制等级', trigger: 'change'},
	],
	office: {
		id: [{required: true, message: '请选择所属部门', trigger: 'change'},]
	},
	tagManagerInfo: {
		// id: [{required: true, message: '请选择档案标签', trigger: 'blur'},]
	},
	protectLevel: [
		{required: true, message: '请选择保密密级', trigger: 'change'},
	],
	org: {
		id: [{required: true, message: '请选择所属机构', trigger: 'change'},]
	}
})
//form 的model
const formLabelAlign = ref({})
// 档案信息
const archivesData = ref({
	tagManagerInfo: '',
    org:{
        id:''
    },
    office:{
        id:''
    }
});
//基本信息
const basicInfo = ref([]);
//数据信息
const otherDataInfo = ref([]);
//form表单label值
const dataConfigList = ref([]);
//是否显示form表单
const open = ref(true)
// tab标签页
const activeName = ref('first')
// 附件上传
const uploadUrl = process.env.VUE_APP_API_UPLOAD
//文件集合
const fileList = ref([])
// 接口请求头
const headers = {
	Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
	ContentType: 'multipart/form-data',
	clientType: "PC"
}
const videoUploadPercent = ref()
// 所属机构
const institution = ref([])
//标签信息集合
const tagsList = ref([])
// 已选择的标签
const selectedTags = ref([])
// 接收库List
const receiveData = ref([])
//文件路径
const fileUrl = ref([])
// 选择部门
const department = ref([])
// 可查看人员
const userByOrg = ref([]);

watch(() => props.addOpen, () => {
	activeName.value = 'first';
	archivesData.value = {
        tagManagerInfo: '',
        org:{
            id:''
        },
        office:{
            id:''
        }
    };
	fileUrl.value = [];
	fileList.value = [];
	selectedTags.value = []; // 清空已选择标签
	getList();
	institutionList();
    getTagsList();
}, {deep: true});

// 监听标签数据变化，用于编辑时的回显
watch(() => archivesData.value.tagManagerInfo, (newTagIds) => {
	if (newTagIds && typeof newTagIds === 'string') {
		selectedTags.value = newTagIds.split(',').filter(id => id.trim() !== '');
	}
}, { immediate: true });

//文件上传成功
function handleUploadSuccess(res, file, fileList) {
	if (res.code === 200) {
		fileList.value = [{...res.data}];
		fileUrl.value.push(fileList.value[0].url);
	}
}

function changeStatus(value) {
	if (value === "0") {
		archivesData.value.boxStatus = 2;
	} else if (value === "1") {
		archivesData.value.boxStatus = 1;
	}
}

// 获取新增数据结构
function getList() {
	collectList.getDataConfig({
		systemId: props?.chooseMenu?.id,
	}).then(res => {
		if (res.code === 200) {
			dataConfigList.value =  sortTreeNodes(res.data);
			open.value = false;
		} else {
			proxy.msgError('选择的分类, 暂未设置对应数据分类配置!');
			cancellation();
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}
/**
 * 递归去除隐藏字段
 * @param data
 * @returns {*}
 */
function sortTreeNodes(data) {
   // 首先对当前层级的节点进行排序
    data.sort((a, b) => a?.config?.sort - b?.config?.sort);
    // 过滤不显示的内容
    data = data.filter(item => item?.config?.isView !== '1');

    // 遍历当前层级的每个节点
    for (let node of data) {
        // 如果当前节点有子节点
        if (node.children) {
            if (Array.isArray(node.children) && !Array.isArray(node.children[0])) {
                // 如果子节点是一维数组
                node.children = sortTreeNodes(node.children);
            } else if (Array.isArray(node.children) && Array.isArray(node.children[0])) {
                // 如果子节点是二维数组
                node.children = node.children.map(childArray => sortTreeNodes(childArray));
            }
        }
    }

    return data;
}

//添加数据结构
function addOtherOneInfo(data) {
	let newArray = JSON.parse(JSON.stringify(data.detailChildren[0]));
	newArray.forEach(item => {
		item.id = '';
		item.detailValue = '';
	});
	data.detailChildren.push(newArray);
	console.log(data.detailChildren);
	console.log(data);
	proxy.msgSuccess('添加成功');
}

//删除数据结构
function removeOtherOneInfo(data, index) {
	dataConfigList.value = dataConfigList.value.filter(item => item !== data);
	proxy.msgSuccess('删除成功');
}

//标签选择回调方法
function choseTagMethodReturn(data) {
	archivesData.value.tagManagerInfo = data.join(',');
	openTagsChose.value = false;
}

// 删除标签
function removeTag(tagId) {
	// 从已选择标签中移除
	selectedTags.value = selectedTags.value.filter(tag => tag !== tagId);
	// 更新标签ID字符串
	const remainingTagIds = selectedTags.value.map(tag => tag);
	archivesData.value.tagManagerInfo = remainingTagIds.join(',');
}

// 判断主题
function judgeTheme(type) {
	if (type === '1') {
		return {label: '深色', type: 'dark'};
	} else if (type === '2') {
		return {label: '浅色', type: 'light'};
	} else {
		return {label: '默认', type: 'plain'};
	}
}

// 判断形状
function judgeShape(type) {
	if (type === '1') {
		return {label: '圆角', type: false};
	} else if (type === '2') {
		return {label: '椭圆', type: true};
	} else {
		return {label: '圆角', type: false};
	}
}

// 判断颜色
function judgeColor(type) {
	if (type === '1') {
		return {label: '蓝色', type: ""};
	} else if (type === '2') {
		return {label: '绿色', type: 'success'};
	} else if (type === '3') {
		return {label: '灰色', type: 'info'};
	} else if (type === '4') {
		return {label: '红色', type: 'danger'};
	} else if (type === '5') {
		return {label: '橙色', type: 'warning'};
	} else {
		return {label: '蓝色', type: ""};
	}
}

//查询信息
async function handleQuery(value) {
	collectList.list({
		current: 1,
		size: -1,
		namePrecise: value,
		year: archivesData.value.year ? moment(archivesData.value.year).format('YYYY') : null,
		month: archivesData.value.month ? moment(archivesData.value.month).format('MM') : null,
		delFlag: 0
	}).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 获取新增数据结构
function getTagsList() {
	tagsManagement.getList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			tagsList.value = res.data.records;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 根据组织机构查询部门
function departmentList(val) {
	if (val) {
		sysOfficeService.list({
			current: 1,
			size: -1,
			'sysOrg.id': val,
			'pparent.id': 0,
		}).then(res => {
			if (res.code === 200) {
				department.value = res.data.records
				archivesData.value = {
					...archivesData.value,
					office: {
						id: ''
					}
				};
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	} else {
		sysOfficeService.list({
			current: 1,
			size: -1,
			'sysOrg.id': archivesData?.value?.office?.id,
			'pparent.id': 0,
		}).then(res => {
			if (res.code === 200) {
				department.value = res.data.records
				archivesData.value = {
					...archivesData.value,
					office: {
						id: ''
					}
				};
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	}
}

// 查询机构信息
function institutionList() {
	sysOrgService.list({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			institution.value = res.data.records
			archivesData.value = {
				...archivesData.value,
				org: {
					id: ''
				}
			};
			loading.value = false;
		}
	}).catch((e) => {
		console.log(e);
		loading.value = false;
		proxy.msgError('查询失败');
	})
}

// 下一步
function nextStep() {
	if (activeName.value === 'first') {
		proxy.$refs["formRef"].validate(valid => {
			if (valid) {
				activeName.value = 'second';
			} else {
				proxy.msgError('请填写完毕当页数据后再点击下一步');
			}
		})
	} else if (activeName.value === 'second') {
		proxy.$refs["formBasic"].validate(valid => {
			if (valid) {
				activeName.value = 'three';
			} else {
				proxy.msgError('请填写完毕当页数据后再点击下一步');
			}
		})
	}
}

// 上一步
function nextBack() {
	if (activeName.value === 'second') {
		activeName.value = 'first';
	} else if (activeName.value === 'three') {
		activeName.value = 'second';
	}
}

function isBefore(nowName, oldName) {
	return new Promise((resolve, reject) => {
		if (oldName === 'first') {
			proxy.$refs["formRef"].validate(valid => {
				if (valid) {
					resolve();
				} else {
					proxy.msgError('请填写完毕当页数据后再切换');
					reject();
				}
			})
		} else {
			resolve();
		}
	});
}

//文件上传
function uploadVideoProcess(event) {
	videoUploadPercent.value = Math.floor(event.percent);
}
// 删除附件
function beforeRemove(file){
    fileUrl.value = fileUrl.value.filter(item=>item !== file?.response?.data?.url)
}

// 确定提交按钮
function collect() {
	loading.value = true;
	if (props.infoAddId.recordGroupId) {
		let data = {};
		data.name = archivesData.value?.name;
		data.digestContent = archivesData.value.digestContent;
		data.year = moment(archivesData.value.year).format('YYYY');
		data.month = moment(archivesData.value.month).format('MM');
		data.retentionPeriod = archivesData.value.retentionPeriod;
		data.protectLevel = archivesData.value.protectLevel;
		data.controlStatus = archivesData.value.controlStatus;
		data.remark = archivesData.value.remark;
		data.dataList = dataConfigList.value;
		data.fileList = fileUrl.value;
		data.systemId = props?.chooseMenu?.id;
		data.org = {
			id: archivesData.value?.org?.id
		}
		data.office = {
			id: archivesData.value?.office?.id
		}
		data.controlGroup = {
			id: props?.infoAddId?.recordGroupId
		}
		data.controlCategory = {
			id: props?.infoAddId?.id
		}
		data.tagManagerInfo = archivesData.value.tagManagerInfo;
		data.ruleUserId = archivesData.value.controlStatus === '4' ? archivesData.value.ruleUserId.join(',') : null;
		collectList.saveInfoData(data).then(res => {
			console.log(res.code === 200);
			if (res.code === 200) {
				proxy.msgSuccess('新增成功');
				cancellation();
			}
			loading.value = false;
		}).catch(() => {
			proxy.msgError('新增失败');
			loading.value = false;
		})
		// formLabelAlign.value = {}
	} else {
		loading.value = false;
		proxy.msgError('没有选择所属全宗, 请退出重新选择');
	}
}

//指定档案可查看人员
function controlGroupChange(chooseValue) {
	if (chooseValue === '4') {
		node.staff({"sysOrg.id": archivesData.value?.org?.id}).then((res) => {
			userByOrg.value = res.data.records;
		});
	}
}

// 取消
function cancellation() {
	emit("childEvent", "");
}
</script>

<style scoped>
.avatar-uploader .el-upload {
	border: 1px dashed var(--el-border-color);
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
	border-color: var(--el-color-primary);
}

.tag-selection-container {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.selected-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-top: 8px;
}

.selected-tag {
	margin: 0;
	transition: all 0.3s ease;
}

.selected-tag:hover {
	transform: scale(1.05);
}
</style>
