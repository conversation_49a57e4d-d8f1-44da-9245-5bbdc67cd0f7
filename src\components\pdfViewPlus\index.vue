<template>
	<el-dialog v-if="props.openStatus" v-model="props.openStatus" :close-on-click-modal="false" align-center
			   append-to-body destroy-on-close title="文件查看" width="82%" @close="closeDialog">
		<el-container style="height: 72vh;width: 100%">
			<el-aside width="20%">
				<div v-for="item in fileDataList"
					 :key="item"
					 class="fileUrl"
					 @click="handleViewFile(item.fileUrl)">
					<div style="display: flex;justify-content: space-between;">
						<el-tooltip :content="item.fileName"
									class="box-item"
									effect="dark"
									placement="right">
							<el-text style="cursor: pointer;" truncated>
								{{ item.fileName }}
							</el-text>
						</el-tooltip>
					</div>
				</div>
			</el-aside>
			<el-main style="background-color: #E4E7ED;padding: 5px">
				<div v-if="fileDataList.length > 0" ref="main" v-loading="pdfLoading" style="width: 100%;height: 100%">
					<VuePdfEmbed
						:source="pdfParams"
						text-layer
						annotation-layer
						@loaded="onLoaded"
						@loading-failed="loadingFailed"
						style="width: 100%;height: 100%"
					/>
				</div>
				<div v-else style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
							justify-content: center;align-items: center;">
					<el-result icon="info" title="温馨提醒">
						<template #sub-title>
							<p>此档案无相关附件</p>
						</template>
					</el-result>
				</div>
			</el-main>
		</el-container>
	</el-dialog>
</template>

<script setup>
import {ref, watch} from "vue";
import VuePdfEmbed from 'vue-pdf-embed';

const props = defineProps({
	openStatus: {
		required: true,
		type: Boolean,
		default: false
	},
	fileDataList: {
		type: Array,
		default: [],
	},
	fileDataStr: {
		type: String,
		default: '',
	},
});
const pdfLoading = ref(false);
const pdfParams = ref({
	cMapUrl: 'https://unpkg.com/pdfjs-dist@5.3.31/cmaps/',
	cMapPacked: true,
	url: ''
});
const emit = defineEmits(['closeFileBox']);

watch(() => props.openStatus, (newData) => {
	if (newData) {
		pdfLoading.value = true;
		pdfParams.value.url = props.fileDataList[0].fileUrl;
	}
}, {deep: true});

function handleViewFile(url) {
	if (pdfParams.value.url !== url) {
		pdfLoading.value = true;
		pdfParams.value.url = url;
	}
}

function onLoaded() {
	pdfLoading.value = false;
	console.log('加载完成')
}

function loadingFailed(data) {
	pdfLoading.value = false;
	console.log(data);
	console.log('加载失败')
}

function onError(data) {
	pdfLoading.value = false;
	console.log(data);
	console.log('加载失败')
}

function closeDialog() {
	emit("closeFileBox");
}
</script>

<style scoped>
.fileUrl {
	cursor: pointer;
	border-top: 1px solid #E4E7ED;
	border-left: 1px solid #E4E7ED;
	border-bottom: 1px solid #E4E7ED;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

.fileUrl:hover {
	cursor: pointer;
	border-top: 1px solid #2a76f8;
	border-left: 1px solid #2a76f8;
	border-right: 1px solid #2a76f8;
	border-bottom: 1px solid #2a76f8;
	padding: 10px 5px 10px 10px;
	border-radius: 5px 0 0 5px;
}

:deep(.el-scrollbar__view) {
	width: 100%;
	height: 100%;
}
</style>
