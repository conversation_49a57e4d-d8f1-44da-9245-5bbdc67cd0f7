import http from "@/utils/request"

export default {
	getInfoByMasterId: function (data) {
		return http.post(
			'/archive/info/data/getInfoByMasterId',
			data
		)
	},
	getDataById: function (params) {
		return http.get(
			'/archive/info/data/getDataById',
			params
		)
	},
	getDataByMasterId: function (params) {
		return http.get(
			'/archive/info/data/getDataById',
			params
		)
	},
	fileFist: function (params) {
		return http.get(
			'/archive/original/file/list',
			params
		)
	},// 查询档案文件信息表列表数据
	fileList: function (params) {
		return http.get(
			'/archive/info/file/list',
			params
		)
	},
    //根据Id获取档案信息数据
    queryById: function (params) {
        return http.get(
            '/archive/info/main/queryById',
            params
        )
    },
}
