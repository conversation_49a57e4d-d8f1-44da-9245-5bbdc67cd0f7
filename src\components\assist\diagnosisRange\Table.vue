<template>
	<div>
		<el-table
			:data="props.data.tableData"
			:load="load"
			:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
			border
			lazy
			row-key="id"
			style="width: 100%"
		>
			<el-table-column
				label="范围名称"
				min-width="250px"
				prop="treatmentName"
			/>
			<el-table-column
				align="center"
				label="范围编号"
				min-width="150px"
				prop="treatmentNo"
			/>
			<el-table-column align="center" label="状态" prop="isStop">
				<template #default="scope">
					{{ scope.row.isStop == 1 ? "正常" : "停用" }}
				</template>
			</el-table-column>
			<el-table-column
				align="center"
				label="备注"
				min-width="150px"
				prop="remark"
			/>
			<el-table-column
				align="center"
				label="操作"
				prop="address"
				width="283px"
			>
				<template #default="scope">
					<el-button
						:icon="CirclePlus"
						text
						type="primary"
						@click="newAdd(scope.row)"
					>新增
					</el-button
					>
					<el-button
						icon="el-icon-edit"
						text
						type="success"
						@click="editData(scope.row)"
					>编辑
					</el-button
					>
					<el-button
						icon="el-icon-delete"
						text
						type="danger"
						@click="delData(scope.row)"
					>删除
					</el-button
					>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, toRefs, watchEffect,} from "vue";
import {useStore} from "vuex";
import {useRoute, useRouter} from "vue-router";
import {CirclePlus} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import {diagnosisRange} from "@/api/model/assist/diagnosisRange/index";

const props = defineProps({
	data: Object,
});
const emits = defineEmits(["newAdd", "editData", "delData", "detailsBtn"]);
const newAdd = (row) => {
	emits("newAdd", row);
};
const editData = (row) => {
	emits("editData", row);
};

const delData = (row) => {
	emits("delData", row);
};
const load = (row, treeNode, resolve) => {
	diagnosisRange
		.diagnosisList({
			"parent.id": row.id,
		})
		.then((res) => {
			if (res.code == 200 && res.data.records.length > 0) {
				let newData = res.data.records;
				newData.forEach((record) => {
					record.hasChildren = true;
				});
				resolve(newData);
			} else {
				resolve([]);
				ElMessage({
					type: "warning",
					message: "无数据",
				});
			}
		});
};

const tableRowClassName = ({row, rowIndex}) => {
	row.index = rowIndex;
};
const detailsBtn = (row) => {
	emits("detailsBtn", row);
};
/**
 * 仓库
 */
const store = useStore();
/**
 * 路由对象
 */
const route = useRoute();
/**
 * 路由实例
 */
const router = useRouter();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({
	anIndex: null,
});
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	...toRefs(data),
});
</script>
<style lang="less" scoped></style>
