/**
 * v-clickOutside directive
 * Detects clicks outside of the element and calls the provided handler
 * Usage: v-clickOutside="handler" or v-clickOutside="($event) => handler($event)"
 */

const clickOutside = {
  mounted(el, binding) {
    // Store the click handler function
    el._clickOutsideHandler = function(event) {
      // Check if the click was outside the element
      if (!(el === event.target || el.contains(event.target))) {
        // Call the provided handler function
        if (typeof binding.value === 'function') {
          binding.value(event);
        }
      }
    };

    // Add event listener to document with capture phase to ensure it runs before other handlers
    document.addEventListener('click', el._clickOutsideHandler, true);
  },

  beforeUnmount(el) {
    // Clean up event listener when component is unmounted
    if (el._clickOutsideHandler) {
      document.removeEventListener('click', el._clickOutsideHandler, true);
      delete el._clickOutsideHandler;
    }
  },

  updated(el, binding) {
    // Update the handler if the binding value changes
    if (el._clickOutsideHandler) {
      document.removeEventListener('click', el._clickOutsideHandler, true);
    }

    el._clickOutsideHandler = function(event) {
      if (!(el === event.target || el.contains(event.target))) {
        if (typeof binding.value === 'function') {
          binding.value(event);
        }
      }
    };

    document.addEventListener('click', el._clickOutsideHandler, true);
  }
};

export default clickOutside;
