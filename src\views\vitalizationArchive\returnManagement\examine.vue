<template>
    <el-descriptions :column="3" border style="margin-bottom: 20px">
        <el-descriptions-item label="借阅人" width="80">
            {{ descriptionsLabel.borrowApply.name}}
        </el-descriptions-item>
        <el-descriptions-item label="借阅部门" width="80">
            {{ descriptionsLabel.borrowApply.sysOffice.name }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅起始时间" width="80">
            {{ moment(descriptionsLabel.borrowStartTime).format('YYYY-MM-DD HH:mm:ss') }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅结束时间" width="80">
            {{ moment(descriptionsLabel.borrowEndTime).format('YYYY-MM-DD HH:mm:ss') }}
        </el-descriptions-item>
        <el-descriptions-item label="是否包含水印" width="80">
            {{ descriptionsLabel.borrowIsWatermark == '1' ? '是' : '否' }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅理由" width="80">
            {{ descriptionsLabel.borrowRemark }}
        </el-descriptions-item>
    </el-descriptions>
    <el-table :data="controlList" border @selection-change="handleSelectionChange" >
        <el-table-column align="center" label="序号" prop="sort" width="80">
            <template #default="scope">
                {{ scope.$index + 1 }}
            </template>
        </el-table-column>
        <el-table-column align="left" label="档案名称" prop="detailsInfo.name" />
        <el-table-column align="left" label="档案号" prop="detailsInfo.num" />
        <el-table-column align="left" label="档案门类" prop="detailsInfo.controlCategory.name" width="120" />
        <el-table-column align="left" label="借阅人" prop="detailsBorrow.borrowApply.name" width="120" />
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="80px">
            <template #default="scope">
				<el-button v-if="moment(scope.row.detailsBorrow.borrowEndTime).format('YYYY-MM-DD HH:mm:ss') >= moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
							&& descriptionsLabel.borrowStatus === '1'" link type="primary"
						   @click="collectFile(scope.row)">
					查看
				</el-button>
				<el-tooltip
					v-else-if="descriptionsLabel.borrowStatus === '2'"
					content="您的借阅已经归还无法查看"
					effect="light"
					placement="top"
				>
					<el-button :disabled="descriptionsLabel.borrowStatus === '2'" link type="primary"
							   @click="collectFile(scope.row)">
						查看
					</el-button>
				</el-tooltip>
				<el-tooltip
					v-else
					content="您的借阅已经过期, 请及时归还, 或进行续借操作"
					effect="light"
					placement="top"
				>
					<el-button :disabled="moment(scope.row.detailsBorrow.borrowEndTime).format('YYYY-MM-DD HH:mm:ss') < moment(new Date()).format('YYYY-MM-DD HH:mm:ss')" link type="primary"
							   @click="collectFile(scope.row)">
						查看
					</el-button>
				</el-tooltip>
			</template>
        </el-table-column>
    </el-table>
	<el-main>
		<el-container>
			<el-main>
				<div style="float: right;">
					<!--                    <el-button plain @click="() => cancellation()">取消</el-button>-->
					<!--                    <el-button type="primary" @click="rightOrder()">确认</el-button>-->
				</div>
			</el-main>
		</el-container>
	</el-main>
	<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
		<viewFiles :isMark="descriptionsLabel.borrowIsWatermark === '是'" :receiveId="receiveId"
				   @childMove="parentView"></viewFiles>
	</el-dialog>
</template>
<script setup>
import vitalizationArchiveList from '@/api/archive/vitalizationArchive'
import {defineProps, getCurrentInstance, onMounted, ref} from 'vue'
// 查看弹窗
import viewFiles from '@/views/archiveReception/view.vue';
import moment from 'moment'

const { proxy } = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const props = defineProps({
    receiveId: {
        type: String
    },
})
// 接收库List
const descriptionsLabel = ref([])

// 取消
function cancellation() {
    emit("childMove");
}

const title = ref('')
// 查看receiveId
const receiveId = ref('')
const openView = ref(false)

function collectFile(val) {
    title.value = '查看';
    receiveId.value = val.detailsInfo;
    openView.value = true;
}
// 关闭查看
function parentView() {
    openView.value = false;
}

// 查询原数据
function getList() {
    descriptionsLabel.value = props.receiveId;
}
// 查询审批档案
const controlList = ref([]);
function getControl() {
    vitalizationArchiveList.list({
        'detailsBorrow.id': props.receiveId.id,
        size: '-1',
    }).then(res => {
        if (res.code === 200) {
            controlList.value = res.data.records
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

onMounted(() => {
    getControl();
    getList();
});
</script>
<style scoped>
.fileUrl {
    margin-bottom: 10px;
    cursor: pointer;
}

.fileUrl:hover {
    color: #2a76f8;
}

p {
    /* width: 250px;
    white-space: nowrap; */
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
