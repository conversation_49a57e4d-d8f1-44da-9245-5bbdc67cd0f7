import http from "@/utils/request"

export default {
	getReviewTotal: function (inputForm) {
		return http.get('/archive/audit/currentAuditNode/countNowData', inputForm)
	},
	// 档案接收
	countByTime: function (params) {
		return http.get(
			'/archive/original/info/countByTime',
			params
		)
	},
	// 档案 归档1 入库2
	infoTime: function (params) {
		return http.get(
			'/archive/info/main/countByTime',
			params
		)
	},
	//根据时间筛选排行数据
	rankingCountByTime: function (params) {
		return http.get(
			'/archive/info/main/rankingCountByTime',
			params
		)
	},
	// 借阅
	borrowTime: function (params) {
		return http.get(
			'/archive/borrow/info/countByTime',
			params
		)
	},
	// 归还
	expireInfo: function (params) {
		return http.get(
			'/archive/borrow/info/expireInfo',
			params
		)
	},
	// 归还
	getHomePageData: function (params) {
		return http.get(
			'/archive/info/control/getHomePageData',
			params
		)
	},
}
