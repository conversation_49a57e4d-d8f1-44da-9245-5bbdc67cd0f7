<template>
    <el-table :data="receiveData">
        <el-table-column label="档案名称" align="left" prop="name" />
        <el-table-column label="档案号" align="left" prop="num" />
        <el-table-column label="档案年份" align="left" prop="year" width="100"/>
        <el-table-column label="档案月份" align="left" prop="month" width="100"/>
        <el-table-column label="档案创建时间" align="left" prop="createDate" width="180"
						 :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD HH:mm:ss')"/>
    </el-table>
    <!-- <div style="float: right;">
        <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
            @pagination="getRelevanceFileList()" />
    </div> -->
</template>
<script setup>
import receiveList from "@/api/archive/archiveReception/receive"
import { ref, getCurrentInstance, reactive, toRefs } from 'vue'
import moment from "moment/moment";
const { proxy } = getCurrentInstance()
// const data = reactive({
//     queryParams: {
//         current: 1,
//         size: 10,
//     }
// })
const total = ref(0)
// const { queryParams } = toRefs(data)
// List详情
const receiveData = ref([])
const props = defineProps({
    relevanceList: {
        type: Array
    }
})
function getRelevanceFileList() {
    receiveList.getRelevanceFileList({
        // current: queryParams.value.current,
        // size: queryParams.value.size,
        id: props.relevanceList.id
    }).then(res => {
        if (res.code === 200) {
            receiveData.value = res.data;
            total.value = res.data.total;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

getRelevanceFileList();
</script>
