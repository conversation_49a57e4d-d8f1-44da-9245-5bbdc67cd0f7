import http from "@/utils/request"
//系统配置数据结构管理配置
export default {
	infoList: function (params) {
		return http.get(
			'/archive/config/info/list',
			params
		)
	},
	versionList: function (params) {
		return http.get(
			'/archive/config/info/versionList',
			params
		)
	},
	infoSave: function (data) {
		return http.post(
			'/archive/config/info/save',
			data
		)
	},
	infoDelete: function (params) {
		return http.delete(
			'/archive/config/info/delete',
			params
		)
	},
	list: function (params) {
		return http.get(
			'/archive/config/dataConfig/treeData',
			params
		)
	},
	getList: function (inputForm) {
		return http.get(
			'/archive/config/dataConfig/details/list',
			inputForm
		)
	},
	save: function (data) {
		return http.post(
			'/archive/config/dataConfig/save',
			data
		)
	},
	saveDetails: function (data) {
		return http.post(
			'/archive/config/dataConfig/details/save',
			data
		)
	},
	queryById: function (params) {
		return http.get(
			'/archive/config/dataConfig/queryById',
			params
		)
	},
	queryDetailsById: function (params) {
		return http.get(
			'/archive/config/dataConfig/details/queryById',
			params
		)
	},
	delete: function (params) {
		return http.delete(
			'/archive/config/dataConfig/delete',
			params
		)
	},
	deleteDetailsById: function (params) {
		return http.delete(
			'/archive/config/dataConfig/details/delete',
			params
		)
	},
}
