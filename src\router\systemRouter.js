/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:11
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-05-09 11:08:28
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\router\systemRouter.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import config from "@/config"

//系统路由
const routes = [
	{
		name: "layout",
		path: "/",
		component: () => import(/* webpackChunkName: "layout" */ '@/layout'),
		redirect: config.DASHBOARD_URL || '/home',
		children: []
	},
	{
		path: "/login",
		component: () => import(/* webpackChunkName: "login" */ '@/views/login'),
		meta: {
			title: "登录"
		}
	},
	{
		path: "/user_register",
		component: () => import(/* webpackChunkName: "userRegister" */ '@/views/login/userRegister'),
		meta: {
			title: "注册"
		}
	},
	{
		path: "/searchSystem",
		component: () => import(/* webpackChunkName: "userRegister" */ '@/views/home/<USER>'),
		meta: {
			title: "档案查询"
		}
	},
	// {
	// 	path: "/reset_password",
	// 	component: () => import(/* webpackChunkName: "resetPassword" */ '@/views/login/resetPassword'),
	// 	meta: {
	// 		title: "重置密码"
	// 	}
	// }
]

export default routes;
