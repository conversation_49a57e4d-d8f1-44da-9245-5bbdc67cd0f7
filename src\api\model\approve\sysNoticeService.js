import http from "@/utils/request"

export default {


  queryById: function (id) {
    return http.get('/archive/audit/auditRecordqueryById',id)
  },

  list: function (params) {
    //return http.get('/erp/audit/auditRecord/list',params)
	  return http.get('/archive/audit/currentAuditNode/list',params)
  },
	listRecode: function (params) {
		//return http.get('/erp/audit/auditRecord/list',params)
		return http.get('/archive/audit/auditRecord/list',params)
	},

  handbatch: function ({ ids, status,idea }) {
    return http.get(`/archive/audit/auditApi/auditInfoProcess?ids=${ids}&idea=${idea}&status=${status}`)
  },

  importExcel: function (data) {
    return http.post( '/sys/notice/import', data)
  },
  release:function ({id,status}) {
    return http.get( `/sys/notice/updateStatusById?id=${id}&status=${status}`)
  },
}
