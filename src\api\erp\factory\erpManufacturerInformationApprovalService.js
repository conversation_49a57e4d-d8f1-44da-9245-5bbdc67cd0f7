import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/factory/erpManufacturerInformationApproval/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/factory/erpManufacturerInformationApproval/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/factory/erpManufacturerInformationApproval/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerInformationApproval/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/factory/erpManufacturerInformationApproval/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/factory/erpManufacturerInformationApproval/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/factory/erpManufacturerInformationApproval/import',
      data
    )
  }
}
