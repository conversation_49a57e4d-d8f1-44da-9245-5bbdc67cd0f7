<template>
	<el-dialog
		v-model="visible"
		title="门类菜单选择"
		top="2vh"
		width="30%"
		@closed="$emit('closed')"
	>
		<el-container v-loading="listLoading" style="cursor: pointer; height: 80vh">
			<el-main
				style="
				  	padding: 0;
				  	border: 1px solid var(--el-border-color-light);
				  	display: flex;
				  	justify-content: space-between;
				  	align-items: stretch;
				  	flex-direction: column;
				  	flex-wrap: nowrap;
				"
			>
				<el-header>
					<div class="right-panel-search">
						<el-input
							v-model="filterText"
							placeholder="输入关键字进行过滤"
							style="width: 220px"
							@input="input_change"
						>
						</el-input>
					</div>
				</el-header>
				<el-main class="noPadding" style="height: 360px">
					<right-menu ref="right_menu" @onMenuClick="onMenuClick"/>
					<el-tree
						ref="tree"
						:data="dataList"
						:default-checked-keys="selects"
						:filter-node-method="filterNode"
						:props="menuProps"
						check-strictly
						class="filter-tree"
						default-expand-all
						highlight-current
						node-key="id"
						show-checkbox
						@check="handleCheck"
						@node-contextmenu="right_btn"
						@node-click="menuNodeClick"
					>
					</el-tree>
					<div class="buttons">
						<!-- <el-button @click="resetChecked">清空</el-button> -->
					</div>
				</el-main>
			</el-main>
		</el-container>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" @click="submitComplet()">确 定</el-button>
		</template>
	</el-dialog>
</template>

<script>
import sysDataRulesService from "@/api/model/sys/sysDataRulesService";
import sysRoleDataruleService from "@/api/model/sys/sysRoleDataruleService";
import RightMenu from "@/components/system/sysMenuSelect/rightMenu.vue";
import completeManagement from "@/api/archive/systemConfiguration/completeManagement";
import category from "@/api/archive/categoryManagement/category";

export default {
	components: {
		RightMenu,
	},
	props: {
		//是否多选
		isMultiple: {type: Boolean, default: false},
		//回调函数
		selectChange: {type: Function},
		//当前角色
		orgId: {type: String, default: ""},
	},
	data() {
		return {
			//数据列表
			dataList: [],
			filterText: "",
			menuProps: {
				children: "children",
				label: "name",
			},
			//查询表单
			searchForm: {},
			//数据列选中行
			selection: [],
			//列表加载
			listLoading: false,
			//已选择的数据
			selectData: [],
			selects: [],
			visible: true,
			//当前角色
			role: {},
			//当前菜单
			menu: {},

			//菜单权限列表
			dataRuleList: [],
			dataRuleLoading: false,
		};
	},
	mounted() {
		setTimeout(() => {
			//刷新数据列表
			this.getDataList();
		}, 0);
	},
	methods: {
		// 新增处理选中事件的方法
		handleCheck(checkedNode, {checkedKeys}) {
			if (checkedKeys.includes(checkedNode.id)) {
				let currentNode = this.$refs.tree.getNode(checkedNode);
				this.checkParentNodes(currentNode);
			}
		},

		// 递归选中父节点
		checkParentNodes(node) {
			if (node.parent && node.parent.data.id !== undefined) {
				this.$refs.tree.setChecked(node.parent, true, false);
				this.checkParentNodes(node.parent);
			}
		},

		/*
		 * 刷新数据列表
		 * @author: 路正宁
		 * @date: 2023-03-24 13:13:35
		 */
		async getDataList() {
			//初始化数据列表
			this.dataList = [];
			//请求接口
			this.listLoading = true;
			completeManagement
				.getList({
					current: 1,
					size: -1,
				})
				.then(async (result) => {
					if (result.code === 200) {
						let treeData = result.data;
						const promises = treeData.records.map(
							async (treeDataForLoop, index) => {
								try {
									const res = await category.groupList({
										current: 1,
										size: -1,
										groupId: treeDataForLoop.id,
										isAut: '0'  //是否门类权限查询，1：是，0：否
									});
									if (res.code === 200) {
										treeDataForLoop.children = res.data;
										treeDataForLoop.name = treeDataForLoop.recordGroupName;
										treeDataForLoop.isGroup = true;
									} else {
										this.$Response.errorNotice(null, "全宗下门类数据查询失败");
									}
								} catch (error) {
									proxy.msgError(`全宗${index + 1}下的门类数据查询失败`);
								}
							}
						);
						await Promise.all(promises);
						this.dataList = treeData.records;
						this.dataList = this.recursiveSort(this.menuList, "sort");
					} else {
						this.$Response.errorNotice(null, result.msg);
					}
				})
				.catch((error) => {
					//this.$Response.errorNotice(null, "系统菜单数据查询失败");
				})
				.finally(() => {
					this.listLoading = false;
				});
		},

		/*
		 * 选中的数据
		 * @author: 路正宁
		 * @date: 2023-03-31 17:26:29
		 */
		selecteds(role, menuIds) {
			//选中指定的系统菜单
			this.selects = menuIds;
			this.role = role;
		},
		/*
		 * 提交选择结果
		 * @author: 路正宁
		 * @date: 2023-04-03 09:55:23
		 */
		async submitComplet() {
			const param = {
				roleId: this.role.id,
				menuIds: this.$refs.tree.getCheckedKeys().join(","),
			};
			const res = await category.configMenuList(param);
			this.visible = false;
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		input_change() {
			this.$refs.tree.filter(this.filterText);
		},
		right_btn(e, data) {
			this.$refs.right_menu.onButtonClick(e, data);
		},
		onMenuClick(ind, data) {
			this.$refs.tree.setChecked(data, ind === 1);
			if (ind === 1) {
				const node = this.$refs.tree.getNode(data);
				this.checkParentNodes(node);
			}
			if (data.children) {
				data.children.forEach((v) => {
					this.onMenuClick(ind, v);
				});
			}
		},
		/*
		 * 系统菜单点击事件
		 * @author: 路正宁
		 * @date: 2023-04-04 10:30:34
		 */
		async menuNodeClick(row, node, rootNode) {
			//初始化数据列表
			this.dataRuleList = [];
			this.menu = row;
			//请求接口
			this.dataRuleLoading = true;
			let res = await sysDataRulesService.list({
				"categoryMenu.id": row.id,
				//当前页码
				current: 1,
				//每页条数
				size: 1000,
			});

			if (res.code == 200) {
				//数据列表
				this.dataRuleList = res.data.records;
			} else {
				this.$Response.errorNotice(res, "查询失败");
				return;
			}
			//角色数据权限查询
			let result = await sysRoleDataruleService.list({
				"sysRole.id": this.role.id,
				menuId: row.id,
			});
			if (result.code != 200) {
				this.$Response.errorNotice(res, "查询失败");
				return;
			}
			this.dataRuleLoading = false;
			//遍历数据权限列表
			this.dataRuleList.forEach((item) => {
				//选中已配置的数据权限
				for (let j = 0; j < result.data.records.length; j++) {
					if (result.data.records[j].sysDataRules.id == item.id) {
						this.$nextTick(() => {
							this.$refs.dataRuleTable.toggleRowSelection(item);
						});
					}
				}
			});
		},
		/*
		 * 数据权限勾选事件
		 * @author: 路正宁
		 * @date: 2023-04-04 13:27:13
		 */
		async dataRuleSelectClick(selection, row) {
			let ruleData = [];
			for (let j = 0; j < selection.length; j++) {
				ruleData[j] = selection[j].id;
			}
			//数据权限ID
			let ruleDataIds = ruleData.join(",");
			//角色
			let roleId = this.role.id;
			//菜单id
			let menuId = this.menu.id;
			this.dataRuleLoading = true;
			let result = await sysRoleDataruleService.configDataRule(
				roleId,
				menuId,
				ruleDataIds
			);
			if (result.code == 200) {
				this.$message.success("配置成功");
			} else {
				this.$Response.errorNotice(result, "查询失败");
				return;
			}
			this.dataRuleLoading = false;
		},
	},
};
</script>

<style scoped>
.el-dialog__body {
	padding: 10px 20px;
}
</style>
