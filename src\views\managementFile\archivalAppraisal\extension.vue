<template>
    <el-container>
        <el-main v-if="open" ref="main" class="noPadding" style="">
			<el-form ref="formRef" :inline="true" :model="formBox" :rules="rules" label-width="80px">
				<el-form-item label="申请人:" prop="controlApply" style="width: 46%;">
					<el-input v-model="formBox.controlApply" disabled placeholder="请输入申请人"/>
				</el-form-item>
				<el-form-item label="申请部门:" prop="department" style="width: 46%;">
					<el-input v-model="formBox.department" disabled placeholder="请输入申请部门"/>
				</el-form-item>
				<el-form-item label="申请时间:" prop="applyDate" style="width: 46%;">
					<el-date-picker v-model="formBox.applyDate" disabled format="YYYY-MM-DD HH:mm:ss"
									placeholder="请选择申请时间" style="width: 100%"
									type="datetime" value-format="YYYY-MM-DD HH:mm:ss"/>
				</el-form-item>
				<el-form-item label="延期时间:" prop="retentionPeriod" style="width: 46%">
					<el-select v-model="formBox.retentionPeriod" placeholder="请选择延期时间" style="width: 100%;">
						<el-option label="永久" value="Y"/>
						<el-option label="5年" value="D5"/>
						<el-option label="10年" value="D10"/>
						<el-option label="20年" value="D20"/>
						<el-option label="30年" value="D30"/>
					</el-select>
				</el-form-item>
				<el-form-item label="申请理由:" prop="controlRemark" style="width: 95.5%;">
					<el-input v-model="formBox.controlRemark" placeholder="请输入申请理由" type="textarea"/>
				</el-form-item>
			</el-form>
            <div>
                <p style="font-size: 16px; font-weight: 600;color: black;margin-bottom: 10px;">档案鉴定清单</p>
            </div>
			<el-table :data="props.handList">
				<el-table-column align="center" label="序号" prop="sort" width="80">
					<template #default="scope">
						{{ scope.$index + 1 }}
					</template>
				</el-table-column>
				<el-table-column align="left" label="档案名称" prop="name"/>
				<el-table-column align="center" label="档案号" prop="num"/>
				<el-table-column align="center" label="保留截止日期" prop="expirationData" width="150">
					<template #default="scope">
						{{ moment(scope.row.expirationData).format('YYYY-MM-DD HH:mm:ss') }}
					</template>
				</el-table-column>
				<el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="80px">
					<template #default="scope">
						<el-button link type="primary" @click="collectFile(scope.row)">查看
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="float: right;margin-top: 42px;margin-bottom: 12px">
				<el-button plain @click="() => cancellation()">取消</el-button>
				<el-button type="primary" @click="() => determine()">确定</el-button>
			</div>
			<!-- 查看 -->
			<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
				<viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
			</el-dialog>
		</el-main>
    </el-container>
</template>

<script setup>
import {defineProps, getCurrentInstance, onMounted, ref} from 'vue'
import tool from "@/utils/tool";
import moment from "moment";
// 根据id查询全宗名称
import completeManagement from '@/api/archive/systemConfiguration/completeManagement'
import archivalAppraisal from '@/api/archive/managementFile/archivalAppraisal'
// 查看弹窗
import viewFiles from '../view.vue';

const { proxy } = getCurrentInstance()
const emit = defineEmits(["childEvent"]);
const props = defineProps({
    handList: {
        type: Array
    },
    clickEvenId: {
        type: Array
    },
})
//标题
const title = ref('')
// 查看receiveId
const receiveId = ref('')
const openView = ref(false)
// 新增表单
const formBox = ref({})
// 是否显示清单
const open = ref(true)
// 全宗ID
const groupId = ref('')
// 门类ID
const categoryId = ref('')

onMounted(() => {
	getUserInfo()
	// getGroupNum();
	// getCategory();
});

// 查看档案
function collectFile(val) {
	title.value = '查看';
	receiveId.value = val;
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 确定group
function determine() {
	let ids = props.handList.map((v) => v.id);
	//拼接的数组字符串，接口传参
	let idStr = ids.join(",");
	archivalAppraisal.submitRecordInfoCheck({
		"recordInfoIds": idStr,
		"controlApply": {
			id: tool.data.get("USER_INFO").id
        },
        "controlType": '1',
        "controlContext": JSON.stringify({
            delayTime: formBox.value.retentionPeriod
        }),
		"controlRemark": formBox.value.controlRemark

    }).then(res => {
        if (res.code === 200) {
            proxy.msgSuccess('鉴定延期成功');
            emit("childMove");
        }
    }).catch(() => {
        proxy.msgError('鉴定延期失败!');
    })
}

// 取消
function cancellation() {
    emit("childMove");
}

// 进入时查询全宗
function getGroupNum() {
    if (props.clickEvenId.recordGroupNum) {
        completeManagement.queryById({
            id: props.clickEvenId.id
        }).then(res => {
            if (res.code === 200) {
                groupId.value = res.data.id;
                formBox.value.controlGroup = res.data.recordGroupNum
            }
        }).catch(() => {
            proxy.msgError('全宗回显失败;请选择全宗!');
        })
    } else {
        completeManagement.queryById({
            id: props.clickEvenId.recordGroupId
        }).then(res => {
            if (res.code === 200) {
                groupId.value = res.data.id;
                formBox.value.controlGroup = res.data.recordGroupNum
            }
        }).catch(() => {
            proxy.msgError('全宗回显失败;请选择全宗!');
        })
    }

}
// 进入时回显门类
function getCategory() {
    if (props.clickEvenId.name && !props.clickEvenId.recordGroupNum) {
        formBox.value.controlCategory = props.clickEvenId.name;
        categoryId.value = props.clickEvenId.id;
    } else {
        formBox.value.controlCategory = '暂无门类'
    }
}
// 获取当前登录人
function getUserInfo() {
	formBox.value.controlApply = tool.data.get("USER_INFO").name;
	formBox.value.department = tool.data.get("USER_INFO").sysOffice.name;
	formBox.value.applyDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
}
</script>

<style scoped></style>
