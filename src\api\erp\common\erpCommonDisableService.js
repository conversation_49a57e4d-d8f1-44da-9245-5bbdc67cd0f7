import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/common/erpCommonDisable/save',
      inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
      '/erp/common/erpCommonDisable/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/common/erpCommonDisable/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/common/erpCommonDisable/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/common/erpCommonDisable/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/common/erpCommonDisable/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/common/erpCommonDisable/import',
      data
    )
  }
}
